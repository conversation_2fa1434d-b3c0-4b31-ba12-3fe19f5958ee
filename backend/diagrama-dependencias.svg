<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN"
 "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<!-- Generated by graphviz version 12.2.1 (20241206.2353)
 -->
<!-- Title: G Pages: 1 -->
<svg width="1772pt" height="323pt"
 viewBox="0.00 0.00 1772.45 323.20" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
<g id="graph0" class="graph" transform="scale(1 1) rotate(0) translate(21.6 301.6)">
<title>G</title>
<polygon fill="#111111" stroke="none" points="-21.6,21.6 -21.6,-301.6 1750.85,-301.6 1750.85,21.6 -21.6,21.6"/>
<!-- index.ts -->
<g id="node1" class="node">
<title>index.ts</title>
<path fill="none" stroke="#c6c5fe" d="M54.58,-120.75C54.58,-120.75 7.92,-120.75 7.92,-120.75 3.96,-120.75 0,-116.79 0,-112.83 0,-112.83 0,-104.92 0,-104.92 0,-100.96 3.96,-97 7.92,-97 7.92,-97 54.58,-97 54.58,-97 58.54,-97 62.5,-100.96 62.5,-104.92 62.5,-104.92 62.5,-112.83 62.5,-112.83 62.5,-116.79 58.54,-120.75 54.58,-120.75"/>
<text text-anchor="middle" x="31.25" y="-103.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">index.ts</text>
</g>
<!-- server/server.ts -->
<g id="node2" class="node">
<title>server/server.ts</title>
<path fill="none" stroke="#c6c5fe" d="M198.83,-120.75C198.83,-120.75 106.42,-120.75 106.42,-120.75 102.46,-120.75 98.5,-116.79 98.5,-112.83 98.5,-112.83 98.5,-104.92 98.5,-104.92 98.5,-100.96 102.46,-97 106.42,-97 106.42,-97 198.83,-97 198.83,-97 202.79,-97 206.75,-100.96 206.75,-104.92 206.75,-104.92 206.75,-112.83 206.75,-112.83 206.75,-116.79 202.79,-120.75 198.83,-120.75"/>
<text text-anchor="middle" x="152.62" y="-103.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">server/server.ts</text>
</g>
<!-- index.ts&#45;&gt;server/server.ts -->
<g id="edge1" class="edge">
<title>index.ts&#45;&gt;server/server.ts</title>
<path fill="none" stroke="#757575" d="M62.99,-108.88C70.4,-108.88 78.6,-108.88 86.92,-108.88"/>
<polygon fill="#757575" stroke="#757575" points="86.78,-112.38 96.78,-108.88 86.78,-105.38 86.78,-112.38"/>
</g>
<!-- minsa/infrastructure/minsaRouter.ts -->
<g id="node7" class="node">
<title>minsa/infrastructure/minsaRouter.ts</title>
<path fill="none" stroke="#c6c5fe" d="M464.58,-141.75C464.58,-141.75 250.67,-141.75 250.67,-141.75 246.71,-141.75 242.75,-137.79 242.75,-133.83 242.75,-133.83 242.75,-125.92 242.75,-125.92 242.75,-121.96 246.71,-118 250.67,-118 250.67,-118 464.58,-118 464.58,-118 468.54,-118 472.5,-121.96 472.5,-125.92 472.5,-125.92 472.5,-133.83 472.5,-133.83 472.5,-137.79 468.54,-141.75 464.58,-141.75"/>
<text text-anchor="middle" x="357.62" y="-124.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">minsa/infrastructure/minsaRouter.ts</text>
</g>
<!-- server/server.ts&#45;&gt;minsa/infrastructure/minsaRouter.ts -->
<g id="edge14" class="edge">
<title>server/server.ts&#45;&gt;minsa/infrastructure/minsaRouter.ts</title>
<path fill="none" stroke="#757575" d="M207.23,-114.42C214.82,-115.21 222.87,-116.04 231.15,-116.9"/>
<polygon fill="#757575" stroke="#757575" points="230.75,-120.37 241.06,-117.92 231.47,-113.41 230.75,-120.37"/>
</g>
<!-- user/userRouter.ts -->
<g id="node13" class="node">
<title>user/userRouter.ts</title>
<path fill="none" stroke="#c6c5fe" d="M412.83,-99.75C412.83,-99.75 302.42,-99.75 302.42,-99.75 298.46,-99.75 294.5,-95.79 294.5,-91.83 294.5,-91.83 294.5,-83.92 294.5,-83.92 294.5,-79.96 298.46,-76 302.42,-76 302.42,-76 412.83,-76 412.83,-76 416.79,-76 420.75,-79.96 420.75,-83.92 420.75,-83.92 420.75,-91.83 420.75,-91.83 420.75,-95.79 416.79,-99.75 412.83,-99.75"/>
<text text-anchor="middle" x="357.62" y="-82.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">user/userRouter.ts</text>
</g>
<!-- server/server.ts&#45;&gt;user/userRouter.ts -->
<g id="edge15" class="edge">
<title>server/server.ts&#45;&gt;user/userRouter.ts</title>
<path fill="none" stroke="#757575" d="M207.23,-103.33C230.35,-100.94 257.78,-98.1 282.78,-95.51"/>
<polygon fill="#757575" stroke="#757575" points="282.98,-99.01 292.57,-94.5 282.26,-92.05 282.98,-99.01"/>
</g>
<!-- minsa/application/MinsaService.ts -->
<g id="node3" class="node">
<title>minsa/application/MinsaService.ts</title>
<path fill="none" stroke="#c6c5fe" d="M1278.71,-221.75C1278.71,-221.75 1073.79,-221.75 1073.79,-221.75 1069.83,-221.75 1065.88,-217.79 1065.88,-213.83 1065.88,-213.83 1065.88,-205.92 1065.88,-205.92 1065.88,-201.96 1069.83,-198 1073.79,-198 1073.79,-198 1278.71,-198 1278.71,-198 1282.67,-198 1286.62,-201.96 1286.62,-205.92 1286.62,-205.92 1286.62,-213.83 1286.62,-213.83 1286.62,-217.79 1282.67,-221.75 1278.71,-221.75"/>
<text text-anchor="middle" x="1176.25" y="-204.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">minsa/application/MinsaService.ts</text>
</g>
<!-- minsa/domain/types.ts -->
<g id="node4" class="node">
<title>minsa/domain/types.ts</title>
<path fill="none" stroke="#cfffac" d="M1721.33,-241.75C1721.33,-241.75 1585.42,-241.75 1585.42,-241.75 1581.46,-241.75 1577.5,-237.79 1577.5,-233.83 1577.5,-233.83 1577.5,-225.92 1577.5,-225.92 1577.5,-221.96 1581.46,-218 1585.42,-218 1585.42,-218 1721.33,-218 1721.33,-218 1725.29,-218 1729.25,-221.96 1729.25,-225.92 1729.25,-225.92 1729.25,-233.83 1729.25,-233.83 1729.25,-237.79 1725.29,-241.75 1721.33,-241.75"/>
<text text-anchor="middle" x="1653.38" y="-224.45" font-family="Arial" font-size="14.00" fill="#cfffac">minsa/domain/types.ts</text>
</g>
<!-- minsa/application/MinsaService.ts&#45;&gt;minsa/domain/types.ts -->
<g id="edge2" class="edge">
<title>minsa/application/MinsaService.ts&#45;&gt;minsa/domain/types.ts</title>
<path fill="none" stroke="#757575" d="M1286.85,-214.49C1371.74,-218.06 1487.71,-222.94 1566.08,-226.24"/>
<polygon fill="#757575" stroke="#757575" points="1565.71,-229.73 1575.85,-226.65 1566,-222.74 1565.71,-229.73"/>
</g>
<!-- minsa/infrastructure/MinsaAPI.ts -->
<g id="node5" class="node">
<title>minsa/infrastructure/MinsaAPI.ts</title>
<path fill="none" stroke="#c6c5fe" d="M1533.58,-261.75C1533.58,-261.75 1337.67,-261.75 1337.67,-261.75 1333.71,-261.75 1329.75,-257.79 1329.75,-253.83 1329.75,-253.83 1329.75,-245.92 1329.75,-245.92 1329.75,-241.96 1333.71,-238 1337.67,-238 1337.67,-238 1533.58,-238 1533.58,-238 1537.54,-238 1541.5,-241.96 1541.5,-245.92 1541.5,-245.92 1541.5,-253.83 1541.5,-253.83 1541.5,-257.79 1537.54,-261.75 1533.58,-261.75"/>
<text text-anchor="middle" x="1435.62" y="-244.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">minsa/infrastructure/MinsaAPI.ts</text>
</g>
<!-- minsa/application/MinsaService.ts&#45;&gt;minsa/infrastructure/MinsaAPI.ts -->
<g id="edge3" class="edge">
<title>minsa/application/MinsaService.ts&#45;&gt;minsa/infrastructure/MinsaAPI.ts</title>
<path fill="none" stroke="#757575" d="M1256.57,-222.2C1284.21,-226.5 1315.44,-231.35 1343.93,-235.78"/>
<polygon fill="#757575" stroke="#757575" points="1343.21,-239.21 1353.63,-237.29 1344.28,-232.29 1343.21,-239.21"/>
</g>
<!-- minsa/infrastructure/MinsaAPI.ts&#45;&gt;minsa/domain/types.ts -->
<g id="edge4" class="edge">
<title>minsa/infrastructure/MinsaAPI.ts&#45;&gt;minsa/domain/types.ts</title>
<path fill="none" stroke="#757575" d="M1541.97,-240.11C1549.95,-239.37 1557.95,-238.63 1565.78,-237.9"/>
<polygon fill="#757575" stroke="#757575" points="1566,-241.4 1575.63,-236.99 1565.35,-234.43 1566,-241.4"/>
</g>
<!-- minsa/infrastructure/controllers/MinsaController.ts -->
<g id="node6" class="node">
<title>minsa/infrastructure/controllers/MinsaController.ts</title>
<path fill="none" stroke="#c6c5fe" d="M1014.83,-183.75C1014.83,-183.75 716.92,-183.75 716.92,-183.75 712.96,-183.75 709,-179.79 709,-175.83 709,-175.83 709,-167.92 709,-167.92 709,-163.96 712.96,-160 716.92,-160 716.92,-160 1014.83,-160 1014.83,-160 1018.79,-160 1022.75,-163.96 1022.75,-167.92 1022.75,-167.92 1022.75,-175.83 1022.75,-175.83 1022.75,-179.79 1018.79,-183.75 1014.83,-183.75"/>
<text text-anchor="middle" x="865.88" y="-166.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">minsa/infrastructure/controllers/MinsaController.ts</text>
</g>
<!-- minsa/infrastructure/controllers/MinsaController.ts&#45;&gt;minsa/application/MinsaService.ts -->
<g id="edge5" class="edge">
<title>minsa/infrastructure/controllers/MinsaController.ts&#45;&gt;minsa/application/MinsaService.ts</title>
<path fill="none" stroke="#757575" d="M967,-184.21C997.91,-188.02 1032.14,-192.24 1063.77,-196.14"/>
<polygon fill="#757575" stroke="#757575" points="1062.99,-199.57 1073.34,-197.32 1063.84,-192.62 1062.99,-199.57"/>
</g>
<!-- server/dependencies.ts -->
<g id="node8" class="node">
<title>server/dependencies.ts</title>
<path fill="none" stroke="#c6c5fe" d="M660.58,-141.75C660.58,-141.75 520.92,-141.75 520.92,-141.75 516.96,-141.75 513,-137.79 513,-133.83 513,-133.83 513,-125.92 513,-125.92 513,-121.96 516.96,-118 520.92,-118 520.92,-118 660.58,-118 660.58,-118 664.54,-118 668.5,-121.96 668.5,-125.92 668.5,-125.92 668.5,-133.83 668.5,-133.83 668.5,-137.79 664.54,-141.75 660.58,-141.75"/>
<text text-anchor="middle" x="590.75" y="-124.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">server/dependencies.ts</text>
</g>
<!-- minsa/infrastructure/minsaRouter.ts&#45;&gt;server/dependencies.ts -->
<g id="edge6" class="edge">
<title>minsa/infrastructure/minsaRouter.ts&#45;&gt;server/dependencies.ts</title>
<path fill="none" stroke="#757575" d="M472.83,-129.88C482.45,-129.88 492.09,-129.88 501.47,-129.88"/>
<polygon fill="#757575" stroke="#757575" points="501.25,-133.38 511.25,-129.88 501.25,-126.38 501.25,-133.38"/>
</g>
<!-- server/dependencies.ts&#45;&gt;minsa/application/MinsaService.ts -->
<g id="edge7" class="edge">
<title>server/dependencies.ts&#45;&gt;minsa/application/MinsaService.ts</title>
<path fill="none" stroke="#757575" d="M608.57,-142.15C630.43,-157.32 670.42,-182.41 709,-192.88 823.19,-223.83 958.86,-225 1054.32,-220.29"/>
<polygon fill="#757575" stroke="#757575" points="1054.44,-223.79 1064.25,-219.77 1054.08,-216.8 1054.44,-223.79"/>
</g>
<!-- server/dependencies.ts&#45;&gt;minsa/infrastructure/MinsaAPI.ts -->
<g id="edge8" class="edge">
<title>server/dependencies.ts&#45;&gt;minsa/infrastructure/MinsaAPI.ts</title>
<path fill="none" stroke="#757575" d="M602.21,-142.02C621.06,-162.91 663.1,-205.02 709,-221.88 925.91,-301.53 1204.18,-281.48 1344.75,-263.66"/>
<polygon fill="#757575" stroke="#757575" points="1344.9,-267.17 1354.37,-262.41 1344,-260.23 1344.9,-267.17"/>
</g>
<!-- server/dependencies.ts&#45;&gt;minsa/infrastructure/controllers/MinsaController.ts -->
<g id="edge9" class="edge">
<title>server/dependencies.ts&#45;&gt;minsa/infrastructure/controllers/MinsaController.ts</title>
<path fill="none" stroke="#757575" d="M657.71,-142.2C674.42,-145.19 692.35,-148.26 709,-150.88 724.56,-153.32 741.03,-155.72 757.19,-157.99"/>
<polygon fill="#757575" stroke="#757575" points="756.44,-161.42 766.83,-159.32 757.4,-154.48 756.44,-161.42"/>
</g>
<!-- services/jwt.ts -->
<g id="node9" class="node">
<title>services/jwt.ts</title>
<path fill="none" stroke="#c6c5fe" d="M907.96,-141.75C907.96,-141.75 823.79,-141.75 823.79,-141.75 819.83,-141.75 815.88,-137.79 815.88,-133.83 815.88,-133.83 815.88,-125.92 815.88,-125.92 815.88,-121.96 819.83,-118 823.79,-118 823.79,-118 907.96,-118 907.96,-118 911.92,-118 915.88,-121.96 915.88,-125.92 915.88,-125.92 915.88,-133.83 915.88,-133.83 915.88,-137.79 911.92,-141.75 907.96,-141.75"/>
<text text-anchor="middle" x="865.88" y="-124.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">services/jwt.ts</text>
</g>
<!-- server/dependencies.ts&#45;&gt;services/jwt.ts -->
<g id="edge10" class="edge">
<title>server/dependencies.ts&#45;&gt;services/jwt.ts</title>
<path fill="none" stroke="#757575" d="M668.92,-129.88C711.71,-129.88 764.15,-129.88 804.01,-129.88"/>
<polygon fill="#757575" stroke="#757575" points="803.92,-133.38 813.92,-129.88 803.92,-126.38 803.92,-133.38"/>
</g>
<!-- user/application/login.ts -->
<g id="node10" class="node">
<title>user/application/login.ts</title>
<path fill="none" stroke="#c6c5fe" d="M1246.83,-101.75C1246.83,-101.75 1105.67,-101.75 1105.67,-101.75 1101.71,-101.75 1097.75,-97.79 1097.75,-93.83 1097.75,-93.83 1097.75,-85.92 1097.75,-85.92 1097.75,-81.96 1101.71,-78 1105.67,-78 1105.67,-78 1246.83,-78 1246.83,-78 1250.79,-78 1254.75,-81.96 1254.75,-85.92 1254.75,-85.92 1254.75,-93.83 1254.75,-93.83 1254.75,-97.79 1250.79,-101.75 1246.83,-101.75"/>
<text text-anchor="middle" x="1176.25" y="-84.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">user/application/login.ts</text>
</g>
<!-- server/dependencies.ts&#45;&gt;user/application/login.ts -->
<g id="edge11" class="edge">
<title>server/dependencies.ts&#45;&gt;user/application/login.ts</title>
<path fill="none" stroke="#757575" d="M651.47,-117.55C669.85,-114.2 690.19,-110.93 709,-108.88 839.65,-94.58 991.96,-90.77 1086,-89.9"/>
<polygon fill="#757575" stroke="#757575" points="1085.98,-93.4 1095.95,-89.82 1085.92,-86.4 1085.98,-93.4"/>
</g>
<!-- user/infrastructure/UserRepository.ts -->
<g id="node11" class="node">
<title>user/infrastructure/UserRepository.ts</title>
<path fill="none" stroke="#c6c5fe" d="M1285.83,-59.75C1285.83,-59.75 1066.67,-59.75 1066.67,-59.75 1062.71,-59.75 1058.75,-55.79 1058.75,-51.83 1058.75,-51.83 1058.75,-43.92 1058.75,-43.92 1058.75,-39.96 1062.71,-36 1066.67,-36 1066.67,-36 1285.83,-36 1285.83,-36 1289.79,-36 1293.75,-39.96 1293.75,-43.92 1293.75,-43.92 1293.75,-51.83 1293.75,-51.83 1293.75,-55.79 1289.79,-59.75 1285.83,-59.75"/>
<text text-anchor="middle" x="1176.25" y="-42.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">user/infrastructure/UserRepository.ts</text>
</g>
<!-- server/dependencies.ts&#45;&gt;user/infrastructure/UserRepository.ts -->
<g id="edge12" class="edge">
<title>server/dependencies.ts&#45;&gt;user/infrastructure/UserRepository.ts</title>
<path fill="none" stroke="#757575" d="M648.41,-117.54C656.79,-115.05 665.22,-112.17 673,-108.88 690.27,-101.56 691.18,-92.7 709,-86.88 819.17,-50.85 951.39,-42.87 1046.85,-42.86"/>
<polygon fill="#757575" stroke="#757575" points="1046.78,-46.36 1056.79,-42.9 1046.8,-39.36 1046.78,-46.36"/>
</g>
<!-- user/infrastructure/controllers/login.ts -->
<g id="node12" class="node">
<title>user/infrastructure/controllers/login.ts</title>
<path fill="none" stroke="#c6c5fe" d="M976.58,-23.75C976.58,-23.75 755.17,-23.75 755.17,-23.75 751.21,-23.75 747.25,-19.79 747.25,-15.83 747.25,-15.83 747.25,-7.92 747.25,-7.92 747.25,-3.96 751.21,0 755.17,0 755.17,0 976.58,0 976.58,0 980.54,0 984.5,-3.96 984.5,-7.92 984.5,-7.92 984.5,-15.83 984.5,-15.83 984.5,-19.79 980.54,-23.75 976.58,-23.75"/>
<text text-anchor="middle" x="865.88" y="-6.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">user/infrastructure/controllers/login.ts</text>
</g>
<!-- server/dependencies.ts&#45;&gt;user/infrastructure/controllers/login.ts -->
<g id="edge13" class="edge">
<title>server/dependencies.ts&#45;&gt;user/infrastructure/controllers/login.ts</title>
<path fill="none" stroke="#757575" d="M658.77,-117.67C663.86,-115.25 668.69,-112.34 673,-108.88 700.57,-86.7 680.48,-58.82 709,-37.88 717.28,-31.79 726.48,-26.93 736.14,-23.07"/>
<polygon fill="#757575" stroke="#757575" points="737.13,-26.43 745.37,-19.77 734.77,-19.84 737.13,-26.43"/>
</g>
<!-- services/interfaces/IJWT.ts -->
<g id="node14" class="node">
<title>services/interfaces/IJWT.ts</title>
<path fill="none" stroke="#cfffac" d="M1515.96,-141.75C1515.96,-141.75 1355.29,-141.75 1355.29,-141.75 1351.33,-141.75 1347.38,-137.79 1347.38,-133.83 1347.38,-133.83 1347.38,-125.92 1347.38,-125.92 1347.38,-121.96 1351.33,-118 1355.29,-118 1355.29,-118 1515.96,-118 1515.96,-118 1519.92,-118 1523.88,-121.96 1523.88,-125.92 1523.88,-125.92 1523.88,-133.83 1523.88,-133.83 1523.88,-137.79 1519.92,-141.75 1515.96,-141.75"/>
<text text-anchor="middle" x="1435.62" y="-124.45" font-family="Arial" font-size="14.00" fill="#cfffac">services/interfaces/IJWT.ts</text>
</g>
<!-- services/jwt.ts&#45;&gt;services/interfaces/IJWT.ts -->
<g id="edge16" class="edge">
<title>services/jwt.ts&#45;&gt;services/interfaces/IJWT.ts</title>
<path fill="none" stroke="#757575" d="M916.27,-129.88C1009.6,-129.88 1213.46,-129.88 1335.88,-129.88"/>
<polygon fill="#757575" stroke="#757575" points="1335.58,-133.38 1345.58,-129.88 1335.58,-126.38 1335.58,-133.38"/>
</g>
<!-- user/application/login.ts&#45;&gt;services/interfaces/IJWT.ts -->
<g id="edge17" class="edge">
<title>user/application/login.ts&#45;&gt;services/interfaces/IJWT.ts</title>
<path fill="none" stroke="#757575" d="M1255.17,-101.98C1283.12,-106.33 1314.88,-111.26 1343.83,-115.76"/>
<polygon fill="#757575" stroke="#757575" points="1343.28,-119.22 1353.7,-117.3 1344.35,-112.3 1343.28,-119.22"/>
</g>
<!-- user/domain/IUser.ts -->
<g id="node15" class="node">
<title>user/domain/IUser.ts</title>
<path fill="none" stroke="#c6c5fe" d="M1497.96,-61.75C1497.96,-61.75 1373.29,-61.75 1373.29,-61.75 1369.33,-61.75 1365.38,-57.79 1365.38,-53.83 1365.38,-53.83 1365.38,-45.92 1365.38,-45.92 1365.38,-41.96 1369.33,-38 1373.29,-38 1373.29,-38 1497.96,-38 1497.96,-38 1501.92,-38 1505.88,-41.96 1505.88,-45.92 1505.88,-45.92 1505.88,-53.83 1505.88,-53.83 1505.88,-57.79 1501.92,-61.75 1497.96,-61.75"/>
<text text-anchor="middle" x="1435.62" y="-44.45" font-family="Arial" font-size="14.00" fill="#c6c5fe">user/domain/IUser.ts</text>
</g>
<!-- user/application/login.ts&#45;&gt;user/domain/IUser.ts -->
<g id="edge18" class="edge">
<title>user/application/login.ts&#45;&gt;user/domain/IUser.ts</title>
<path fill="none" stroke="#757575" d="M1255.17,-77.77C1286.54,-72.89 1322.7,-67.27 1354.3,-62.36"/>
<polygon fill="#757575" stroke="#757575" points="1354.43,-65.88 1363.78,-60.89 1353.36,-58.96 1354.43,-65.88"/>
</g>
<!-- user/domain/User.ts -->
<g id="node16" class="node">
<title>user/domain/User.ts</title>
<path fill="none" stroke="#cfffac" d="M1713.83,-41.75C1713.83,-41.75 1592.92,-41.75 1592.92,-41.75 1588.96,-41.75 1585,-37.79 1585,-33.83 1585,-33.83 1585,-25.92 1585,-25.92 1585,-21.96 1588.96,-18 1592.92,-18 1592.92,-18 1713.83,-18 1713.83,-18 1717.79,-18 1721.75,-21.96 1721.75,-25.92 1721.75,-25.92 1721.75,-33.83 1721.75,-33.83 1721.75,-37.79 1717.79,-41.75 1713.83,-41.75"/>
<text text-anchor="middle" x="1653.38" y="-24.45" font-family="Arial" font-size="14.00" fill="#cfffac">user/domain/User.ts</text>
</g>
<!-- user/application/login.ts&#45;&gt;user/domain/User.ts -->
<g id="edge19" class="edge">
<title>user/application/login.ts&#45;&gt;user/domain/User.ts</title>
<path fill="none" stroke="#757575" d="M1255.21,-92.55C1329.55,-93.53 1444.28,-90.9 1541.5,-70.88 1567,-65.62 1594.46,-55.5 1615.68,-46.63"/>
<polygon fill="#757575" stroke="#757575" points="1616.95,-49.9 1624.76,-42.74 1614.19,-43.46 1616.95,-49.9"/>
</g>
<!-- user/infrastructure/UserRepository.ts&#45;&gt;user/domain/IUser.ts -->
<g id="edge21" class="edge">
<title>user/infrastructure/UserRepository.ts&#45;&gt;user/domain/IUser.ts</title>
<path fill="none" stroke="#757575" d="M1293.89,-48.78C1314.14,-48.94 1334.79,-49.1 1353.82,-49.25"/>
<polygon fill="#757575" stroke="#757575" points="1353.58,-52.75 1363.61,-49.32 1353.64,-45.75 1353.58,-52.75"/>
</g>
<!-- user/infrastructure/UserRepository.ts&#45;&gt;user/domain/User.ts -->
<g id="edge22" class="edge">
<title>user/infrastructure/UserRepository.ts&#45;&gt;user/domain/User.ts</title>
<path fill="none" stroke="#757575" d="M1260.98,-35.55C1283.27,-32.79 1307.38,-30.26 1329.75,-28.88 1423.68,-23.05 1447.39,-28.45 1541.5,-28.88 1551.75,-28.92 1562.53,-28.99 1573.18,-29.07"/>
<polygon fill="#757575" stroke="#757575" points="1573.11,-32.57 1583.14,-29.15 1573.17,-25.57 1573.11,-32.57"/>
</g>
<!-- user/infrastructure/controllers/login.ts&#45;&gt;user/application/login.ts -->
<g id="edge23" class="edge">
<title>user/infrastructure/controllers/login.ts&#45;&gt;user/application/login.ts</title>
<path fill="none" stroke="#757575" d="M904.05,-24.25C942.46,-36.72 1004.32,-55.93 1058.75,-68.88 1068.44,-71.18 1078.64,-73.37 1088.8,-75.39"/>
<polygon fill="#757575" stroke="#757575" points="1087.82,-78.77 1098.31,-77.24 1089.16,-71.9 1087.82,-78.77"/>
</g>
<!-- user/infrastructure/controllers/login.ts&#45;&gt;user/domain/User.ts -->
<g id="edge24" class="edge">
<title>user/infrastructure/controllers/login.ts&#45;&gt;user/domain/User.ts</title>
<path fill="none" stroke="#757575" d="M984.84,-11.58C1119.55,-11.72 1346.51,-13.4 1541.5,-21.88 1551.77,-22.32 1562.56,-22.93 1573.21,-23.61"/>
<polygon fill="#757575" stroke="#757575" points="1572.97,-27.1 1583.18,-24.27 1573.43,-20.12 1572.97,-27.1"/>
</g>
<!-- user/userRouter.ts&#45;&gt;server/dependencies.ts -->
<g id="edge25" class="edge">
<title>user/userRouter.ts&#45;&gt;server/dependencies.ts</title>
<path fill="none" stroke="#757575" d="M421.17,-99.24C448.79,-104.26 481.57,-110.22 510.85,-115.54"/>
<polygon fill="#757575" stroke="#757575" points="509.93,-118.93 520.39,-117.27 511.18,-112.04 509.93,-118.93"/>
</g>
<!-- user/domain/userDTO.ts -->
<g id="node17" class="node">
<title>user/domain/userDTO.ts</title>
<path fill="none" stroke="#cfffac" d="M665.08,-99.75C665.08,-99.75 516.42,-99.75 516.42,-99.75 512.46,-99.75 508.5,-95.79 508.5,-91.83 508.5,-91.83 508.5,-83.92 508.5,-83.92 508.5,-79.96 512.46,-76 516.42,-76 516.42,-76 665.08,-76 665.08,-76 669.04,-76 673,-79.96 673,-83.92 673,-83.92 673,-91.83 673,-91.83 673,-95.79 669.04,-99.75 665.08,-99.75"/>
<text text-anchor="middle" x="590.75" y="-82.45" font-family="Arial" font-size="14.00" fill="#cfffac">user/domain/userDTO.ts</text>
</g>
<!-- user/userRouter.ts&#45;&gt;user/domain/userDTO.ts -->
<g id="edge26" class="edge">
<title>user/userRouter.ts&#45;&gt;user/domain/userDTO.ts</title>
<path fill="none" stroke="#757575" d="M421.17,-87.88C444.46,-87.88 471.42,-87.88 496.86,-87.88"/>
<polygon fill="#757575" stroke="#757575" points="496.52,-91.38 506.52,-87.88 496.52,-84.38 496.52,-91.38"/>
</g>
<!-- user/domain/IUser.ts&#45;&gt;user/domain/User.ts -->
<g id="edge20" class="edge">
<title>user/domain/IUser.ts&#45;&gt;user/domain/User.ts</title>
<path fill="none" stroke="#757575" d="M1506.37,-43.41C1527.76,-41.43 1551.44,-39.23 1573.4,-37.2"/>
<polygon fill="#757575" stroke="#757575" points="1573.56,-40.7 1583.2,-36.29 1572.92,-33.73 1573.56,-40.7"/>
</g>
</g>
</svg>

import * as jwt from 'jsonwebtoken'
import type { JwtPayload } from 'jsonwebtoken'
import { handleJWTError, validateTokenPresence } from '../server/errors/JWTErrorHandler.js'
import type { IJWT, TokenPair } from './interfaces/IJWT.js'

export class JWT implements IJWT {
  private readonly ACCESS_SECRET = process.env.JWT_SECRET ?? 'your-secret-key'
  private readonly REFRESH_SECRET = process.env.JWT_REFRESH_SECRET ?? 'your-refresh-secret-key'
  private readonly ACCESS_EXPIRY: string
  private readonly REFRESH_EXPIRY: string

  constructor() {
    // Determine if we're in development mode
    const isDevelopment = process.env.NODE_ENV !== 'production'

    // Set expiration times based on environment
    // Development: longer expiration for easier testing
    // Production: shorter expiration for security
    this.ACCESS_EXPIRY = process.env.JWT_ACCESS_EXPIRY ?? (isDevelopment ? '1d' : '15m')
    this.REFRESH_EXPIRY = process.env.JWT_REFRESH_EXPIRY ?? '7d'
  }

  async sign(payload: string): Promise<TokenPair> {
    const accessToken = jwt.sign({ data: payload }, this.ACCESS_SECRET as string, { expiresIn: this.ACCESS_EXPIRY } as jwt.SignOptions)

    const refreshToken = jwt.sign({ data: payload }, this.REFRESH_SECRET as string, { expiresIn: this.REFRESH_EXPIRY } as jwt.SignOptions)

    return { accessToken, refreshToken }
  }

  verify(token: string): JwtPayload {
    validateTokenPresence(token)
    try {
      return jwt.verify(token, this.ACCESS_SECRET) as JwtPayload
    } catch (error) {
      handleJWTError(error)
    }
  }

  verifyRefresh(token: string): JwtPayload {
    validateTokenPresence(token)
    try {
      return jwt.verify(token, this.REFRESH_SECRET) as JwtPayload
    } catch (error) {
      handleJWTError(error)
    }
  }

  async refreshAccessToken(refreshToken: string): Promise<string> {
    const payload = this.verifyRefresh(refreshToken)
    return jwt.sign({ data: payload.data }, this.ACCESS_SECRET as string, { expiresIn: this.ACCESS_EXPIRY } as jwt.SignOptions)
  }
}

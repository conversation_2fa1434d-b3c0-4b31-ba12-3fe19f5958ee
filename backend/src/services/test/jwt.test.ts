import { afterEach, beforeEach, describe, expect, it } from 'bun:test'
import * as jwt from 'jsonwebtoken'
import { AuthenticationError } from '../../server/errors/CustomErrors.js'
import { JWT } from '../jwt.js'

describe('JWT Service', () => {
  let jwtService: JWT
  const testPayload = 'test-user-id'
  const originalEnv = process.env

  beforeEach(() => {
    // Reset environment variables for each test
    process.env = { ...originalEnv }
    process.env.JWT_SECRET = 'test-secret-key'
    process.env.JWT_REFRESH_SECRET = 'test-refresh-secret-key'
    process.env.NODE_ENV = 'test'

    jwtService = new JWT()
  })

  afterEach(() => {
    // Restore original environment
    process.env = originalEnv
  })

  describe('constructor', () => {
    it('should use environment variables for secrets', () => {
      process.env.JWT_SECRET = 'custom-secret'
      process.env.JWT_REFRESH_SECRET = 'custom-refresh-secret'

      const service = new JWT()
      // We can't directly test private properties, but we can test behavior
      expect(service).toBeInstanceOf(JWT)
    })

    it('should use default secrets when environment variables are not set', () => {
      delete process.env.JWT_SECRET
      delete process.env.JWT_REFRESH_SECRET

      const service = new JWT()
      expect(service).toBeInstanceOf(JWT)
    })

    it('should set development expiry times in non-production environment', () => {
      process.env.NODE_ENV = 'development'

      const service = new JWT()
      expect(service).toBeInstanceOf(JWT)
    })

    it('should set production expiry times in production environment', () => {
      process.env.NODE_ENV = 'production'

      const service = new JWT()
      expect(service).toBeInstanceOf(JWT)
    })
  })

  describe('sign', () => {
    it('should generate access and refresh tokens', async () => {
      const result = await jwtService.sign(testPayload)

      expect(result.accessToken).toBeDefined()
      expect(result.refreshToken).toBeDefined()
      expect(typeof result.accessToken).toBe('string')
      expect(typeof result.refreshToken).toBe('string')
      expect(result.accessToken.length).toBeGreaterThan(0)
      expect(result.refreshToken.length).toBeGreaterThan(0)
    })

    it('should generate different tokens for different payloads', async () => {
      const result1 = await jwtService.sign('user-1')
      const result2 = await jwtService.sign('user-2')

      expect(result1.accessToken).not.toBe(result2.accessToken)
      expect(result1.refreshToken).not.toBe(result2.refreshToken)
    })

    it('should generate tokens with correct payload structure', async () => {
      const result = await jwtService.sign(testPayload)

      // Verify token structure without using the service's verify method
      const decoded = jwt.verify(result.accessToken, process.env.JWT_SECRET!) as any
      expect(decoded.data).toBe(testPayload)
      expect(decoded.iat).toBeDefined()
      expect(decoded.exp).toBeDefined()
    })
  })

  describe('verify', () => {
    it('should verify valid access token', async () => {
      const { accessToken } = await jwtService.sign(testPayload)

      const result = jwtService.verify(accessToken)

      expect(result.data).toBe(testPayload)
      expect(result.iat).toBeDefined()
      expect(result.exp).toBeDefined()
    })

    it('should throw AuthenticationError for invalid token', () => {
      const invalidToken = 'invalid.token.here'

      expect(() => {
        jwtService.verify(invalidToken)
      }).toThrow(AuthenticationError)
    })

    it('should throw AuthenticationError for expired token', () => {
      // Create an expired token
      const expiredToken = jwt.sign({ data: testPayload }, process.env.JWT_SECRET!, { expiresIn: '-1s' })

      expect(() => {
        jwtService.verify(expiredToken)
      }).toThrow(AuthenticationError)
    })

    it('should throw AuthenticationError for token with wrong secret', () => {
      const tokenWithWrongSecret = jwt.sign({ data: testPayload }, 'wrong-secret', { expiresIn: '1h' })

      expect(() => {
        jwtService.verify(tokenWithWrongSecret)
      }).toThrow(AuthenticationError)
    })

    it('should throw AuthenticationError for malformed token', () => {
      const malformedToken = 'not.a.valid.jwt.token'

      expect(() => {
        jwtService.verify(malformedToken)
      }).toThrow(AuthenticationError)
    })
  })

  describe('verifyRefresh', () => {
    it('should verify valid refresh token', async () => {
      const { refreshToken } = await jwtService.sign(testPayload)

      const result = jwtService.verifyRefresh(refreshToken)

      expect(result.data).toBe(testPayload)
      expect(result.iat).toBeDefined()
      expect(result.exp).toBeDefined()
    })

    it('should throw AuthenticationError for invalid refresh token', () => {
      const invalidToken = 'invalid.refresh.token'

      expect(() => {
        jwtService.verifyRefresh(invalidToken)
      }).toThrow(AuthenticationError)
    })

    it('should throw AuthenticationError for expired refresh token', () => {
      // Create an expired refresh token
      const expiredToken = jwt.sign({ data: testPayload }, process.env.JWT_REFRESH_SECRET!, { expiresIn: '-1s' })

      expect(() => {
        jwtService.verifyRefresh(expiredToken)
      }).toThrow(AuthenticationError)
    })

    it('should not verify access token as refresh token', async () => {
      const { accessToken } = await jwtService.sign(testPayload)

      expect(() => {
        jwtService.verifyRefresh(accessToken)
      }).toThrow(AuthenticationError)
    })
  })

  describe('refreshAccessToken', () => {
    it('should generate new access token from valid refresh token', async () => {
      const { refreshToken } = await jwtService.sign(testPayload)

      const newAccessToken = await jwtService.refreshAccessToken(refreshToken)

      expect(typeof newAccessToken).toBe('string')
      expect(newAccessToken.length).toBeGreaterThan(0)

      // Verify the new access token contains correct payload
      const decoded = jwtService.verify(newAccessToken)
      expect(decoded.data).toBe(testPayload)
    })

    it('should throw AuthenticationError for invalid refresh token', async () => {
      const invalidRefreshToken = 'invalid.refresh.token'

      await expect(jwtService.refreshAccessToken(invalidRefreshToken)).rejects.toThrow(AuthenticationError)
    })

    it('should throw AuthenticationError for expired refresh token', async () => {
      // Create an expired refresh token
      const expiredRefreshToken = jwt.sign({ data: testPayload }, process.env.JWT_REFRESH_SECRET!, { expiresIn: '-1s' })

      await expect(jwtService.refreshAccessToken(expiredRefreshToken)).rejects.toThrow(AuthenticationError)
    })

    it('should generate different access tokens on multiple refresh calls', async () => {
      const { refreshToken } = await jwtService.sign(testPayload)

      const token1 = await jwtService.refreshAccessToken(refreshToken)
      // Wait a bit to ensure different timestamps
      await new Promise((resolve) => setTimeout(resolve, 1000))
      const token2 = await jwtService.refreshAccessToken(refreshToken)

      expect(token1).not.toBe(token2)

      // Both should be valid and contain same payload
      const decoded1 = jwtService.verify(token1)
      const decoded2 = jwtService.verify(token2)
      expect(decoded1.data).toBe(testPayload)
      expect(decoded2.data).toBe(testPayload)
    })
  })

  describe('integration scenarios', () => {
    it('should handle complete token lifecycle', async () => {
      // 1. Sign tokens
      const { accessToken, refreshToken } = await jwtService.sign(testPayload)

      // 2. Verify access token
      const accessPayload = jwtService.verify(accessToken)
      expect(accessPayload.data).toBe(testPayload)

      // 3. Verify refresh token
      const refreshPayload = jwtService.verifyRefresh(refreshToken)
      expect(refreshPayload.data).toBe(testPayload)

      // 4. Refresh access token
      const newAccessToken = await jwtService.refreshAccessToken(refreshToken)

      // 5. Verify new access token
      const newAccessPayload = jwtService.verify(newAccessToken)
      expect(newAccessPayload.data).toBe(testPayload)
    })
  })
})

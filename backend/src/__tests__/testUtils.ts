// Test utilities barrel export to avoid deep relative imports in test files
// This file provides a single import point for test dependencies

// Error classes
export { AuthenticationError, ValidationError, ConflictError, NotFoundError, InternalServerError } from '../server/errors/index.js'

// Services
export { JWT } from '../services/jwt.js'

// User domain and application
export { LoginUser } from '../user/application/login.js'
export { firebaseLoginDTO, refreshTokenDTO } from '../user/domain/userDTO.js'
export { LoginController } from '../user/infrastructure/controllers/login.js'

// Favorites domain and application
export { AddMedicationFavorite } from '../favorites/application/AddMedicationFavorite.js'
export { ListMedicationFavorites } from '../favorites/application/ListMedicationFavorites.js'
export { SearchMedicationFavorites } from '../favorites/application/SearchMedicationFavorites.js'
export {
  addMedicationFavoriteDTO,
  listMedicationFavoritesDTO,
  removeMedicationFavoriteDTO,
  searchMedicationFavoritesDTO
} from '../favorites/domain/medicationFavoriteDTO.js'
export { MedicationFavoriteController } from '../favorites/infrastructure/controllers/MedicationFavoriteController.js'

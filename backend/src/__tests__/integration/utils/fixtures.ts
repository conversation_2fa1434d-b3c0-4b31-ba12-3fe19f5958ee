import type { MedicationData } from '../../../favorites/domain/MedicationFavorite.js'

export const createTestMedicationData = (overrides: Partial<MedicationData> = {}): MedicationData => ({
  codEstab: '0094084',
  codProdE: 8850,
  fecha: '21/04/2025 05:18:50 PM',
  nombreProducto: 'PARACETAMOL',
  precio1: 5.3,
  precio2: 0.05,
  precio3: null,
  codGrupoFF: '3',
  ubicodigo: '150110',
  direccion: 'AV. TUPAC AMARU CARABAYLLO 361',
  telefono: '01-3142020',
  nomGrupoFF: 'Tableta - Capsula',
  setcodigo: 'Privado',
  nombreComercial: 'BOTICA INKAFARMA',
  grupo: '2926',
  totalPA: '1',
  concent: '500 mg',
  nombreFormaFarmaceutica: 'Tableta',
  fracciones: 100,
  totalRegistros: null,
  nombreLaboratorio: 'INSTITUTO QUIMIOTERAPICO S.A.',
  nombreTitular: 'INSTITUTO QUIMIOTERAPICO S.A. - IQFARMA',
  catCodigo: '04',
  nombreSustancia: 'PARACETAMOL',
  fabricante: null,
  departamento: 'LIMA',
  provincia: 'LIMA',
  distrito: 'CARABAYLLO',
  ...overrides
})

export const createTestMedicationData2 = (overrides: Partial<MedicationData> = {}): MedicationData => ({
  codEstab: '0094085',
  codProdE: 8851,
  fecha: '21/04/2025 05:20:00 PM',
  nombreProducto: 'IBUPROFENO',
  precio1: 8.5,
  precio2: 0.08,
  precio3: null,
  codGrupoFF: '3',
  ubicodigo: '150111',
  direccion: 'AV. BRASIL 1234',
  telefono: '01-4567890',
  nomGrupoFF: 'Tableta - Capsula',
  setcodigo: 'Privado',
  nombreComercial: 'FARMACIA UNIVERSAL',
  grupo: '2927',
  totalPA: '1',
  concent: '400 mg',
  nombreFormaFarmaceutica: 'Tableta',
  fracciones: 50,
  totalRegistros: null,
  nombreLaboratorio: 'LABORATORIO FARMACEUTICO S.A.',
  nombreTitular: 'LABORATORIO FARMACEUTICO S.A.',
  catCodigo: '04',
  nombreSustancia: 'IBUPROFENO',
  fabricante: null,
  departamento: 'LIMA',
  provincia: 'LIMA',
  distrito: 'LIMA',
  ...overrides
})

export const createTestMedicationData3 = (overrides: Partial<MedicationData> = {}): MedicationData => ({
  codEstab: '0094086',
  codProdE: 8852,
  fecha: '21/04/2025 05:25:00 PM',
  nombreProducto: 'AMOXICILINA',
  precio1: 12.0,
  precio2: 0.12,
  precio3: null,
  codGrupoFF: '4',
  ubicodigo: '150112',
  direccion: 'AV. AREQUIPA 5678',
  telefono: '01-9876543',
  nomGrupoFF: 'Capsula',
  setcodigo: 'Privado',
  nombreComercial: 'MIFARMA',
  grupo: '2928',
  totalPA: '1',
  concent: '500 mg',
  nombreFormaFarmaceutica: 'Capsula',
  fracciones: 20,
  totalRegistros: null,
  nombreLaboratorio: 'ANTIBIOTICOS DEL PERU S.A.',
  nombreTitular: 'ANTIBIOTICOS DEL PERU S.A.',
  catCodigo: '05',
  nombreSustancia: 'AMOXICILINA',
  fabricante: null,
  departamento: 'LIMA',
  provincia: 'LIMA',
  distrito: 'MIRAFLORES',
  ...overrides
})

export const testUserPreferences = {
  language: 'es',
  theme: 'light' as const,
  notifications: true,
  defaultLocation: {
    departmentCode: '15',
    provinceCode: '01',
    districtCode: '01'
  }
}

export const testUserPreferences2 = {
  language: 'en',
  theme: 'dark' as const,
  notifications: false,
  defaultLocation: {
    departmentCode: '07',
    provinceCode: '01',
    districtCode: '01'
  }
}

export const testHealthData = {
  allergies: ['penicillin', 'shellfish'],
  conditions: ['diabetes', 'hypertension'],
  medications: [
    {
      name: 'Metformin',
      dosage: '500mg',
      frequency: 'twice daily'
    },
    {
      name: 'Lisinopril',
      dosage: '10mg',
      frequency: 'once daily'
    }
  ]
}

export const testHealthData2 = {
  allergies: ['aspirin'],
  conditions: ['asthma'],
  medications: [
    {
      name: 'Albuterol',
      dosage: '90mcg',
      frequency: 'as needed'
    }
  ]
}

export const testLocationData = {
  latitude: -12.0464,
  longitude: -77.0428
}

export const testLocationData2 = {
  latitude: -11.0464,
  longitude: -76.0428
}

export const createFirebaseLoginPayload = (overrides: any = {}) => ({
  firebaseId: 'firebase-test-id-123',
  email: '<EMAIL>',
  name: 'New Test User',
  profilePictureUrl: 'https://example.com/default-avatar.jpg',
  firebaseToken: 'firebase-token-xyz',
  ...overrides
})

export const createInvalidMedicationData = () => ({
  // Missing required fields
  codEstab: '',
  codProdE: 'invalid', // Should be number
  nombreProducto: '',
  precio1: 'invalid' // Should be number
})

export const createLargeMedicationDataset = (count: number = 25): MedicationData[] => {
  const medications: MedicationData[] = []

  for (let i = 0; i < count; i++) {
    medications.push(
      createTestMedicationData({
        codEstab: `009408${i.toString().padStart(2, '0')}`,
        codProdE: 8850 + i,
        nombreProducto: `MEDICATION_${i + 1}`,
        precio1: 5.0 + i * 0.5,
        nombreComercial: `PHARMACY_${i + 1}`,
        departamento: 'LIMA',
        provincia: 'LIMA',
        distrito: `DISTRICT_${i + 1}`
      })
    )
  }

  return medications
}

import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'bun:test'
import { generateTestAccessToken, testUsers } from '../setup/auth.js'
import { cleanupTestDatabase, getTestPrismaClient, resetTestDatabase, seedTestDatabase, setupTestDatabase } from '../setup/database.js'
import { makeAuthenticatedRequest, makeRequest, startTestServer, stopTestServer } from '../setup/server.js'
import {
  createInvalidMedicationData,
  createLargeMedicationDataset,
  createTestMedicationData,
  createTestMedicationData2,
  createTestMedicationData3
} from '../utils/fixtures.js'
import {
  expectAuthenticationError,
  expectConflictError,
  expectErrorResponse,
  expectNotFoundError,
  expectSuccessResponse,
  expectValidationError,
  validateMedicationFavoriteResponse,
  validatePaginationResponse
} from '../utils/testHelpers.js'

describe('Favorites API Integration Tests', () => {
  beforeAll(async () => {
    await setupTestDatabase()
    await startTestServer()
  })

  afterAll(async () => {
    await stopTestServer()
    await cleanupTestDatabase()
  })

  beforeEach(async () => {
    await resetTestDatabase()
    await seedTestDatabase()
  })

  describe('POST /favorites/medications', () => {
    describe('Successful addition scenarios', () => {
      it('should add medication to favorites with valid data', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const medicationData = createTestMedicationData()

        const response = await makeAuthenticatedRequest('POST', '/favorites/medications', token, {
          body: medicationData
        })

        const data = await expectSuccessResponse(response, 201)
        validateMedicationFavoriteResponse(data)
        expect(data.userId).toBe(testUsers.user1.id)
        expect(data.codEstab).toBe(medicationData.codEstab)
        expect(data.codProdE).toBe(medicationData.codProdE)
        expect(data.medicationData).toEqual(medicationData)
      })

      it('should add multiple different medications to favorites', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const medication1 = createTestMedicationData()
        const medication2 = createTestMedicationData2()

        // Add first medication
        const response1 = await makeAuthenticatedRequest('POST', '/favorites/medications', token, {
          body: medication1
        })
        const data1 = await expectSuccessResponse(response1, 201)
        validateMedicationFavoriteResponse(data1)

        // Add second medication
        const response2 = await makeAuthenticatedRequest('POST', '/favorites/medications', token, {
          body: medication2
        })
        const data2 = await expectSuccessResponse(response2, 201)
        validateMedicationFavoriteResponse(data2)

        expect(data1.id).not.toBe(data2.id)
        expect(data1.codEstab).not.toBe(data2.codEstab)
      })
    })

    describe('Conflict scenarios', () => {
      it('should reject adding duplicate medication to favorites', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const medicationData = createTestMedicationData()

        // Add medication first time
        const response1 = await makeAuthenticatedRequest('POST', '/favorites/medications', token, {
          body: medicationData
        })
        await expectSuccessResponse(response1, 201)

        // Try to add same medication again
        const response2 = await makeAuthenticatedRequest('POST', '/favorites/medications', token, {
          body: medicationData
        })
        await expectConflictError(response2, 'already in favorites')
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject addition without authentication token', async () => {
        const medicationData = createTestMedicationData()

        const response = await makeRequest('POST', '/favorites/medications', {
          body: medicationData
        })

        await expectAuthenticationError(response)
      })

      it('should reject addition with invalid authentication token', async () => {
        const medicationData = createTestMedicationData()

        const response = await makeAuthenticatedRequest('POST', '/favorites/medications', 'invalid-token', {
          body: medicationData
        })

        await expectAuthenticationError(response)
      })

      it('should reject addition with missing required fields', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidData = createInvalidMedicationData()

        const response = await makeAuthenticatedRequest('POST', '/favorites/medications', token, {
          body: invalidData
        })

        await expectValidationError(response)
      })

      it('should reject addition with invalid data types', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidData = {
          ...createTestMedicationData(),
          codProdE: 'should-be-number',
          precio1: 'should-be-number'
        }

        const response = await makeAuthenticatedRequest('POST', '/favorites/medications', token, {
          body: invalidData
        })

        await expectValidationError(response)
      })
    })
  })

  describe('GET /favorites/medications', () => {
    beforeEach(async () => {
      // Add some test favorites
      const prisma = getTestPrismaClient()
      const medications = createLargeMedicationDataset(15)

      for (let i = 0; i < medications.length; i++) {
        await prisma.medication_favorite.create({
          data: {
            user_id: testUsers.user1.id,
            cod_estab: medications[i].codEstab,
            cod_prod_e: medications[i].codProdE,
            medication_data: JSON.stringify(medications[i])
          }
        })
      }
    })

    describe('Successful listing scenarios', () => {
      it('should list favorites with default pagination', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications', token)

        const data = await expectSuccessResponse(response, 200)
        validatePaginationResponse(data, 1, 20)
        expect(data.favorites.length).toBe(15) // All 15 favorites should fit in first page
        data.favorites.forEach(validateMedicationFavoriteResponse)
      })

      it('should list favorites with custom pagination', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications?page=1&limit=5', token)

        const data = await expectSuccessResponse(response, 200)
        validatePaginationResponse(data, 1, 5)
        expect(data.favorites.length).toBe(5)
        expect(data.pagination.total).toBe(15)
        expect(data.pagination.totalPages).toBe(3)
        expect(data.pagination.hasNext).toBe(true)
        expect(data.pagination.hasPrev).toBe(false)
      })

      it('should list favorites for second page', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications?page=2&limit=5', token)

        const data = await expectSuccessResponse(response, 200)
        validatePaginationResponse(data, 2, 5)
        expect(data.favorites.length).toBe(5)
        expect(data.pagination.hasNext).toBe(true)
        expect(data.pagination.hasPrev).toBe(true)
      })

      it('should return empty list for user with no favorites', async () => {
        const token = generateTestAccessToken(testUsers.user2.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications', token)

        const data = await expectSuccessResponse(response, 200)
        validatePaginationResponse(data, 1, 20)
        expect(data.favorites.length).toBe(0)
        expect(data.pagination.total).toBe(0)
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject listing without authentication token', async () => {
        const response = await makeRequest('GET', '/favorites/medications')

        await expectAuthenticationError(response)
      })

      it('should reject listing with invalid page parameter', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications?page=0', token)

        await expectValidationError(response)
      })

      it('should reject listing with invalid limit parameter', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications?limit=101', token)

        await expectValidationError(response)
      })
    })
  })

  describe('GET /favorites/medications/search', () => {
    beforeEach(async () => {
      // Add test favorites with different medication names
      const prisma = getTestPrismaClient()
      const medications = [
        createTestMedicationData({ nombreProducto: 'PARACETAMOL' }),
        createTestMedicationData2({ nombreProducto: 'IBUPROFENO' }),
        createTestMedicationData3({ nombreProducto: 'AMOXICILINA' })
      ]

      for (let i = 0; i < medications.length; i++) {
        await prisma.medication_favorite.create({
          data: {
            user_id: testUsers.user1.id,
            cod_estab: medications[i].codEstab,
            cod_prod_e: medications[i].codProdE,
            medication_data: JSON.stringify(medications[i])
          }
        })
      }
    })

    describe('Successful search scenarios', () => {
      it('should search favorites by medication name', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications/search?q=PARACETAMOL', token)

        const data = await expectSuccessResponse(response, 200)
        validatePaginationResponse(data, 1, 20)
        expect(data.favorites.length).toBe(1)
        expect(data.favorites[0].medicationData.nombreProducto).toContain('PARACETAMOL')
      })

      it('should search favorites with partial name match', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications/search?q=PARA', token)

        const data = await expectSuccessResponse(response, 200)
        expect(data.favorites.length).toBe(1)
        expect(data.favorites[0].medicationData.nombreProducto).toContain('PARACETAMOL')
      })

      it('should search favorites case insensitively', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications/search?q=paracetamol', token)

        const data = await expectSuccessResponse(response, 200)
        expect(data.favorites.length).toBe(1)
        expect(data.favorites[0].medicationData.nombreProducto).toContain('PARACETAMOL')
      })

      it('should return empty results for non-matching search', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications/search?q=NONEXISTENT', token)

        const data = await expectSuccessResponse(response, 200)
        validatePaginationResponse(data, 1, 20)
        expect(data.favorites.length).toBe(0)
      })

      it('should search with pagination', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications/search?q=AM&page=1&limit=2', token)

        const data = await expectSuccessResponse(response, 200)
        validatePaginationResponse(data, 1, 2)
        expect(data.favorites.length).toBeLessThanOrEqual(2)
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject search without authentication token', async () => {
        const response = await makeRequest('GET', '/favorites/medications/search?q=PARACETAMOL')

        await expectAuthenticationError(response)
      })

      it('should reject search without query parameter', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications/search', token)

        await expectValidationError(response, "Property 'q' is missing")
      })

      it('should reject search with empty query', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications/search?q=', token)

        await expectValidationError(response, 'Search query is required')
      })

      it('should reject search with query too short', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('GET', '/favorites/medications/search?q=A', token)

        await expectValidationError(response, 'Search query must be at least 2 characters')
      })

      it('should reject search with query too long', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const longQuery = 'A'.repeat(101) // Assuming max length is 100

        const response = await makeAuthenticatedRequest('GET', `/favorites/medications/search?q=${longQuery}`, token)

        await expectValidationError(response, 'Search query cannot exceed 100 characters')
      })
    })
  })

  describe('DELETE /favorites/medications', () => {
    let testFavorite: any

    beforeEach(async () => {
      // Add a test favorite to remove
      const prisma = getTestPrismaClient()
      const medicationData = createTestMedicationData()

      testFavorite = await prisma.medication_favorite.create({
        data: {
          user_id: testUsers.user1.id,
          cod_estab: medicationData.codEstab,
          cod_prod_e: medicationData.codProdE,
          medication_data: JSON.stringify(medicationData)
        }
      })
    })

    describe('Successful removal scenarios', () => {
      it('should remove favorite by medication identifiers', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const medicationData = JSON.parse(testFavorite.medication_data)

        const response = await makeAuthenticatedRequest('DELETE', '/favorites/medications', token, {
          body: {
            codEstab: medicationData.codEstab,
            codProdE: medicationData.codProdE
          }
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.message).toBe('Medication removed from favorites')

        // Verify favorite was actually removed
        const prisma = getTestPrismaClient()
        const removedFavorite = await prisma.medication_favorite.findUnique({
          where: { id: testFavorite.id }
        })
        expect(removedFavorite).toBeNull()
      })
    })

    describe('Error scenarios', () => {
      it('should reject removal without authentication token', async () => {
        const medicationData = JSON.parse(testFavorite.medication_data)

        const response = await makeRequest('DELETE', '/favorites/medications', {
          body: {
            codEstab: medicationData.codEstab,
            codProdE: medicationData.codProdE
          }
        })

        await expectAuthenticationError(response)
      })

      it('should reject removal of non-existent favorite', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('DELETE', '/favorites/medications', token, {
          body: {
            codEstab: 'non-existent',
            codProdE: 99999
          }
        })

        await expectNotFoundError(response, 'Medication is not in favorites')
      })

      it('should reject removal with missing identifiers', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('DELETE', '/favorites/medications', token, {
          body: {
            codEstab: 'test'
            // Missing codProdE
          }
        })

        await expectValidationError(response)
      })
    })
  })

  describe('DELETE /favorites/medications/:id', () => {
    let testFavorite: any

    beforeEach(async () => {
      // Add a test favorite to remove
      const prisma = getTestPrismaClient()
      const medicationData = createTestMedicationData()

      testFavorite = await prisma.medication_favorite.create({
        data: {
          user_id: testUsers.user1.id,
          cod_estab: medicationData.codEstab,
          cod_prod_e: medicationData.codProdE,
          medication_data: JSON.stringify(medicationData)
        }
      })
    })

    describe('Successful removal scenarios', () => {
      it('should remove favorite by ID', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('DELETE', `/favorites/medications/${testFavorite.id}`, token)

        const data = await expectSuccessResponse(response, 200)
        expect(data.message).toBe('Medication removed from favorites')

        // Verify favorite was actually removed
        const prisma = getTestPrismaClient()
        const removedFavorite = await prisma.medication_favorite.findUnique({
          where: { id: testFavorite.id }
        })
        expect(removedFavorite).toBeNull()
      })
    })

    describe('Error scenarios', () => {
      it('should reject removal without authentication token', async () => {
        const response = await makeRequest('DELETE', `/favorites/medications/${testFavorite.id}`)

        await expectAuthenticationError(response)
      })

      it('should reject removal of non-existent favorite ID', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const nonExistentId = 'non-existent-id'

        const response = await makeAuthenticatedRequest('DELETE', `/favorites/medications/${nonExistentId}`, token)

        await expectValidationError(response, 'Invalid favorite ID format')
      })

      it('should reject removal of favorite belonging to different user', async () => {
        const token = generateTestAccessToken(testUsers.user2.id) // Different user

        const response = await makeAuthenticatedRequest('DELETE', `/favorites/medications/${testFavorite.id}`, token)

        await expectNotFoundError(response, 'Favorite not found or does not belong to user')
      })

      it('should reject removal with invalid favorite ID format', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidId = 'invalid-id-format'

        const response = await makeAuthenticatedRequest('DELETE', `/favorites/medications/${invalidId}`, token)

        await expectValidationError(response, 'Invalid favorite ID format')
      })
    })
  })

  describe('Cross-user isolation', () => {
    it('should not allow users to access other users favorites', async () => {
      // Add favorite for user1
      const prisma = getTestPrismaClient()
      const medicationData = createTestMedicationData()

      await prisma.medication_favorite.create({
        data: {
          user_id: testUsers.user1.id,
          cod_estab: medicationData.codEstab,
          cod_prod_e: medicationData.codProdE,
          medication_data: JSON.stringify(medicationData)
        }
      })

      // Try to list favorites as user2
      const token = generateTestAccessToken(testUsers.user2.id)
      const response = await makeAuthenticatedRequest('GET', '/favorites/medications', token)

      const data = await expectSuccessResponse(response, 200)
      expect(data.favorites.length).toBe(0) // Should not see user1's favorites
    })

    it('should not allow users to search other users favorites', async () => {
      // Add favorite for user1
      const prisma = getTestPrismaClient()
      const medicationData = createTestMedicationData({ nombreProducto: 'UNIQUE_MEDICATION' })

      await prisma.medication_favorite.create({
        data: {
          user_id: testUsers.user1.id,
          cod_estab: medicationData.codEstab,
          cod_prod_e: medicationData.codProdE,
          medication_data: JSON.stringify(medicationData)
        }
      })

      // Try to search as user2
      const token = generateTestAccessToken(testUsers.user2.id)
      const response = await makeAuthenticatedRequest('GET', '/favorites/medications/search?q=UNIQUE', token)

      const data = await expectSuccessResponse(response, 200)
      expect(data.favorites.length).toBe(0) // Should not find user1's favorites
    })
  })
})

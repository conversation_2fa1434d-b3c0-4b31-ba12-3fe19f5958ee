import { afterAll, beforeAll, beforeEach, describe, expect, it } from 'bun:test'
import {
  generateTestAccessToken,
  generateTestRefreshToken,
  testUsers
} from '../setup/auth.js'
import { cleanupTestDatabase, resetTestDatabase, seedTestDatabase, setupTestDatabase } from '../setup/database.js'
import { makeAuthenticatedRequest, makeRequest, startTestServer, stopTestServer } from '../setup/server.js'
import {
  createFirebaseLoginPayload,
  testHealthData,
  testLocationData,
  testUserPreferences,
} from '../utils/fixtures.js'
import {
  expectAuthenticationError,
  expectSuccessResponse,
  expectValidationError,
  generateRandomEmail,
  validateJWTTokens,
  validateUserResponse
} from '../utils/testHelpers.js'

describe('Users API Integration Tests', () => {
  beforeAll(async () => {
    await setupTestDatabase()
    await startTestServer()
  })

  afterAll(async () => {
    await stopTestServer()
    await cleanupTestDatabase()
  })

  beforeEach(async () => {
    await resetTestDatabase()
    await seedTestDatabase()
  })

  describe('POST /users/firebase/login', () => {
    describe('Successful login scenarios', () => {
      it('should login existing user with valid Firebase credentials', async () => {
        const loginPayload = createFirebaseLoginPayload({
          firebaseId: testUsers.user1.id,
          email: testUsers.user1.email,
          name: testUsers.user1.name,
          firebaseToken: testUsers.user1.firebaseToken
        })

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        const data = await expectSuccessResponse(response, 200)
        validateJWTTokens(data)
        expect(data.user).toBeDefined()
        validateUserResponse(data.user)
        expect(data.user.email).toBe(loginPayload.email)
      })

      it('should create new user and login with valid Firebase credentials', async () => {
        const loginPayload = createFirebaseLoginPayload({
          email: generateRandomEmail(),
          name: 'Brand New User'
        })

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        const data = await expectSuccessResponse(response, 201)
        validateJWTTokens(data)
        expect(data.user).toBeDefined()
        validateUserResponse(data.user)
        expect(data.user.email).toBe(loginPayload.email)
        expect(data.user.name).toBe(loginPayload.name)
      })
    })

    describe('Validation error scenarios', () => {
      it('should reject login with missing firebaseId', async () => {
        const loginPayload = createFirebaseLoginPayload()
        delete loginPayload.firebaseId

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        await expectValidationError(response)
      })

      it('should reject login with missing email', async () => {
        const loginPayload = createFirebaseLoginPayload()
        delete loginPayload.email

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        await expectValidationError(response)
      })

      it('should reject login with invalid email format', async () => {
        const loginPayload = createFirebaseLoginPayload({
          email: 'invalid-email'
        })

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        await expectValidationError(response)
      })

      it('should reject login with missing firebaseToken', async () => {
        const loginPayload = createFirebaseLoginPayload()
        delete loginPayload.firebaseToken

        const response = await makeRequest('POST', '/users/firebase/login', {
          body: loginPayload
        })

        await expectValidationError(response)
      })
    })
  })

  describe('POST /users/refresh-token', () => {
    describe('Successful refresh scenarios', () => {
      it('should refresh access token with valid refresh token', async () => {
        const refreshToken = generateTestRefreshToken(testUsers.user1.id)

        const response = await makeRequest('POST', '/users/refresh-token', {
          body: { refreshToken }
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.accessToken).toBeDefined()
        expect(typeof data.accessToken).toBe('string')
        expect(data.accessToken.length).toBeGreaterThan(0)
      })
    })

    describe('Error scenarios', () => {
      it('should reject refresh with missing refresh token', async () => {
        const response = await makeRequest('POST', '/users/refresh-token', {
          body: {}
        })

        await expectValidationError(response)
      })

      it('should reject refresh with invalid refresh token', async () => {
        const response = await makeRequest('POST', '/users/refresh-token', {
          body: { refreshToken: 'invalid-token' }
        })

        await expectAuthenticationError(response)
      })

      it('should reject refresh with expired refresh token', async () => {
        // Create an expired token (this would need JWT with past expiry)
        const expiredToken = 'expired.jwt.token'

        const response = await makeRequest('POST', '/users/refresh-token', {
          body: { refreshToken: expiredToken }
        })

        await expectAuthenticationError(response)
      })
    })
  })

  describe('PUT /users/preferences', () => {
    describe('Successful update scenarios', () => {
      it('should update user preferences with valid data', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('PUT', '/users/preferences', token, {
          body: testUserPreferences
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.preferences).toEqual(testUserPreferences)
      })

      it('should update preferences with partial data', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const partialPreferences = {
          language: 'en',
          theme: 'dark' as const,
          notifications: false
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/preferences', token, {
          body: partialPreferences
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.preferences.language).toBe('en')
        expect(data.preferences.theme).toBe('dark')
        expect(data.preferences.notifications).toBe(false)
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject update without authentication token', async () => {
        const response = await makeRequest('PUT', '/users/preferences', {
          body: testUserPreferences
        })

        await expectAuthenticationError(response)
      })

      it('should reject update with invalid authentication token', async () => {
        const response = await makeAuthenticatedRequest('PUT', '/users/preferences', 'invalid-token', {
          body: testUserPreferences
        })

        await expectAuthenticationError(response)
      })

      it('should reject update with invalid theme value', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidPreferences = {
          ...testUserPreferences,
          theme: 'invalid-theme'
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/preferences', token, {
          body: invalidPreferences
        })

        await expectValidationError(response)
      })
    })
  })

  describe('PUT /users/health-data', () => {
    describe('Successful update scenarios', () => {
      it('should update user health data with valid data', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('PUT', '/users/health-data', token, {
          body: testHealthData
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.healthData).toEqual(testHealthData)
      })

      it('should update health data with empty arrays', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const emptyHealthData = {
          allergies: [],
          conditions: [],
          medications: []
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/health-data', token, {
          body: emptyHealthData
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.healthData).toEqual(emptyHealthData)
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject update without authentication token', async () => {
        const response = await makeRequest('PUT', '/users/health-data', {
          body: testHealthData
        })

        await expectAuthenticationError(response)
      })

      it('should reject update with invalid medication structure', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidHealthData = {
          allergies: ['penicillin'],
          conditions: ['diabetes'],
          medications: [
            {
              name: 'Metformin'
              // Missing dosage and frequency
            }
          ]
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/health-data', token, {
          body: invalidHealthData
        })

        await expectValidationError(response)
      })
    })
  })

  describe('PUT /users/location', () => {
    describe('Successful update scenarios', () => {
      it('should update user location with valid coordinates', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('PUT', '/users/location', token, {
          body: testLocationData
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.currentLocation).toEqual(testLocationData)
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject update without authentication token', async () => {
        const response = await makeRequest('PUT', '/users/location', {
          body: testLocationData
        })

        await expectAuthenticationError(response)
      })

      it('should reject update with invalid latitude', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidLocation = {
          latitude: 91, // Invalid latitude (> 90)
          longitude: -77.0428
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/location', token, {
          body: invalidLocation
        })

        await expectValidationError(response)
      })

      it('should reject update with invalid longitude', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const invalidLocation = {
          latitude: -12.0464,
          longitude: 181 // Invalid longitude (> 180)
        }

        const response = await makeAuthenticatedRequest('PUT', '/users/location', token, {
          body: invalidLocation
        })

        await expectValidationError(response)
      })
    })
  })

  describe('PUT /users/profile-picture', () => {
    describe('Successful update scenarios', () => {
      it('should update profile picture URL with valid URL', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)
        const profilePictureUrl = 'https://example.com/new-avatar.jpg'

        const response = await makeAuthenticatedRequest('PUT', '/users/profile-picture', token, {
          body: { profilePictureUrl }
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.profilePictureUrl).toBe(profilePictureUrl)
      })

      it('should remove profile picture with null URL', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('PUT', '/users/profile-picture', token, {
          body: { profilePictureUrl: null }
        })

        const data = await expectSuccessResponse(response, 200)
        expect(data.profilePictureUrl).toBeNull()
      })
    })

    describe('Authentication and validation errors', () => {
      it('should reject update without authentication token', async () => {
        const response = await makeRequest('PUT', '/users/profile-picture', {
          body: { profilePictureUrl: 'https://example.com/avatar.jpg' }
        })

        await expectAuthenticationError(response)
      })

      it('should reject update with invalid URL format', async () => {
        const token = generateTestAccessToken(testUsers.user1.id)

        const response = await makeAuthenticatedRequest('PUT', '/users/profile-picture', token, {
          body: { profilePictureUrl: 'invalid-url' }
        })

        await expectValidationError(response)
      })
    })
  })
})

// --- Start of new describe block for DELETE /users/delete ---
import { PrismaClient } from '@prisma/client' // Added import
const prisma = new PrismaClient() // Added prisma client instance

describe('DELETE /users/delete - User Account Deletion', () => {
  let userId: string
  let authToken: string
  // firebaseTestUser is not directly used from the provided snippet, userId/authToken are key

  beforeEach(async () => {
    // Create a new unique user for each test in this block to ensure test isolation for deletion
    // Adapting from the placeholder `createUserAndGetToken`
    // We'll use a unique email to ensure a new user is created.
    const uniqueEmail = generateRandomEmail()
    const firebaseId = `firebase-test-uid-${Date.now()}`
    const name = 'Test Delete User'
    const profilePictureUrl = 'http://example.com/delete_pic.jpg'
    const firebaseToken = 'test-firebase-delete-token'

    // 1. Create user via login (or a direct DB creation utility if one existed)
    const loginPayload = createFirebaseLoginPayload({
      firebaseId,
      email: uniqueEmail,
      name,
      profilePictureUrl,
      firebaseToken
    })

    const loginResponse = await makeRequest('POST', '/users/firebase/login', {
      body: loginPayload
    })
    const loginData = await loginResponse.json() // Don't use expectSuccessResponse here as we need the data directly

    if (!loginData || !loginData.user || !loginData.tokens || !loginData.tokens.accessToken) {
      throw new Error('Failed to create user and get token in beforeEach for delete tests. Response: ' + JSON.stringify(loginData))
    }

    userId = loginData.user.id
    authToken = loginData.tokens.accessToken

    // Verify user exists in DB (optional sanity check)
    const userInDb = await prisma.user.findUnique({ where: { id: userId } })
    if (!userInDb) {
      throw new Error(`User with id ${userId} was not created in the database during beforeEach.`)
    }
  })

  it('should allow a user to delete their own account with a valid token', async () => {
    const response = await makeAuthenticatedRequest('DELETE', `/users/delete`, authToken)
    const responseBody = await response.json()

    // Check response status and message
    // expectSuccessResponse would be ideal if it can check non-200/201, e.g. 200 for delete
    expect(response.status).toBe(200) // As per current router code
    expect(responseBody.message).toBe('User account deleted successfully.')

    // Verify the user is actually deleted from the database
    const deletedUser = await prisma.user.findUnique({
      where: { id: userId }
    })
    expect(deletedUser).toBeNull()
    userId = '' // Mark as deleted so afterEach doesn't try to delete again
  })

  it('should return 401 Unauthorized if no token is provided', async () => {
    const response = await makeRequest('DELETE', `/users/delete`)
    // Use existing utility if possible
    await expectAuthenticationError(response) // expectAuthenticationError usually checks for 401

    // Manual check if expectAuthenticationError is too specific or doesn't match message
    const responseBody = await response.json()
    expect(response.status).toBe(401)
    expect(responseBody.message).toBe('Authorization token is missing or invalid.')
  })

  it('should return 500 Internal Server Error if an invalid token is provided (e.g. bad signature)', async () => {
    // This tests the scenario where jwt.verify throws an error that is not "User not found"
    const response = await makeAuthenticatedRequest('DELETE', `/users/delete`, 'invalid-token-123')
    const responseBody = await response.json()

    expect(response.status).toBe(500)
    expect(responseBody.message).toBe('Failed to delete user account.')
  })

  it('should return 404 Not Found if attempting to delete a user that does not exist (e.g. already deleted or token for non-existent user)', async () => {
    // First, delete the user
    await makeAuthenticatedRequest('DELETE', `/users/delete`, authToken)
    userId = '' // Mark as deleted

    // Then, attempt to delete the same user again
    const response = await makeAuthenticatedRequest('DELETE', `/users/delete`, authToken) // Token is still valid format, but user ID it points to is gone
    const responseBody = await response.json()

    expect(response.status).toBe(404)
    expect(responseBody.message).toBe('User not found or token invalid.')
  })
})

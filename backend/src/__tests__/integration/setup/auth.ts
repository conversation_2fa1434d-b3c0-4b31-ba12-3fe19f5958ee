import jwt from 'jsonwebtoken'

const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key'
const JWT_REFRESH_SECRET = process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key'
const JWT_EXPIRES_IN = '1d' // 1 day for tests

export interface TestUser {
  id: string
  email: string
  name: string
  firebaseToken: string
}

export const testUsers: Record<string, TestUser> = {
  user1: {
    id: 'test-user-1',
    email: '<EMAIL>',
    name: 'Test User 1',
    firebaseToken: 'firebase-token-1'
  },
  user2: {
    id: 'test-user-2',
    email: '<EMAIL>',
    name: 'Test User 2',
    firebaseToken: 'firebase-token-2'
  }
}

export const generateTestAccessToken = (userId: string): string => {
  try {
    const token = jwt.sign({ data: userId }, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN })
    return token
  } catch (error) {
    console.error('❌ Failed to generate test access token:', error)
    throw error
  }
}

export const generateTestRefreshToken = (userId: string): string => {
  try {
    const token = jwt.sign({ data: userId }, JWT_REFRESH_SECRET, { expiresIn: '7d' })
    return token
  } catch (error) {
    console.error('❌ Failed to generate test refresh token:', error)
    throw error
  }
}

export const verifyTestToken = (token: string): { data: string } => {
  try {
    const decoded = jwt.verify(token, JWT_SECRET) as jwt.JwtPayload & { data: string }
    return { data: decoded.data }
  } catch (error) {
    console.error('❌ Failed to verify test token:', error)
    throw error
  }
}

export const getTestAuthHeaders = (userId: string): Record<string, string> => {
  const token = generateTestAccessToken(userId)
  return {
    Authorization: `Bearer ${token}`
  }
}

export const createTestLoginPayload = (userKey: keyof typeof testUsers) => {
  const user = testUsers[userKey]
  return {
    firebaseId: user.id,
    email: user.email,
    name: user.name,
    profilePictureUrl: `https://example.com/avatar/${user.id}.jpg`,
    firebaseToken: user.firebaseToken
  }
}

// Mock Firebase token verification for tests
export const mockFirebaseVerification = () => {
  // In a real implementation, you might want to mock the Firebase admin SDK
  // For now, we'll assume Firebase tokens are valid in tests
  return true
}

export const getTestUserPreferences = () => ({
  language: 'es',
  theme: 'light' as const,
  notifications: true,
  defaultLocation: {
    departmentCode: '15',
    provinceCode: '01',
    districtCode: '01'
  }
})

export const getTestHealthData = () => ({
  allergies: ['penicillin', 'shellfish'],
  conditions: ['diabetes', 'hypertension'],
  medications: [
    {
      name: 'Metformin',
      dosage: '500mg',
      frequency: 'twice daily'
    },
    {
      name: 'Lisinopril',
      dosage: '10mg',
      frequency: 'once daily'
    }
  ]
})

export const getTestLocationData = () => ({
  latitude: -12.0464,
  longitude: -77.0428
})

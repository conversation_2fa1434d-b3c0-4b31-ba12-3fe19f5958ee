import { Elysia } from 'elysia'
import { getTestPrismaClient } from './database.js'
import { createTestDependencies } from './testDependencies.js'

// Local type definitions to avoid deep relative imports
interface TestServerOptions {
  body?: unknown
  headers?: Record<string, string>
  port?: number
}

let testServer: any = null
let testServerInstance: any = null

export const createTestServer = (): any => {
  if (testServer) {
    return testServer
  }

  // Create test server with test dependencies
  const testPrismaClient = getTestPrismaClient()
  const { favoritesRouter, userRouter, legalRouter } = createTestDependencies(testPrismaClient)

  // Create Elysia server with test configuration
  testServer = new Elysia()
    .derive(({ headers }: any) => {
      try {
        // Handle both 'authorization' and 'Authorization' headers
        const auth = headers?.['authorization'] ?? headers?.['Authorization']

        if (auth && typeof auth === 'string' && auth.startsWith('Bearer ')) {
          return {
            token: auth.slice(7)
          }
        }

        return {
          token: null
        }
      } catch (error) {
        console.error('Error processing authorization header:', error)
        return {
          token: null
        }
      }
    })
    .onError(({ code, error, set }: any) => {
      // Handle custom error types
      if ((error as any).type === 'AUTHENTICATION_ERROR') {
        if (set) set.status = 401
        return { status: 401, message: (error as any).message }
      }

      if ((error as any).type === 'VALIDATION_ERROR') {
        if (set) set.status = 400
        return { status: 400, message: (error as any).message }
      }

      if ((error as any).type === 'CONFLICT_ERROR') {
        if (set) set.status = 409
        return { status: 409, message: (error as any).message }
      }

      if ((error as any).type === 'NOT_FOUND_ERROR') {
        if (set) set.status = 404
        return { status: 404, message: (error as any).message }
      }

      // Handle validation errors
      if (code === 'VALIDATION') {
        if (set) set.status = 400
        return { status: 400, message: (error as any).message || 'Validation Error' }
      }

      // Handle parse errors
      if (code === 'PARSE') {
        if (set) set.status = 400
        return { status: 400, message: 'Invalid request format' }
      }

      // Handle not found
      if (code === 'NOT_FOUND') {
        if (set) set.status = 404
        return { status: 404, message: 'Not Found' }
      }

      // Default error handling
      console.error('Unhandled error:', error)
      if (set) set.status = 500
      return { status: 500, message: 'Internal server error' }
    })
    .use(legalRouter)
    .group('/api/v1', (app: any) => app.use(userRouter).use(favoritesRouter))

  return testServer
}

export const startTestServer = async (port: number = 3001): Promise<void> => {
  if (testServerInstance) {
    return
  }

  const server = createTestServer()

  return new Promise((resolve, reject) => {
    try {
      testServerInstance = server.listen(port, () => {
        console.log(`✅ Test server started on port ${port}`)
        resolve()
      })
    } catch (error) {
      console.error('❌ Failed to start test server:', error)
      reject(error)
    }
  })
}

export const stopTestServer = async (): Promise<void> => {
  if (testServerInstance) {
    try {
      ;(testServerInstance as any).stop()
      testServerInstance = null
      testServer = null
      console.log('✅ Test server stopped')
    } catch (error) {
      console.error('❌ Failed to stop test server:', error)
      throw error
    }
  }
}

export const getTestServerUrl = (port: number = 3001): string => {
  return `http://localhost:${port}`
}

// Helper to make HTTP requests to test server
export const makeRequest = async (
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  endpoint: string,
  options: TestServerOptions = {}
): Promise<Response> => {
  const { body, headers = {}, port = 3001 } = options

  // Determine if this is an API endpoint or root-level endpoint
  const isApiEndpoint = endpoint.startsWith('/users') || endpoint.startsWith('/favorites')
  const baseUrl = getTestServerUrl(port)
  const url = isApiEndpoint ? `${baseUrl}/api/v1${endpoint}` : `${baseUrl}${endpoint}`

  const requestOptions: RequestInit = {
    method,
    headers: {
      'Content-Type': 'application/json',
      ...headers
    }
  }

  if (body && (method === 'POST' || method === 'PUT' || method === 'DELETE')) {
    requestOptions.body = JSON.stringify(body)
  }

  try {
    const response = await fetch(url, requestOptions)
    return response
  } catch (error) {
    console.error(`❌ Request failed: ${method} ${url}`, error)
    throw error
  }
}

// Helper to make authenticated requests
export const makeAuthenticatedRequest = async (
  method: 'GET' | 'POST' | 'PUT' | 'DELETE',
  endpoint: string,
  token: string,
  options: TestServerOptions = {}
): Promise<Response> => {
  const { headers = {}, ...restOptions } = options

  return makeRequest(method, endpoint, {
    ...restOptions,
    headers: {
      ...headers,
      Authorization: `Bearer ${token}`
    }
  })
}

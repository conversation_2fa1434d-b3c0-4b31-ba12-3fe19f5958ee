import { afterAll, beforeAll, describe, expect, it } from 'bun:test'
import { cleanupTestDatabase, getTestPrismaClient, resetTestDatabase, seedTestDatabase, setupTestDatabase } from './database.js'

describe('Database Setup Integration Test', () => {
  beforeAll(async () => {
    await setupTestDatabase()
  })

  afterAll(async () => {
    await cleanupTestDatabase()
  })

  it('should setup test database successfully', async () => {
    const prisma = getTestPrismaClient()
    expect(prisma).toBeDefined()
  })

  it('should seed and reset test database', async () => {
    await seedTestDatabase()

    const prisma = getTestPrismaClient()
    const users = await prisma.user.findMany()
    expect(users.length).toBeGreaterThan(0)

    await resetTestDatabase()
    const usersAfterReset = await prisma.user.findMany()
    expect(usersAfterReset.length).toBe(0)
  })

  it('should create and query test data', async () => {
    const prisma = getTestPrismaClient()

    // Create test user
    const testUser = await prisma.user.create({
      data: {
        id: 'test-user-integration',
        name: 'Integration Test User',
        email: '<EMAIL>',
        firebase_token: 'test-token',
        preferences: JSON.stringify({ language: 'en', theme: 'light', notifications: true }),
        health_data: JSON.stringify({ allergies: [], conditions: [], medications: [] })
      }
    })

    expect(testUser).toBeDefined()
    expect(testUser.id).toBe('test-user-integration')
    expect(testUser.email).toBe('<EMAIL>')

    // Create test medication favorite
    const testFavorite = await prisma.medication_favorite.create({
      data: {
        user_id: testUser.id,
        cod_estab: 'TEST001',
        cod_prod_e: 12345,
        medication_data: JSON.stringify({
          codEstab: 'TEST001',
          codProdE: 12345,
          nombreProducto: 'TEST MEDICATION',
          precio1: 10.0
        })
      }
    })

    expect(testFavorite).toBeDefined()
    expect(testFavorite.user_id).toBe(testUser.id)
    expect(testFavorite.cod_estab).toBe('TEST001')

    // Query with relationship
    const userWithFavorites = await prisma.user.findUnique({
      where: { id: testUser.id },
      include: { medication_favorites: true }
    })

    expect(userWithFavorites).toBeDefined()
    expect(userWithFavorites?.medication_favorites.length).toBe(1)
  })
})

import { InternalServerError, NotFoundError, handleJWTError, validateTokenPresence } from '../../../server/errors/index.js'
import { LoginUser } from '../../application/login.js'
import type { HealthData, Point, UserPreferences } from '../../domain/User.js'

type FirebaseLoginRequest = {
  body: {
    firebaseId: string
    email: string
    name: string
    profilePictureUrl: string
    firebaseToken: string
  }
}

type AuthenticatedRequest = {
  token: string | null
}

type UpdatePreferencesRequest = AuthenticatedRequest & {
  body: UserPreferences
}

type UpdateHealthDataRequest = AuthenticatedRequest & {
  body: HealthData
}

type UpdateLocationRequest = AuthenticatedRequest & {
  body: Point
}

type UpdateProfilePictureRequest = AuthenticatedRequest & {
  body: {
    profilePictureUrl: string | null
  }
}

export class LoginController {
  constructor(private readonly loginUser: LoginUser) {}

  async loginWithFirebase({ body }: FirebaseLoginRequest) {
    try {
      const result = await this.loginUser.loginWithFirebase(body.firebaseId, body.email, body.name, body.profilePictureUrl, body.firebaseToken)
      const userData = result.user.toJSON()

      return {
        status: result.isNewUser ? 201 : 200,
        data: {
          user: {
            id: userData.id,
            name: userData.name,
            email: userData.email,
            profilePictureUrl: userData.profilePictureUrl,
            preferences: userData.preferences,
            healthData: userData.healthData,
            currentLocation: userData.currentLocation
          },
          accessToken: userData.accessToken,
          refreshToken: userData.refreshToken
        }
      }
    } catch (error) {
      // All errors in login are treated as internal server errors
      // unless they're specific validation or authentication errors
      throw new InternalServerError((error as Error).message)
    }
  }

  async refreshToken({ body }: { body: { refreshToken: string } }) {
    try {
      const accessToken = await this.loginUser.refreshAccessToken(body.refreshToken)
      return {
        status: 200,
        data: { accessToken }
      }
    } catch (error) {
      // Handle JWT errors using centralized error handler
      handleJWTError(error)
    }
  }

  async updatePreferences({ body, token }: UpdatePreferencesRequest) {
    validateTokenPresence(token)

    try {
      const user = await this.loginUser.updatePreferences(token, body)
      return {
        status: 200,
        data: user.toJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message === 'User not found') {
        throw new NotFoundError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async updateHealthData({ body, token }: UpdateHealthDataRequest) {
    validateTokenPresence(token)

    try {
      const user = await this.loginUser.updateHealthData(token, body)
      return {
        status: 200,
        data: user.toJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message === 'User not found') {
        throw new NotFoundError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async updateLocation({ body, token }: UpdateLocationRequest) {
    validateTokenPresence(token)

    try {
      const user = await this.loginUser.updateLocation(token, body)
      return {
        status: 200,
        data: user.toJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message === 'User not found') {
        throw new NotFoundError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async updateProfilePicture({ body, token }: UpdateProfilePictureRequest) {
    validateTokenPresence(token)

    try {
      const user = await this.loginUser.updateProfilePicture(token, body.profilePictureUrl)
      return {
        status: 200,
        data: user.toJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message === 'User not found') {
        throw new NotFoundError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async getProfile({ token }: AuthenticatedRequest) {
    validateTokenPresence(token)

    try {
      const user = await this.loginUser.getProfile(token)
      return {
        status: 200,
        data: user.toJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message === 'User not found') {
        throw new NotFoundError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async deleteUser({ token }: AuthenticatedRequest) { // Using existing AuthenticatedRequest type
    validateTokenPresence(token) // Re-use existing token validation

    try {
      await this.loginUser.deleteUser(token)
      // For DELETE operations, typically a 200 OK with a message or 204 No Content is returned.
      // The router is currently handling the response construction.
      // This controller method can simply ensure the operation completes or throws.
      // The router will then set the status code and message.
      // So, no explicit return here if successful, or just return a simple success object if preferred.
      // Let's align with how router calls it: router expects promise to resolve or reject.
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message.toLowerCase().includes('user not found')) { // Make case-insensitive for robustness
        throw new NotFoundError('User not found or token invalid.') // More specific message for client
      }

      // Handle JWT specific errors (though deleteUser itself might not directly throw JWTError if token was already used by getUserFromToken)
      // However, if getUserFromToken (called by loginUser.deleteUser) throws a JWT specific error that wasn't "User not found"
      handleJWTError(error) // This will re-throw specific JWT errors or a generic one

      // Fallback for other unexpected errors
      throw new InternalServerError('An unexpected error occurred while deleting the user account.')
    }
  }
}

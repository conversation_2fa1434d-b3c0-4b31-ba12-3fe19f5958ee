import { afterEach, beforeEach, describe, expect, it, mock } from 'bun:test'
import type { LoginUser } from '../../application/login.js'
import { LoginController } from './login.js'

// Mock types for better type safety
interface MockLoginUser {
  loginWithFirebase: ReturnType<typeof mock>
  refreshAccessToken: ReturnType<typeof mock>
  updatePreferences: ReturnType<typeof mock>
  updateHealthData: ReturnType<typeof mock>
  updateLocation: ReturnType<typeof mock>
  updateProfilePicture: ReturnType<typeof mock>
  getProfile: ReturnType<typeof mock>
}

describe('LoginController', () => {
  let loginController: LoginController
  let mockLoginUser: MockLoginUser

  beforeEach(() => {
    mockLoginUser = {
      loginWithFirebase: mock(),
      refreshAccessToken: mock(),
      updatePreferences: mock(),
      updateHealthData: mock(),
      updateLocation: mock(),
      updateProfilePicture: mock(),
      getProfile: mock()
    }

    loginController = new LoginController(mockLoginUser as unknown as LoginUser)
  })

  afterEach(() => {
    // Clean up after each test
    Object.values(mockLoginUser).forEach((mockFn) => {
      if (typeof mockFn === 'function' && 'mockClear' in mockFn) {
        mockFn.mockClear()
      }
    })
  })

  describe('loginWithFirebase', () => {
    it('should handle Firebase login successfully', async () => {
      const loginData = {
        firebaseId: 'firebase-user-123',
        email: '<EMAIL>',
        name: 'Test User',
        profilePictureUrl: 'https://example.com/avatar.jpg',
        firebaseToken: 'firebase-token-123'
      }

      const mockUser = {
        toJSON: () => ({
          id: 'user-123',
          name: 'Test User',
          email: '<EMAIL>',
          profilePictureUrl: 'https://example.com/avatar.jpg',
          preferences: {},
          healthData: {},
          currentLocation: null,
          accessToken: 'access-token',
          refreshToken: process.env.TEST_REFRESH_TOKEN || 'mock-refresh-token'
        })
      }

      const mockResult = {
        user: mockUser,
        isNewUser: false
      }

      mockLoginUser.loginWithFirebase.mockResolvedValue(mockResult)

      const result = await loginController.loginWithFirebase({
        body: loginData
      })

      expect(result.status).toBe(200)
      expect(result.data.user.id).toBe('user-123')
      expect(result.data.accessToken).toBe('access-token')
      expect(result.data.refreshToken).toBe(process.env.TEST_REFRESH_TOKEN || 'mock-refresh-token')
      expect(mockLoginUser.loginWithFirebase).toHaveBeenCalledWith(
        loginData.firebaseId,
        loginData.email,
        loginData.name,
        loginData.profilePictureUrl,
        loginData.firebaseToken
      )
    })

    it('should return 201 for new users', async () => {
      const loginData = {
        firebaseId: 'firebase-user-123',
        email: '<EMAIL>',
        name: 'Test User',
        profilePictureUrl: 'https://example.com/avatar.jpg',
        firebaseToken: 'firebase-token-123'
      }

      const mockUser = {
        toJSON: () => ({
          id: 'user-123',
          name: 'Test User',
          email: '<EMAIL>',
          profilePictureUrl: 'https://example.com/avatar.jpg',
          preferences: {},
          healthData: {},
          currentLocation: null,
          accessToken: 'access-token',
          refreshToken: process.env.TEST_REFRESH_TOKEN || 'mock-refresh-token'
        })
      }

      const mockResult = {
        user: mockUser,
        isNewUser: true
      }

      mockLoginUser.loginWithFirebase.mockResolvedValue(mockResult)

      const result = await loginController.loginWithFirebase({
        body: loginData
      })

      expect(result.status).toBe(201)
    })
  })

  describe('refreshToken', () => {
    it('should refresh token successfully', async () => {
      const refreshData = {
        refreshToken: 'valid-refresh-token'
      }

      mockLoginUser.refreshAccessToken.mockResolvedValue('new-access-token')

      const result = await loginController.refreshToken({
        body: refreshData
      })

      expect(result.status).toBe(200)
      expect(result.data.accessToken).toBe('new-access-token')
      expect(mockLoginUser.refreshAccessToken).toHaveBeenCalledWith(refreshData.refreshToken)
    })

    it('should handle JWT errors properly', async () => {
      const refreshData = {
        refreshToken: 'invalid-token'
      }

      const jwtError = new Error('Invalid token format')
      mockLoginUser.refreshAccessToken.mockRejectedValue(jwtError)

      await expect(
        loginController.refreshToken({
          body: refreshData
        })
      ).rejects.toThrow()
    })
  })
})

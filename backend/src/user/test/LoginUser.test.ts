import { type Mock, afterEach, beforeEach, describe, expect, it, mock } from 'bun:test'
import { JwtPayload } from 'jsonwebtoken'

import { IJWT, TokenPair } from '../../services/interfaces/IJWT.js'
import { LoginUser } from '../application/login.js'

import { IUser } from '../domain/IUser.js'
import { HealthData, Point, User, UserPreferences } from '../domain/User.js'

// Utility type for mocked interfaces
type Mocked<T> = {
  [K in keyof T]: T[K] extends (...args: any[]) => any ? Mock<T[K]> : T[K]
}

// Mocks
const mockUserRepository: Mocked<IUser> = {
  create: mock() as unknown as Mock<IUser['create']>,
  findById: mock() as unknown as Mock<IUser['findById']>,
  findByEmail: mock() as unknown as Mo<PERSON><IUser['findByEmail']>,
  updatePreferences: mock() as unknown as Mock<IUser['updatePreferences']>,
  updateHealthData: mock() as unknown as Mock<IUser['updateHealthData']>,
  updateLocation: mock() as unknown as Mock<IUser['updateLocation']>,
  updateProfilePicture: mock() as unknown as Mock<IUser['updateProfilePicture']>,
  updateFirebaseToken: mock() as unknown as Mock<IUser['updateFirebaseToken']>,
  updateRefreshToken: mock() as unknown as Mock<IUser['updateRefreshToken']>,
  findOrCreateFromFirebase: mock() as unknown as Mock<IUser['findOrCreateFromFirebase']>,
  deleteUser: mock() as unknown as Mock<IUser['deleteUser']>
}

const mockJwtService: Mocked<IJWT> = {
  sign: mock() as unknown as Mock<IJWT['sign']>,
  verify: mock(() => ({ data: 'test-user-id' }) as JwtPayload) as unknown as Mock<IJWT['verify']>,
  verifyRefresh: mock(() => ({ data: 'test-user-id' }) as JwtPayload) as unknown as Mock<IJWT['verifyRefresh']>,
  refreshAccessToken: mock() as unknown as Mock<IJWT['refreshAccessToken']>
}

describe('LoginUser Service', () => {
  let loginUser: LoginUser
  const testUserId = 'test-user-id'
  const testEmail = '<EMAIL>'
  const testName = 'Test User'
  const testProfilePic = 'http://example.com/pic.jpg'
  const testFirebaseToken = 'fb-token'
  const mockAccessToken = 'mock-access-token'
  const mockRefreshToken = 'mock-refresh-token'
  const mockTokens: TokenPair = { accessToken: mockAccessToken, refreshToken: mockRefreshToken }

  beforeEach(() => {
    // Reset mocks before each test
    mockUserRepository.create.mockReset()
    mockUserRepository.findById.mockReset()
    mockUserRepository.findByEmail.mockReset()
    mockUserRepository.updatePreferences.mockReset()
    mockUserRepository.updateHealthData.mockReset()
    mockUserRepository.updateLocation.mockReset()
    mockUserRepository.updateProfilePicture.mockReset()
    mockUserRepository.updateFirebaseToken.mockReset()
    mockUserRepository.updateRefreshToken.mockReset()
    mockUserRepository.findOrCreateFromFirebase.mockReset()
    mockUserRepository.deleteUser.mockReset()

    mockJwtService.sign.mockReset()
    mockJwtService.verify.mockReset()
    mockJwtService.verifyRefresh.mockReset()
    mockJwtService.refreshAccessToken.mockReset()

    loginUser = new LoginUser(mockUserRepository, mockJwtService)
  })

  afterEach(() => {
    // Clean up after each test
    loginUser = null as any
  })

  describe('loginWithFirebase', () => {
    it('should login/create user and return user with tokens', async () => {
      const mockUser = new User(testUserId, testName, testEmail, testFirebaseToken)
      mockUserRepository.findOrCreateFromFirebase.mockResolvedValue({ user: mockUser, isNewUser: false })
      mockJwtService.sign.mockResolvedValue(mockTokens)
      mockUserRepository.updateRefreshToken.mockResolvedValue(mockUser) // Assume it returns the user

      const result = await loginUser.loginWithFirebase(testUserId, testEmail, testName, testProfilePic, testFirebaseToken)

      expect(mockUserRepository.findOrCreateFromFirebase).toHaveBeenCalledWith(testUserId, testEmail, testName, testProfilePic, testFirebaseToken)
      expect(mockJwtService.sign).toHaveBeenCalledWith(testUserId)
      expect(mockUserRepository.updateRefreshToken).toHaveBeenCalledWith(testUserId, mockRefreshToken)
      expect(result.user.id).toBe(testUserId)
      expect(result.user.getRefreshToken()).toBe(mockRefreshToken)
      // accessToken is private, but toJSON should reveal it
      expect(result.user.toJSON().accessToken).toBe(mockAccessToken)
      expect(result.isNewUser).toBe(false)
    })
  })

  describe('refreshAccessToken', () => {
    it('should refresh access token successfully', async () => {
      const mockUser = new User(testUserId, testName, testEmail, testFirebaseToken, undefined, undefined, undefined, undefined, mockRefreshToken)
      mockJwtService.verifyRefresh.mockReturnValue({ data: testUserId } as JwtPayload)
      mockUserRepository.findById.mockResolvedValue(mockUser)
      mockJwtService.refreshAccessToken.mockResolvedValue(mockAccessToken) // New access token

      const newAccessToken = await loginUser.refreshAccessToken(mockRefreshToken)

      expect(mockJwtService.verifyRefresh).toHaveBeenCalledWith(mockRefreshToken)
      expect(mockUserRepository.findById).toHaveBeenCalledWith(testUserId)
      expect(mockJwtService.refreshAccessToken).toHaveBeenCalledWith(mockRefreshToken)
      expect(newAccessToken).toBe(mockAccessToken)
    })

    it('should throw if user not found during refresh', async () => {
      mockJwtService.verifyRefresh.mockReturnValue({ data: testUserId } as JwtPayload)
      mockUserRepository.findById.mockResolvedValue(null)

      await expect(loginUser.refreshAccessToken(mockRefreshToken)).rejects.toThrow('User not found')
    })

    it('should throw if stored refresh token does not match', async () => {
      const mockUser = new User(
        testUserId,
        testName,
        testEmail,
        testFirebaseToken,
        undefined,
        undefined,
        undefined,
        undefined,
        'different-refresh-token'
      )
      mockJwtService.verifyRefresh.mockReturnValue({ data: testUserId } as JwtPayload)
      mockUserRepository.findById.mockResolvedValue(mockUser)

      await expect(loginUser.refreshAccessToken(mockRefreshToken)).rejects.toThrow('Invalid refresh token')
    })

    it('should throw if verifyRefresh fails', async () => {
      mockJwtService.verifyRefresh.mockImplementation(() => {
        throw new Error('JWT verification failed')
      })
      await expect(loginUser.refreshAccessToken(mockRefreshToken)).rejects.toThrow('JWT verification failed')
    })
  })

  describe('General User Operations (updatePreferences, updateHealthData, etc.)', () => {
    const mockUserInstance = new User(testUserId, testName, testEmail, testFirebaseToken)

    beforeEach(() => {
      // Common setup for these tests: verify token and find user
      mockJwtService.verify.mockReturnValue({ data: testUserId } as JwtPayload)
      mockUserRepository.findById.mockResolvedValue(mockUserInstance)
    })

    it('updatePreferences should call repository and return user', async () => {
      const preferences: Partial<UserPreferences> = { theme: 'dark' }
      mockUserRepository.updatePreferences.mockResolvedValue(mockUserInstance) // Assume User object is returned

      const result = await loginUser.updatePreferences(mockAccessToken, preferences)
      expect(mockJwtService.verify).toHaveBeenCalledWith(mockAccessToken)
      expect(mockUserRepository.findById).toHaveBeenCalledWith(testUserId)
      expect(mockUserRepository.updatePreferences).toHaveBeenCalledWith(testUserId, preferences)
      expect(result).toBe(mockUserInstance)
    })

    it('updatePreferences should throw if user not found by token', async () => {
      mockUserRepository.findById.mockResolvedValue(null)
      const preferences: Partial<UserPreferences> = { theme: 'dark' }
      await expect(loginUser.updatePreferences(mockAccessToken, preferences)).rejects.toThrow('User not found')
    })

    it('updateHealthData should call repository and return user', async () => {
      const healthData: Partial<HealthData> = { allergies: ['nuts'] }
      mockUserRepository.updateHealthData.mockResolvedValue(mockUserInstance)

      const result = await loginUser.updateHealthData(mockAccessToken, healthData)
      expect(mockUserRepository.updateHealthData).toHaveBeenCalledWith(testUserId, healthData)
      expect(result).toBe(mockUserInstance)
    })

    it('updateLocation should call repository and return user', async () => {
      const location: Point = { latitude: 1, longitude: 1 }
      mockUserRepository.updateLocation.mockResolvedValue(mockUserInstance)

      const result = await loginUser.updateLocation(mockAccessToken, location)
      expect(mockUserRepository.updateLocation).toHaveBeenCalledWith(testUserId, location)
      expect(result).toBe(mockUserInstance)
    })

    it('updateProfilePicture should call repository and return user', async () => {
      const url = 'http://new.pic/img.png'
      mockUserRepository.updateProfilePicture.mockResolvedValue(mockUserInstance)

      const result = await loginUser.updateProfilePicture(mockAccessToken, url)
      expect(mockUserRepository.updateProfilePicture).toHaveBeenCalledWith(testUserId, url)
      expect(result).toBe(mockUserInstance)
    })

    it('getProfile should return user', async () => {
      const result = await loginUser.getProfile(mockAccessToken)
      expect(mockJwtService.verify).toHaveBeenCalledWith(mockAccessToken)
      expect(mockUserRepository.findById).toHaveBeenCalledWith(testUserId)
      expect(result).toBe(mockUserInstance)
    })

    it('getProfile should throw if user not found by token', async () => {
      mockUserRepository.findById.mockResolvedValue(null)
      await expect(loginUser.getProfile(mockAccessToken)).rejects.toThrow('User not found')
    })
  })
})

describe('LoginUser - deleteUser', () => {
  let mockUserRepository: Mocked<IUser>
  let mockJwtService: Mocked<IJWT>
  let loginUser: LoginUser

  beforeEach(() => {
    // Re-initialize mocks for this specific describe block to ensure isolation
    // and to include deleteUser
    mockUserRepository = {
      create: mock() as unknown as Mock<IUser['create']>,
      findById: mock() as unknown as Mock<IUser['findById']>,
      findByEmail: mock() as unknown as Mock<IUser['findByEmail']>,
      updatePreferences: mock() as unknown as Mock<IUser['updatePreferences']>,
      updateHealthData: mock() as unknown as Mock<IUser['updateHealthData']>,
      updateLocation: mock() as unknown as Mock<IUser['updateLocation']>,
      updateProfilePicture: mock() as unknown as Mock<IUser['updateProfilePicture']>,
      updateFirebaseToken: mock() as unknown as Mock<IUser['updateFirebaseToken']>,
      updateRefreshToken: mock() as unknown as Mock<IUser['updateRefreshToken']>,
      findOrCreateFromFirebase: mock() as unknown as Mock<IUser['findOrCreateFromFirebase']>,
      deleteUser: mock() as unknown as Mock<IUser['deleteUser']> // Ensure deleteUser is part of the mock type
    }

    mockJwtService = {
      sign: mock() as unknown as Mock<IJWT['sign']>,
      verify: mock() as unknown as Mock<IJWT['verify']>,
      verifyRefresh: mock() as unknown as Mock<IJWT['verifyRefresh']>,
      refreshAccessToken: mock() as unknown as Mock<IJWT['refreshAccessToken']>
    }

    loginUser = new LoginUser(mockUserRepository, mockJwtService)
  })

  it('should successfully delete a user when a valid token is provided', async () => {
    const userId = 'test-user-id'
    const validToken = 'valid-token'
    const mockUser = new User(userId, 'Test User', '<EMAIL>', 'firebase-token')

    // Mock jwtService.verify to return a valid user ID
    ;(mockJwtService.verify as any).mockReturnValue({ data: userId })
    // Mock userRepository.findById to return a user
    ;(mockUserRepository.findById as any).mockResolvedValue(mockUser)
    // Mock userRepository.deleteUser to resolve successfully
    ;(mockUserRepository.deleteUser as any).mockResolvedValue(undefined)

    await expect(loginUser.deleteUser(validToken)).resolves.toBeUndefined()

    expect(mockJwtService.verify).toHaveBeenCalledWith(validToken)
    expect(mockUserRepository.findById).toHaveBeenCalledWith(userId)
    expect(mockUserRepository.deleteUser).toHaveBeenCalledWith(userId)
  })

  it('should throw an error if token verification fails', async () => {
    const invalidToken = 'invalid-token'
    ;(mockJwtService.verify as any).mockImplementation(() => {
      throw new Error('Invalid token')
    })

    await expect(loginUser.deleteUser(invalidToken)).rejects.toThrow('Invalid token')
    expect(mockUserRepository.deleteUser).not.toHaveBeenCalled()
  })

  it('should throw an error if user is not found after token verification', async () => {
    const userId = 'test-user-id'
    const validToken = 'valid-token-user-not-found'

    ;(mockJwtService.verify as any).mockReturnValue({ data: userId })
    ;(mockUserRepository.findById as any).mockResolvedValue(null) // User not found

    await expect(loginUser.deleteUser(validToken)).rejects.toThrow('User not found')
    expect(mockUserRepository.deleteUser).not.toHaveBeenCalled()
  })

  it('should throw an error if userRepository.deleteUser fails', async () => {
    const userId = 'test-user-id'
    const validToken = 'valid-token-db-error'
    const mockUser = new User(userId, 'Test User', '<EMAIL>', 'firebase-token')

    ;(mockJwtService.verify as any).mockReturnValue({ data: userId })
    ;(mockUserRepository.findById as any).mockResolvedValue(mockUser)
    ;(mockUserRepository.deleteUser as any).mockRejectedValue(new Error('Database error'))

    await expect(loginUser.deleteUser(validToken)).rejects.toThrow('Database error')
    expect(mockUserRepository.deleteUser).toHaveBeenCalledWith(userId)
  })
})

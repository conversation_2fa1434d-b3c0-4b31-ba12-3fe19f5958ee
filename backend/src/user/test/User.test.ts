import { describe, expect, it } from 'bun:test'
import { HealthData, Point, User, UserPreferences } from '../domain/User.js'

describe('User Class', () => {
  const mockId = 'user-id-123'
  const mockName = 'Test User'
  const mockEmail = '<EMAIL>'
  const mockFirebaseToken = 'firebase-token-xyz'

  it('should create a User instance with default values', () => {
    const user = new User(mockId, mockName, mockEmail, mockFirebaseToken)
    expect(user.id).toBe(mockId)
    expect(user.name).toBe(mockName)
    expect(user.email).toBe(mockEmail)
    expect(user.firebaseToken).toBe(mockFirebaseToken)
    expect(user.currentLocation).toBeUndefined()
    expect(user.preferences).toEqual({
      language: 'es',
      theme: 'light',
      notifications: true
    })
    expect(user.healthData).toEqual({
      allergies: [],
      conditions: [],
      medications: []
    })
    expect(user.profilePictureUrl).toBeUndefined()
    expect(user.getRefreshToken()).toBeUndefined()
    // Private accessToken is not directly testable via getter in this class structure
  })

  it('should create a User instance with all provided values', () => {
    const location: Point = { latitude: 10, longitude: 20 }
    const preferences: UserPreferences = {
      language: 'en',
      theme: 'dark',
      notifications: false,
      defaultLocation: { departmentCode: '01', provinceCode: '01', districtCode: '01' }
    }
    const healthData: HealthData = {
      allergies: ['pollen'],
      conditions: ['asthma'],
      medications: [{ name: 'Ventolin', dosage: '1 puff', frequency: 'as needed' }]
    }
    const profilePic = 'http://example.com/pic.jpg'
    const refreshToken = 'refresh-token-abc'
    const accessToken = 'access-token-def'

    const user = new User(mockId, mockName, mockEmail, mockFirebaseToken, location, preferences, healthData, profilePic, refreshToken, accessToken)

    expect(user.currentLocation).toEqual(location)
    expect(user.preferences).toEqual(preferences)
    expect(user.healthData).toEqual(healthData)
    expect(user.profilePictureUrl).toBe(profilePic)
    expect(user.getRefreshToken()).toBe(refreshToken)
    // Private accessToken is not directly testable
  })

  it('should set and get tokens correctly', () => {
    const user = new User(mockId, mockName, mockEmail, mockFirebaseToken)
    const newAccessToken = 'new-access-123'
    const newRefreshToken = 'new-refresh-456'

    user.setTokens(newAccessToken, newRefreshToken)
    expect(user.getRefreshToken()).toBe(newRefreshToken)
    // Private accessToken is not directly testable

    const newerAccessToken = 'newer-access-789'
    user.setAccessToken(newerAccessToken)
    // Private accessToken is not directly testable
    // We can infer it's set if other methods dependent on it work,
    // or by checking toJSON if it includes accessToken
  })

  it('should set and get Firebase token correctly', () => {
    const user = new User(mockId, mockName, mockEmail, mockFirebaseToken)
    const newFirebaseToken = 'new-firebase-token-abc'
    user.setFirebaseToken(newFirebaseToken)
    expect(user.firebaseToken).toBe(newFirebaseToken)
    expect(user.getFirebaseToken()).toBe(newFirebaseToken)
  })

  it('should return correct JSON representation', () => {
    const location: Point = { latitude: 10, longitude: 20 }
    const user = new User(mockId, mockName, mockEmail, mockFirebaseToken, location)
    user.setTokens('access', 'refresh')

    const jsonUser = user.toJSON()
    expect(jsonUser.id).toBe(mockId)
    expect(jsonUser.name).toBe(mockName)
    expect(jsonUser.email).toBe(mockEmail)
    // firebaseToken is not part of toJSON in the current User class
    expect(jsonUser.currentLocation).toEqual(location)
    expect(jsonUser.preferences).toEqual({ language: 'es', theme: 'light', notifications: true })
    expect(jsonUser.healthData).toEqual({ allergies: [], conditions: [], medications: [] })
    expect(jsonUser.profilePictureUrl).toBeUndefined()
    expect(jsonUser.accessToken).toBe('access')
    expect(jsonUser.refreshToken).toBe('refresh')
  })

  it('should update preferences', () => {
    const user = new User(mockId, mockName, mockEmail, mockFirebaseToken)
    const newPreferences: Partial<UserPreferences> = {
      language: 'fr',
      theme: 'dark'
    }
    user.updatePreferences(newPreferences)
    expect(user.preferences.language).toBe('fr')
    expect(user.preferences.theme).toBe('dark')
    expect(user.preferences.notifications).toBe(true) // Unchanged
  })

  it('should update health data', () => {
    const user = new User(mockId, mockName, mockEmail, mockFirebaseToken)
    const newHealthData: Partial<HealthData> = {
      allergies: ['dust'],
      medications: [{ name: 'Claritin', dosage: '10mg', frequency: 'daily' }]
    }
    user.updateHealthData(newHealthData)
    expect(user.healthData.allergies).toEqual(['dust'])
    expect(user.healthData.conditions).toEqual([]) // Unchanged
    expect(user.healthData.medications).toEqual([{ name: 'Claritin', dosage: '10mg', frequency: 'daily' }])
  })

  it('should update location', () => {
    const user = new User(mockId, mockName, mockEmail, mockFirebaseToken)
    const newLocation: Point = { latitude: -12.04318, longitude: -77.02824 }
    user.updateLocation(newLocation)
    expect(user.currentLocation).toEqual(newLocation)
  })

  it('should update profile picture', () => {
    const user = new User(mockId, mockName, mockEmail, mockFirebaseToken)
    const newProfilePic = 'http://example.com/new_pic.jpg'
    user.updateProfilePicture(newProfilePic)
    expect(user.profilePictureUrl).toBe(newProfilePic)
  })
})

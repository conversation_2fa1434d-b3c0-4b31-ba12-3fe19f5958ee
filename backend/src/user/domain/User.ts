export interface UserPreferences {
  language: string
  theme: 'light' | 'dark' | 'system'
  notifications: boolean
  defaultLocation?: {
    departmentCode: string
    provinceCode: string
    districtCode: string
  }
}

export interface HealthData {
  allergies: string[]
  conditions: string[]
  medications: {
    name: string
    dosage: string
    frequency: string
  }[]
}

export interface Point {
  latitude: number
  longitude: number
}

export class User {
  private accessToken?: string
  private refreshToken?: string

  constructor(
    public id: string,
    public name: string,
    public email: string,
    public firebaseToken: string,
    public currentLocation?: Point,
    public preferences: UserPreferences = {
      language: 'es',
      theme: 'light',
      notifications: true
    },
    public healthData: HealthData = {
      allergies: [],
      conditions: [],
      medications: []
    },
    public profilePictureUrl?: string,
    refreshToken?: string,
    accessToken?: string
  ) {
    if (refreshToken) {
      this.refreshToken = refreshToken
    }
    if (accessToken) {
      this.accessToken = accessToken
    }
  }

  setTokens(accessToken: string, refreshToken: string) {
    this.accessToken = accessToken
    this.refreshToken = refreshToken
  }

  setAccessToken(token: string) {
    this.accessToken = token
  }

  getRefreshToken(): string | undefined {
    return this.refreshToken
  }

  setFirebaseToken(token: string) {
    this.firebaseToken = token
  }

  getFirebaseToken(): string | undefined {
    return this.firebaseToken
  }

  toJSON() {
    return {
      id: this.id,
      name: this.name,
      email: this.email,
      currentLocation: this.currentLocation,
      preferences: this.preferences,
      healthData: this.healthData,
      profilePictureUrl: this.profilePictureUrl,
      accessToken: this.accessToken,
      refreshToken: this.refreshToken
    }
  }

  updatePreferences(preferences: Partial<UserPreferences>) {
    this.preferences = {
      ...this.preferences,
      ...preferences
    }
  }

  updateHealthData(healthData: Partial<HealthData>) {
    this.healthData = {
      ...this.healthData,
      ...healthData
    }
  }

  updateLocation(location: Point) {
    this.currentLocation = location
  }

  updateProfilePicture(url: string) {
    this.profilePictureUrl = url
  }
}

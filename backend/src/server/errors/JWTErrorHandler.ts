/**
 * JWT Error Handler Utility
 *
 * Centralizes JWT error detection and classification logic.
 * Maps JWT library errors to appropriate custom error types.
 */

import { AuthenticationError } from './CustomErrors.js'

/**
 * JWT Error Types from jsonwebtoken library
 */
export enum JWTErrorType {
  TOKEN_EXPIRED = 'TokenExpiredError',
  JWT_MALFORMED = 'JsonWebTokenError',
  INVALID_SIGNATURE = 'invalid signature',
  INVALID_TOKEN = 'invalid token',
  JWT_EXPIRED = 'jwt expired',
  JWT_MALFORMED_MSG = 'jwt malformed'
}

/**
 * Checks if an error is JWT-related and throws appropriate AuthenticationError
 *
 * @param error - The error to check
 * @throws {AuthenticationError} - Throws specific authentication error based on JWT error type
 */
export function handleJWTError(error: unknown): never {
  const err = error as Error
  const errorMessage = err.message?.toLowerCase() || ''
  const errorName = err.name || ''

  // Handle token expired errors
  if (errorName === JWTErrorType.TOKEN_EXPIRED || errorMessage.includes(JWTErrorType.JWT_EXPIRED)) {
    throw new AuthenticationError('Token expired')
  }

  // Handle invalid signature errors
  if (errorMessage.includes(JWTErrorType.INVALID_SIGNATURE)) {
    throw new AuthenticationError('Invalid token signature')
  }

  // Handle malformed JWT errors
  if (
    errorName === JWTErrorType.JWT_MALFORMED ||
    errorMessage.includes(JWTErrorType.JWT_MALFORMED_MSG) ||
    errorMessage.includes(JWTErrorType.INVALID_TOKEN)
  ) {
    throw new AuthenticationError('Invalid token format')
  }

  // Handle general invalid token or user ID not found errors
  if (errorMessage.includes('invalid token') || errorMessage.includes('user id not found')) {
    throw new AuthenticationError('Unauthorized')
  }

  // If it's not a recognized JWT error, re-throw the original error
  throw error
}

/**
 * Checks if an error is JWT-related without throwing
 *
 * @param error - The error to check
 * @returns boolean - True if the error is JWT-related
 */
export function isJWTError(error: unknown): boolean {
  const err = error as Error
  const errorMessage = err.message?.toLowerCase() || ''
  const errorName = err.name || ''

  return (
    errorName === JWTErrorType.TOKEN_EXPIRED ||
    errorName === JWTErrorType.JWT_MALFORMED ||
    errorMessage.includes(JWTErrorType.JWT_EXPIRED) ||
    errorMessage.includes(JWTErrorType.INVALID_SIGNATURE) ||
    errorMessage.includes(JWTErrorType.JWT_MALFORMED_MSG) ||
    errorMessage.includes(JWTErrorType.INVALID_TOKEN) ||
    errorMessage.includes('invalid token') ||
    errorMessage.includes('user id not found')
  )
}

/**
 * Gets the appropriate authentication error message for a JWT error
 *
 * @param error - The JWT error
 * @returns string - The appropriate error message
 */
export function getJWTErrorMessage(error: unknown): string {
  const err = error as Error
  const errorMessage = err.message?.toLowerCase() || ''
  const errorName = err.name || ''

  if (errorName === JWTErrorType.TOKEN_EXPIRED || errorMessage.includes(JWTErrorType.JWT_EXPIRED)) {
    return 'Token expired'
  }

  if (errorMessage.includes(JWTErrorType.INVALID_SIGNATURE)) {
    return 'Invalid token signature'
  }

  if (
    errorName === JWTErrorType.JWT_MALFORMED ||
    errorMessage.includes(JWTErrorType.JWT_MALFORMED_MSG) ||
    errorMessage.includes(JWTErrorType.INVALID_TOKEN)
  ) {
    return 'Invalid token format'
  }

  return 'Unauthorized'
}

/**
 * Validates token presence and throws AuthenticationError if missing
 *
 * @param token - The token to validate
 * @throws {AuthenticationError} - If token is null or undefined
 */
export function validateTokenPresence(token: string | null | undefined): asserts token is string {
  if (!token) {
    throw new AuthenticationError('Unauthorized')
  }
}

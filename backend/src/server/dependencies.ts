import { JWT } from '../services/jwt.js'
import { LoginUser } from '../user/application/login.js'
import { UserRepository } from '../user/infrastructure/UserRepository.js'
import { LoginController } from '../user/infrastructure/controllers/login.js'

import { AddMedicationFavorite } from '../favorites/application/AddMedicationFavorite.js'
import { ListMedicationFavorites } from '../favorites/application/ListMedicationFavorites.js'
import { RemoveMedicationFavorite } from '../favorites/application/RemoveMedicationFavorite.js'
import { SearchMedicationFavorites } from '../favorites/application/SearchMedicationFavorites.js'
// Favorites dependencies
import { MedicationFavoriteRepository } from '../favorites/infrastructure/MedicationFavoriteRepository.js'
import { MedicationFavoriteController } from '../favorites/infrastructure/controllers/MedicationFavoriteController.js'

// Legal dependencies
import { GetLegalDocument } from '../legal/application/GetLegalDocument.js'
import { LegalController } from '../legal/infrastructure/controllers/LegalController.js'

// Shared services
const jwtService = new JWT()

// User dependencies
const userRepository = new UserRepository()
const loginUser = new LoginUser(userRepository, jwtService)
export const loginController = new LoginController(loginUser)

// Medication favorites dependencies
const medicationFavoriteRepository = new MedicationFavoriteRepository()
const addMedicationFavorite = new AddMedicationFavorite(medicationFavoriteRepository, jwtService)
const removeMedicationFavorite = new RemoveMedicationFavorite(medicationFavoriteRepository, jwtService)
const listMedicationFavorites = new ListMedicationFavorites(medicationFavoriteRepository, jwtService)
const searchMedicationFavorites = new SearchMedicationFavorites(medicationFavoriteRepository, jwtService)
export const medicationFavoriteController = new MedicationFavoriteController(
  addMedicationFavorite,
  removeMedicationFavorite,
  listMedicationFavorites,
  searchMedicationFavorites
)

// Legal dependencies
const getLegalDocument = new GetLegalDocument()
export const legalController = new LegalController(getLegalDocument)

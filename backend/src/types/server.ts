import { Context } from 'elysia'

// Extend the Elysia context to include the token property
export interface AuthenticatedContext extends Context {
  token: string | null
}

// Type for authenticated requests where token is guaranteed to be present
export interface RequiredAuthContext extends Context {
  token: string
}

// JWT Payload interface
export interface JwtPayload {
  data: string
  iat?: number
  exp?: number
}

// Test server types
export interface TestServerOptions {
  body?: unknown
  headers?: Record<string, string>
  port?: number
}

// Custom error types for better type safety
export interface CustomError extends Error {
  type?: string
  status?: number
}

// Elysia context with proper typing
export interface ElysiaContext {
  headers?: Record<string, string | undefined>
  body?: unknown
  query?: Record<string, string>
  params?: Record<string, string>
  set?: {
    status?: number
    headers?: Record<string, string>
  }
  token?: string | null
}

/// <reference types="bun-types" />

// Global type declarations for the project
declare global {
  // biome-ignore lint/style/noNamespace: Required for extending Node.js ProcessEnv types
  namespace NodeJS {
    interface ProcessEnv {
      NODE_ENV?: 'development' | 'production' | 'test'
      PORT?: string
      DATABASE_URL?: string
      JWT_SECRET?: string
      JWT_REFRESH_SECRET?: string
    }
  }
}

export {}

import { t } from 'elysia'

// DTO for adding a medication to favorites
export const addMedicationFavoriteDTO = {
  body: t.Object({
    codEstab: t.String(),
    codProdE: t.Number(),
    fecha: t.String(),
    nombreProducto: t.String(),
    precio1: t.Number(),
    precio2: t.Number(),
    precio3: t.Union([t.Number(), t.Null()]),
    codGrupoFF: t.String(),
    ubicodigo: t.String(),
    direccion: t.String(),
    telefono: t.String(),
    nomGrupoFF: t.String(),
    setcodigo: t.String(),
    nombreComercial: t.String(),
    grupo: t.String(),
    totalPA: t.String(),
    concent: t.String(),
    nombreFormaFarmaceutica: t.String(),
    fracciones: t.Number(),
    totalRegistros: t.Union([t.Number(), t.Null()]),
    nombreLaboratorio: t.String(),
    nombreTitular: t.String(),
    catCodigo: t.String(),
    nombreSustancia: t.String(),
    fabricante: t.Union([t.String(), t.Null()]),
    departamento: t.String(),
    provincia: t.String(),
    distrito: t.String()
  })
}

// DTO for removing a medication from favorites by medication identifiers
export const removeMedicationFavoriteDTO = {
  body: t.Object({
    codEstab: t.String(),
    codProdE: t.Number()
  })
}

// DTO for listing favorites with pagination
export const listMedicationFavoritesDTO = {
  query: t.Object({
    page: t.Optional(t.String()),
    limit: t.Optional(t.String())
  })
}

// DTO for searching within favorites
export const searchMedicationFavoritesDTO = {
  query: t.Object({
    q: t.String(),
    page: t.Optional(t.String()),
    limit: t.Optional(t.String())
  })
}

// Response DTOs
export interface MedicationFavoriteResponse {
  id: string
  codEstab: string
  codProdE: number
  medicationData: {
    codEstab: string
    codProdE: number
    fecha: string
    nombreProducto: string
    precio1: number
    precio2: number
    precio3: number | null
    codGrupoFF: string
    ubicodigo: string
    direccion: string
    telefono: string
    nomGrupoFF: string
    setcodigo: string
    nombreComercial: string
    grupo: string
    totalPA: string
    concent: string
    nombreFormaFarmaceutica: string
    fracciones: number
    totalRegistros: number | null
    nombreLaboratorio: string
    nombreTitular: string
    catCodigo: string
    nombreSustancia: string
    fabricante: string | null
    departamento: string
    provincia: string
    distrito: string
  }
  createdAt: string
  updatedAt: string
}

export interface PaginatedMedicationFavoritesResponse {
  data: MedicationFavoriteResponse[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

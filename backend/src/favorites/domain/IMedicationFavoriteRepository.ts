import { MedicationData, MedicationFavorite } from './MedicationFavorite.js'

export interface PaginationOptions {
  page: number
  limit: number
}

export interface PaginatedResult<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}

export interface SearchOptions extends PaginationOptions {
  query: string
}

export interface IMedicationFavoriteRepository {
  // Create a new medication favorite
  create(userId: string, medicationData: MedicationData): Promise<MedicationFavorite>

  // Find a specific favorite by user and medication identifiers
  findByUserAndMedication(userId: string, codEstab: string, codProdE: number): Promise<MedicationFavorite | null>

  // Find a favorite by ID and user (for authorization)
  findByIdAndUser(id: string, userId: string): Promise<MedicationFavorite | null>

  // List all favorites for a user with pagination
  findByUser(userId: string, options: PaginationOptions): Promise<PaginatedResult<MedicationFavorite>>

  // Search within user's favorites
  searchByUser(userId: string, options: SearchOptions): Promise<PaginatedResult<MedicationFavorite>>

  // Remove a favorite by ID and user
  removeByIdAndUser(id: string, userId: string): Promise<boolean>

  // Remove a favorite by medication identifiers
  removeByUserAndMedication(userId: string, codEstab: string, codProdE: number): Promise<boolean>

  // Check if a medication is already favorited by user
  existsByUserAndMedication(userId: string, codEstab: string, codProdE: number): Promise<boolean>

  // Get total count of favorites for a user
  countByUser(userId: string): Promise<number>

  // Update medication data (in case of price changes, etc.)
  updateMedicationData(id: string, medicationData: MedicationData): Promise<MedicationFavorite>
}

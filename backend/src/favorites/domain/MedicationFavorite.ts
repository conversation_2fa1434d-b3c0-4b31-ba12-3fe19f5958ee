// Medication data structure from MINSA API
export interface MedicationData {
  codEstab: string
  codProdE: number
  fecha: string
  nombreProducto: string
  precio1: number
  precio2: number
  precio3: number | null
  codGrupoFF: string
  ubicodigo: string
  direccion: string
  telefono: string
  nomGrupoFF: string
  setcodigo: string
  nombreComercial: string
  grupo: string
  totalPA: string
  concent: string
  nombreFormaFarmaceutica: string
  fracciones: number
  totalRegistros: number | null
  nombreLaboratorio: string
  nombreTitular: string
  catCodigo: string
  nombreSustancia: string
  fabricante: string | null
  departamento: string
  provincia: string
  distrito: string
}

// Define the data for creating a new favorite
export interface CreateMedicationFavoriteData {
  userId: string
  codEstab: string
  codProdE: number
  medicationData: MedicationData
}

export class MedicationFavorite {
  constructor(
    public id: string,
    public userId: string,
    public codEstab: string,
    public codProdE: number,
    public medicationData: MedicationData,
    public createdAt: Date,
    public updatedAt: Date
  ) {}

  // Create a new medication favorite
  static create(userId: string, medicationData: MedicationData): CreateMedicationFavoriteData {
    return {
      userId,
      codEstab: medicationData.codEstab,
      codProdE: medicationData.codProdE,
      medicationData
    }
  }

  // Get unique identifier for deduplication
  getUniqueKey(): string {
    return `${this.userId}-${this.codEstab}-${this.codProdE}`
  }

  // Check if this favorite matches another medication
  matches(codEstab: string, codProdE: number): boolean {
    return this.codEstab === codEstab && this.codProdE === codProdE
  }

  // Get medication name for display
  getMedicationName(): string {
    return this.medicationData.nombreProducto
  }

  // Get establishment name
  getEstablishmentName(): string {
    return this.medicationData.nombreComercial
  }

  // Get primary price
  getPrimaryPrice(): number {
    return this.medicationData.precio1
  }

  // Convert to JSON for API responses
  toJSON() {
    return {
      id: this.id,
      userId: this.userId,
      codEstab: this.codEstab,
      codProdE: this.codProdE,
      medicationData: this.medicationData,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    }
  }

  // Convert to public JSON (without sensitive user data)
  toPublicJSON() {
    return {
      id: this.id,
      codEstab: this.codEstab,
      codProdE: this.codProdE,
      medicationData: this.medicationData,
      createdAt: this.createdAt,
      updatedAt: this.updatedAt
    }
  }
}

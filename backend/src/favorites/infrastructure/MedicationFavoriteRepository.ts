import { Prisma, PrismaClient, medication_favorite as PrismaMedicationFavorite } from '@prisma/client'
import type { IMedicationFavoriteRepository, PaginatedResult, PaginationOptions, SearchOptions } from '../domain/IMedicationFavoriteRepository.js'
import { MedicationData, MedicationFavorite } from '../domain/MedicationFavorite.js'

export class MedicationFavoriteRepository implements IMedicationFavoriteRepository {
  private readonly db: PrismaClient

  constructor() {
    this.db = new PrismaClient()
  }

  private mapToMedicationFavorite(dbFavorite: PrismaMedicationFavorite): MedicationFavorite {
    return new MedicationFavorite(
      dbFavorite.id,
      dbFavorite.user_id,
      dbFavorite.cod_estab,
      dbFavorite.cod_prod_e,
      dbFavorite.medication_data as unknown as MedicationData,
      dbFavorite.created_at,
      dbFavorite.updated_at
    )
  }

  async create(userId: string, medicationData: MedicationData): Promise<MedicationFavorite> {
    const favorite = await this.db.medication_favorite.create({
      data: {
        user_id: userId,
        cod_estab: medicationData.codEstab,
        cod_prod_e: medicationData.codProdE,
        medication_data: medicationData as unknown as Prisma.JsonObject
      }
    })

    return this.mapToMedicationFavorite(favorite)
  }

  async findByUserAndMedication(userId: string, codEstab: string, codProdE: number): Promise<MedicationFavorite | null> {
    const favorite = await this.db.medication_favorite.findUnique({
      where: {
        user_id_cod_estab_cod_prod_e: {
          user_id: userId,
          cod_estab: codEstab,
          cod_prod_e: codProdE
        }
      }
    })

    if (!favorite) return null
    return this.mapToMedicationFavorite(favorite)
  }

  async findByIdAndUser(id: string, userId: string): Promise<MedicationFavorite | null> {
    const favorite = await this.db.medication_favorite.findFirst({
      where: {
        id,
        user_id: userId
      }
    })

    if (!favorite) return null
    return this.mapToMedicationFavorite(favorite)
  }

  async findByUser(userId: string, options: PaginationOptions): Promise<PaginatedResult<MedicationFavorite>> {
    const skip = (options.page - 1) * options.limit

    const [favorites, total] = await Promise.all([
      this.db.medication_favorite.findMany({
        where: { user_id: userId },
        orderBy: { created_at: 'desc' },
        skip,
        take: options.limit
      }),
      this.db.medication_favorite.count({
        where: { user_id: userId }
      })
    ])

    const totalPages = Math.ceil(total / options.limit)

    return {
      data: favorites.map((f) => this.mapToMedicationFavorite(f)),
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages,
        hasNext: options.page < totalPages,
        hasPrev: options.page > 1
      }
    }
  }

  async searchByUser(userId: string, options: SearchOptions): Promise<PaginatedResult<MedicationFavorite>> {
    const skip = (options.page - 1) * options.limit
    const searchQuery = options.query.toLowerCase()

    // For MongoDB with Prisma, we'll use a simpler approach
    // Get all user favorites first, then filter in memory for search
    // This is less efficient but works reliably with Prisma MongoDB
    const allUserFavorites = await this.db.medication_favorite.findMany({
      where: { user_id: userId },
      orderBy: { created_at: 'desc' }
    })

    // Filter favorites based on search query
    const filteredFavorites = allUserFavorites.filter((favorite) => {
      const medicationData = favorite.medication_data as unknown as MedicationData
      if (!medicationData) return false

      const searchFields = [
        medicationData.nombreProducto,
        medicationData.nombreComercial,
        medicationData.nombreSustancia,
        medicationData.nombreLaboratorio,
        medicationData.concent
      ]

      return searchFields.some((field) => field && typeof field === 'string' && field.toLowerCase().includes(searchQuery))
    })

    // Apply pagination to filtered results
    const total = filteredFavorites.length
    const paginatedFavorites = filteredFavorites.slice(skip, skip + options.limit)
    const totalPages = Math.ceil(total / options.limit)

    return {
      data: paginatedFavorites.map((f) => this.mapToMedicationFavorite(f)),
      pagination: {
        page: options.page,
        limit: options.limit,
        total,
        totalPages,
        hasNext: options.page < totalPages,
        hasPrev: options.page > 1
      }
    }
  }

  async removeByIdAndUser(id: string, userId: string): Promise<boolean> {
    try {
      await this.db.medication_favorite.delete({
        where: {
          id,
          user_id: userId
        }
      })
      return true
    } catch (_error) {
      return false
    }
  }

  async removeByUserAndMedication(userId: string, codEstab: string, codProdE: number): Promise<boolean> {
    try {
      await this.db.medication_favorite.delete({
        where: {
          user_id_cod_estab_cod_prod_e: {
            user_id: userId,
            cod_estab: codEstab,
            cod_prod_e: codProdE
          }
        }
      })
      return true
    } catch (_error) {
      return false
    }
  }

  async existsByUserAndMedication(userId: string, codEstab: string, codProdE: number): Promise<boolean> {
    const count = await this.db.medication_favorite.count({
      where: {
        user_id: userId,
        cod_estab: codEstab,
        cod_prod_e: codProdE
      }
    })
    return count > 0
  }

  async countByUser(userId: string): Promise<number> {
    return await this.db.medication_favorite.count({
      where: { user_id: userId }
    })
  }

  async updateMedicationData(id: string, medicationData: MedicationData): Promise<MedicationFavorite> {
    const favorite = await this.db.medication_favorite.update({
      where: { id },
      data: {
        medication_data: medicationData as unknown as Prisma.JsonObject,
        cod_estab: medicationData.codEstab,
        cod_prod_e: medicationData.codProdE
      }
    })

    return this.mapToMedicationFavorite(favorite)
  }
}

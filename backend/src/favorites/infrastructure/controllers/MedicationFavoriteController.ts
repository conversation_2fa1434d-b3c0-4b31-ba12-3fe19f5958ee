import {
  AuthenticationError,
  ConflictError,
  NotFoundError,
  ValidationError,
  handleJWTError,
  validateTokenPresence
} from '../../../server/errors/index.js'
import { AddMedicationFavorite } from '../../application/AddMedicationFavorite.js'
import { ListMedicationFavorites } from '../../application/ListMedicationFavorites.js'
import { RemoveMedicationFavorite } from '../../application/RemoveMedicationFavorite.js'
import { SearchMedicationFavorites } from '../../application/SearchMedicationFavorites.js'
import type { MedicationData } from '../../domain/MedicationFavorite.js'

type AuthenticatedRequest = {
  token: string | null
}

type AddMedicationFavoriteRequest = AuthenticatedRequest & {
  body: MedicationData
}

type RemoveMedicationFavoriteRequest = AuthenticatedRequest & {
  body: {
    codEstab: string
    codProdE: number
  }
}

type RemoveMedicationFavoriteByIdRequest = AuthenticatedRequest & {
  params: {
    id: string
  }
}

type ListMedicationFavoritesRequest = AuthenticatedRequest & {
  query: {
    page?: string
    limit?: string
  }
}

type SearchMedicationFavoritesRequest = AuthenticatedRequest & {
  query: {
    q: string
    page?: string
    limit?: string
  }
}

export class MedicationFavoriteController {
  constructor(
    private readonly addMedicationFavorite: AddMedicationFavorite,
    private readonly removeMedicationFavorite: RemoveMedicationFavorite,
    private readonly listMedicationFavorites: ListMedicationFavorites,
    private readonly searchMedicationFavorites: SearchMedicationFavorites
  ) {}

  async addFavorite({ body, token }: AddMedicationFavoriteRequest) {
    validateTokenPresence(token)

    try {
      const favorite = await this.addMedicationFavorite.execute(token, body)
      return {
        status: 201,
        data: favorite.toPublicJSON()
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message.includes('already in favorites')) {
        throw new ConflictError(err.message)
      }

      // Handle validation errors
      if (err.message.includes('required') || err.message.includes('must be')) {
        throw new ValidationError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async removeFavorite({ body, token }: RemoveMedicationFavoriteRequest) {
    validateTokenPresence(token)

    try {
      await this.removeMedicationFavorite.executeByMedication(token, body.codEstab, body.codProdE)
      return {
        status: 200,
        message: 'Medication removed from favorites'
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message.includes('not in favorites')) {
        throw new NotFoundError(err.message)
      }

      // Handle validation errors
      if (err.message.includes('required') || err.message.includes('must be')) {
        throw new ValidationError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async removeFavoriteById({ params, token }: RemoveMedicationFavoriteByIdRequest) {
    validateTokenPresence(token)

    try {
      await this.removeMedicationFavorite.executeById(token, params.id)

      return {
        status: 200,
        message: 'Medication removed from favorites'
      }
    } catch (error) {
      const err = error as Error

      // Handle specific business logic errors
      if (err.message.includes('not found') || err.message.includes('does not belong')) {
        throw new NotFoundError(err.message)
      }

      // Handle validation errors
      if (err.message.includes('required') || err.message.includes('Invalid favorite ID format')) {
        throw new ValidationError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async listFavorites({ query, token }: ListMedicationFavoritesRequest) {
    validateTokenPresence(token)

    try {
      const page = query.page ? parseInt(query.page, 10) : 1
      const limit = query.limit ? parseInt(query.limit, 10) : 20

      const result = await this.listMedicationFavorites.execute(token, { page, limit })

      return {
        status: 200,
        data: {
          favorites: result.data.map((f) => f.toPublicJSON()),
          pagination: result.pagination
        }
      }
    } catch (error) {
      const err = error as Error

      // Handle validation errors
      if (err.message.includes('must be') || err.message.includes('cannot exceed')) {
        throw new ValidationError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }

  async searchFavorites({ query, token }: SearchMedicationFavoritesRequest) {
    validateTokenPresence(token)

    try {
      const page = query.page ? parseInt(query.page, 10) : 1
      const limit = query.limit ? parseInt(query.limit, 10) : 20

      const result = await this.searchMedicationFavorites.execute(token, {
        query: query.q,
        page,
        limit
      })

      return {
        status: 200,
        data: {
          favorites: result.data.map((f) => f.toPublicJSON()),
          pagination: result.pagination,
          query: query.q
        }
      }
    } catch (error) {
      const err = error as Error

      // Handle validation errors
      if (err.message.includes('required') || err.message.includes('must be') || err.message.includes('cannot exceed')) {
        throw new ValidationError(err.message)
      }

      // Handle JWT errors
      handleJWTError(error)
    }
  }
}

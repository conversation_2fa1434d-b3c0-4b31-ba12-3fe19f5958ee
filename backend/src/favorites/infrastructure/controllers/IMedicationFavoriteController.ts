import type { MedicationData } from '../../domain/MedicationFavorite.js'

type AuthenticatedRequest = {
  token: string | null
}

type AddMedicationFavoriteRequest = AuthenticatedRequest & {
  body: MedicationData
}

type RemoveMedicationFavoriteRequest = AuthenticatedRequest & {
  body: {
    codEstab: string
    codProdE: number
  }
}

type RemoveMedicationFavoriteByIdRequest = AuthenticatedRequest & {
  params: {
    id: string
  }
}

type ListMedicationFavoritesRequest = AuthenticatedRequest & {
  query: {
    page?: string
    limit?: string
  }
}

type SearchMedicationFavoritesRequest = AuthenticatedRequest & {
  query: {
    q: string
    page?: string
    limit?: string
  }
}

// Response types based on actual controller implementations
type AddFavoriteResponse = {
  status: 201
  data: Record<string, unknown>
}

type RemoveFavoriteResponse = {
  status: 200
  message: string
}

type Pagination = {
  page: number
  limit: number
  total: number
  totalPages: number
}

type ListFavoritesResponse = {
  status: 200
  data: {
    favorites: Record<string, unknown>[]
    pagination: Pagination
  }
}

type SearchFavoritesResponse = {
  status: 200
  data: {
    favorites: Record<string, unknown>[]
    pagination: Pagination
    query: string
  }
}

export interface IMedicationFavoriteController {
  addFavorite(request: AddMedicationFavoriteRequest): Promise<AddFavoriteResponse>
  removeFavorite(request: RemoveMedicationFavoriteRequest): Promise<RemoveFavoriteResponse>
  removeFavoriteById(request: RemoveMedicationFavoriteByIdRequest): Promise<RemoveFavoriteResponse>
  listFavorites(request: ListMedicationFavoritesRequest): Promise<ListFavoritesResponse>
  searchFavorites(request: SearchMedicationFavoritesRequest): Promise<SearchFavoritesResponse>
}

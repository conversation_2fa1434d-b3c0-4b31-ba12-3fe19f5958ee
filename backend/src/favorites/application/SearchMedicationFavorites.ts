import type { IJWT } from '../../services/interfaces/IJWT.js'
import type { IMedicationFavoriteRepository, PaginatedResult, SearchOptions } from '../domain/IMedicationFavoriteRepository.js'
import type { MedicationFavorite } from '../domain/MedicationFavorite.js'

export class SearchMedicationFavorites {
  constructor(
    private readonly medicationFavoriteRepository: IMedicationFavoriteRepository,
    private readonly jwtService: IJWT
  ) {}

  async execute(token: string, options: Partial<SearchOptions>): Promise<PaginatedResult<MedicationFavorite>> {
    // Verify and extract user ID from token
    const payload = this.jwtService.verify(token)
    const userId = payload.data

    if (!userId) {
      throw new Error('Invalid token: user ID not found')
    }

    // Validate search query
    if (!options.query || options.query.trim() === '') {
      throw new Error('Search query is required')
    }

    // Validate provided pagination parameters before setting defaults
    if (options.page !== undefined && options.page < 1) {
      throw new Error('Page number must be greater than 0')
    }
    if (options.limit !== undefined && options.limit < 1) {
      throw new Error('Limit must be greater than 0')
    }
    if (options.limit !== undefined && options.limit > 100) {
      throw new Error('Limit cannot exceed 100 items per page')
    }

    // Set default search options
    const searchOptions: SearchOptions = {
      query: options.query.trim(),
      page: options.page ?? 1,
      limit: options.limit ?? 20
    }

    // Validate search query parameters
    this.validateSearchQuery(searchOptions.query)

    // Search within user's favorites
    const result = await this.medicationFavoriteRepository.searchByUser(userId, searchOptions)

    return result
  }

  private validateSearchQuery(query: string): void {
    if (query.length < 2) {
      throw new Error('Search query must be at least 2 characters long')
    }

    if (query.length > 100) {
      throw new Error('Search query cannot exceed 100 characters')
    }
  }
}

import type { IJWT } from '../../services/interfaces/IJWT.js'
import type { IMedicationFavoriteRepository, PaginatedResult, PaginationOptions } from '../domain/IMedicationFavoriteRepository.js'
import type { MedicationFavorite } from '../domain/MedicationFavorite.js'

export class ListMedicationFavorites {
  constructor(
    private readonly medicationFavoriteRepository: IMedicationFavoriteRepository,
    private readonly jwtService: IJWT
  ) {}

  async execute(token: string, options: Partial<PaginationOptions> = {}): Promise<PaginatedResult<MedicationFavorite>> {
    // Verify and extract user ID from token
    const payload = this.jwtService.verify(token)
    const userId = payload.data

    if (!userId) {
      throw new Error('Invalid token: user ID not found')
    }

    // Validate provided pagination parameters before setting defaults
    if (options.page !== undefined && options.page < 1) {
      throw new Error('Page number must be greater than 0')
    }
    if (options.limit !== undefined && options.limit < 1) {
      throw new Error('Limit must be greater than 0')
    }
    if (options.limit !== undefined && options.limit > 100) {
      throw new Error('Limit cannot exceed 100 items per page')
    }

    // Set default pagination options
    const paginationOptions: PaginationOptions = {
      page: options.page ?? 1,
      limit: options.limit ?? 20
    }

    // Get user's favorites with pagination
    const result = await this.medicationFavoriteRepository.findByUser(userId, paginationOptions)

    return result
  }
}

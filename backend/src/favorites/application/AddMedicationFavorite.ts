import type { IJWT } from '../../services/interfaces/IJWT.js'
import type { IMedicationFavoriteRepository } from '../domain/IMedicationFavoriteRepository.js'
import type { MedicationData, MedicationFavorite } from '../domain/MedicationFavorite.js'

export class AddMedicationFavorite {
  constructor(
    private readonly medicationFavoriteRepository: IMedicationFavoriteRepository,
    private readonly jwtService: IJWT
  ) {}

  async execute(token: string, medicationData: MedicationData): Promise<MedicationFavorite> {
    // Verify and extract user ID from token
    const payload = this.jwtService.verify(token)
    const userId = payload.data

    if (!userId) {
      throw new Error('Invalid token: user ID not found')
    }

    // Validate medication data
    this.validateMedicationData(medicationData)

    // Check if medication is already in favorites
    const existingFavorite = await this.medicationFavoriteRepository.findByUserAndMedication(userId, medicationData.codEstab, medicationData.codProdE)

    if (existingFavorite) {
      throw new Error('Medication is already in favorites')
    }

    // Create new favorite
    const favorite = await this.medicationFavoriteRepository.create(userId, medicationData)

    return favorite
  }

  private validateMedicationData(medicationData: MedicationData): void {
    // Validate required fields
    if (!medicationData.codEstab || medicationData.codEstab.trim() === '') {
      throw new Error('Establishment code is required')
    }

    if (!medicationData.codProdE || medicationData.codProdE <= 0) {
      throw new Error('Product code is required and must be positive')
    }

    if (!medicationData.nombreProducto || medicationData.nombreProducto.trim() === '') {
      throw new Error('Product name is required')
    }

    if (!medicationData.nombreComercial || medicationData.nombreComercial.trim() === '') {
      throw new Error('Commercial name is required')
    }

    if (typeof medicationData.precio1 !== 'number' || medicationData.precio1 < 0) {
      throw new Error('Primary price must be a valid non-negative number')
    }

    if (typeof medicationData.precio2 !== 'number' || medicationData.precio2 < 0) {
      throw new Error('Secondary price must be a valid non-negative number')
    }

    // Validate optional numeric fields
    if (medicationData.precio3 !== null && (typeof medicationData.precio3 !== 'number' || medicationData.precio3 < 0)) {
      throw new Error('Tertiary price must be null or a valid non-negative number')
    }

    if (typeof medicationData.fracciones !== 'number' || medicationData.fracciones <= 0) {
      throw new Error('Fractions must be a positive number')
    }

    if (medicationData.totalRegistros !== null && (typeof medicationData.totalRegistros !== 'number' || medicationData.totalRegistros < 0)) {
      throw new Error('Total records must be null or a valid non-negative number')
    }
  }
}

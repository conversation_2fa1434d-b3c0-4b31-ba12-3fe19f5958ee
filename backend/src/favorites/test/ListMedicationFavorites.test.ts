import { beforeEach, describe, expect, it } from 'bun:test'
import type { IJWT } from '../../services/interfaces/IJWT.js'
import { ListMedicationFavorites } from '../application/ListMedicationFavorites.js'
import type { IMedicationFavoriteRepository } from '../domain/IMedicationFavoriteRepository.js'
import {
  type Mocked,
  TEST_ACCESS_TOKEN,
  TEST_USER_ID,
  createMockJwtService,
  createMockMedicationData,
  createMockMedicationFavorite,
  createMockPaginatedResult,
  createMockRepository
} from './testUtils.js'

describe('ListMedicationFavorites Use Case', () => {
  let listMedicationFavorites: ListMedicationFavorites
  let mockRepository: Mocked<IMedicationFavoriteRepository>
  let mockJwtService: Mocked<IJWT>

  beforeEach(() => {
    mockRepository = createMockRepository()
    mockJwtService = createMockJwtService()
    listMedicationFavorites = new ListMedicationFavorites(mockRepository, mockJwtService)
  })

  describe('Successful execution', () => {
    it('should list favorites with default pagination', async () => {
      const favorites = [createMockMedicationFavorite('fav-1', TEST_USER_ID), createMockMedicationFavorite('fav-2', TEST_USER_ID)]
      const paginatedResult = createMockPaginatedResult(favorites, 1, 20, 2)

      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.findByUser.mockResolvedValue(paginatedResult)

      const result = await listMedicationFavorites.execute(TEST_ACCESS_TOKEN)

      expect(mockJwtService.verify).toHaveBeenCalledWith(TEST_ACCESS_TOKEN)
      expect(mockRepository.findByUser).toHaveBeenCalledWith(TEST_USER_ID, { page: 1, limit: 20 })
      expect(result).toBe(paginatedResult)
    })

    it('should list favorites with custom pagination', async () => {
      const favorites = [createMockMedicationFavorite('fav-1', TEST_USER_ID)]
      const paginatedResult = createMockPaginatedResult(favorites, 2, 10, 15)

      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.findByUser.mockResolvedValue(paginatedResult)

      const result = await listMedicationFavorites.execute(TEST_ACCESS_TOKEN, { page: 2, limit: 10 })

      expect(mockRepository.findByUser).toHaveBeenCalledWith(TEST_USER_ID, { page: 2, limit: 10 })
      expect(result).toBe(paginatedResult)
    })

    it('should list favorites with partial pagination options', async () => {
      const favorites = [createMockMedicationFavorite('fav-1', TEST_USER_ID)]
      const paginatedResult = createMockPaginatedResult(favorites, 3, 20, 50)

      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.findByUser.mockResolvedValue(paginatedResult)

      const result = await listMedicationFavorites.execute(TEST_ACCESS_TOKEN, { page: 3 })

      expect(mockRepository.findByUser).toHaveBeenCalledWith(TEST_USER_ID, { page: 3, limit: 20 })
      expect(result).toBe(paginatedResult)
    })

    it('should handle empty favorites list', async () => {
      const paginatedResult = createMockPaginatedResult([], 1, 20, 0)

      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.findByUser.mockResolvedValue(paginatedResult)

      const result = await listMedicationFavorites.execute(TEST_ACCESS_TOKEN)

      expect(result.data).toEqual([])
      expect(result.pagination.total).toBe(0)
      expect(result.pagination.totalPages).toBe(0)
    })
  })

  describe('Authentication errors', () => {
    it('should throw error when token is invalid', async () => {
      mockJwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token')
      })

      expect(listMedicationFavorites.execute(TEST_ACCESS_TOKEN)).rejects.toThrow('Invalid token')
    })

    it('should throw error when user ID is not found in token', async () => {
      mockJwtService.verify.mockReturnValue({ data: null })

      expect(listMedicationFavorites.execute(TEST_ACCESS_TOKEN)).rejects.toThrow('Invalid token: user ID not found')
    })

    it('should throw error when user ID is undefined in token', async () => {
      mockJwtService.verify.mockReturnValue({})

      expect(listMedicationFavorites.execute(TEST_ACCESS_TOKEN)).rejects.toThrow('Invalid token: user ID not found')
    })
  })

  describe('Pagination validation', () => {
    beforeEach(() => {
      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
    })

    it('should throw error when page is less than 1', async () => {
      expect(listMedicationFavorites.execute(TEST_ACCESS_TOKEN, { page: 0 })).rejects.toThrow('Page number must be greater than 0')
      expect(mockRepository.findByUser).not.toHaveBeenCalled()
    })

    it('should throw error when page is negative', async () => {
      expect(listMedicationFavorites.execute(TEST_ACCESS_TOKEN, { page: -1 })).rejects.toThrow('Page number must be greater than 0')
      expect(mockRepository.findByUser).not.toHaveBeenCalled()
    })

    it('should throw error when limit is less than 1', async () => {
      expect(listMedicationFavorites.execute(TEST_ACCESS_TOKEN, { limit: 0 })).rejects.toThrow('Limit must be greater than 0')
      expect(mockRepository.findByUser).not.toHaveBeenCalled()
    })

    it('should throw error when limit is negative', async () => {
      expect(listMedicationFavorites.execute(TEST_ACCESS_TOKEN, { limit: -1 })).rejects.toThrow('Limit must be greater than 0')
      expect(mockRepository.findByUser).not.toHaveBeenCalled()
    })

    it('should throw error when limit exceeds maximum', async () => {
      expect(listMedicationFavorites.execute(TEST_ACCESS_TOKEN, { limit: 101 })).rejects.toThrow('Limit cannot exceed 100 items per page')
    })

    it('should accept limit of exactly 100', async () => {
      const paginatedResult = createMockPaginatedResult([], 1, 100, 0)
      mockRepository.findByUser.mockResolvedValue(paginatedResult)

      const result = await listMedicationFavorites.execute(TEST_ACCESS_TOKEN, { limit: 100 })

      expect(mockRepository.findByUser).toHaveBeenCalledWith(TEST_USER_ID, { page: 1, limit: 100 })
      expect(result).toBe(paginatedResult)
    })

    it('should accept limit of 1', async () => {
      const paginatedResult = createMockPaginatedResult([], 1, 1, 0)
      mockRepository.findByUser.mockResolvedValue(paginatedResult)

      const result = await listMedicationFavorites.execute(TEST_ACCESS_TOKEN, { limit: 1 })

      expect(mockRepository.findByUser).toHaveBeenCalledWith(TEST_USER_ID, { page: 1, limit: 1 })
      expect(result).toBe(paginatedResult)
    })
  })

  describe('Edge cases', () => {
    beforeEach(() => {
      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
    })

    it('should handle large page numbers', async () => {
      const paginatedResult = createMockPaginatedResult([], 999, 20, 0)
      mockRepository.findByUser.mockResolvedValue(paginatedResult)

      const result = await listMedicationFavorites.execute(TEST_ACCESS_TOKEN, { page: 999 })

      expect(mockRepository.findByUser).toHaveBeenCalledWith(TEST_USER_ID, { page: 999, limit: 20 })
      expect(result).toBe(paginatedResult)
    })

    it('should handle empty options object', async () => {
      const paginatedResult = createMockPaginatedResult([], 1, 20, 0)
      mockRepository.findByUser.mockResolvedValue(paginatedResult)

      const result = await listMedicationFavorites.execute(TEST_ACCESS_TOKEN, {})

      expect(mockRepository.findByUser).toHaveBeenCalledWith(TEST_USER_ID, { page: 1, limit: 20 })
      expect(result).toBe(paginatedResult)
    })

    it('should handle undefined options', async () => {
      const paginatedResult = createMockPaginatedResult([], 1, 20, 0)
      mockRepository.findByUser.mockResolvedValue(paginatedResult)

      const result = await listMedicationFavorites.execute(TEST_ACCESS_TOKEN)

      expect(mockRepository.findByUser).toHaveBeenCalledWith(TEST_USER_ID, { page: 1, limit: 20 })
      expect(result).toBe(paginatedResult)
    })
  })

  describe('Repository integration', () => {
    beforeEach(() => {
      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
    })

    it('should pass through repository results correctly', async () => {
      const favorites = [
        createMockMedicationFavorite('fav-1', TEST_USER_ID, createMockMedicationData({ nombreProducto: 'PARACETAMOL' })),
        createMockMedicationFavorite('fav-2', TEST_USER_ID, createMockMedicationData({ nombreProducto: 'IBUPROFENO' }))
      ]
      const paginatedResult = createMockPaginatedResult(favorites, 1, 20, 25)

      mockRepository.findByUser.mockResolvedValue(paginatedResult)

      const result = await listMedicationFavorites.execute(TEST_ACCESS_TOKEN)

      expect(result.data).toHaveLength(2)
      expect(result.data[0].getMedicationName()).toBe('PARACETAMOL')
      expect(result.data[1].getMedicationName()).toBe('IBUPROFENO')
      expect(result.pagination.total).toBe(25)
      expect(result.pagination.totalPages).toBe(2)
      expect(result.pagination.hasNext).toBe(true)
      expect(result.pagination.hasPrev).toBe(false)
    })
  })
})

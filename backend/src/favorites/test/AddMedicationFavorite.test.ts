import { beforeEach, describe, expect, it } from 'bun:test'
import type { IJWT } from '../../services/interfaces/IJWT.js'
import { AddMedicationFavorite } from '../application/AddMedicationFavorite.js'
import type { IMedicationFavoriteRepository } from '../domain/IMedicationFavoriteRepository.js'
import {
  type Mocked,
  TEST_ACCESS_TOKEN,
  TEST_USER_ID,
  createMockJwtService,
  createMockMedicationData,
  createMockMedicationFavorite,
  createMockRepository
} from './testUtils.js'

describe('AddMedicationFavorite Use Case', () => {
  let addMedicationFavorite: AddMedicationFavorite
  let mockRepository: Mocked<IMedicationFavoriteRepository>
  let mockJwtService: Mocked<IJWT>

  beforeEach(() => {
    mockRepository = createMockRepository()
    mockJwtService = createMockJwtService()
    addMedicationFavorite = new AddMedicationFavorite(mockRepository, mockJwtService)
  })

  describe('Successful execution', () => {
    it('should add medication to favorites successfully', async () => {
      const medicationData = createMockMedicationData()
      const expectedFavorite = createMockMedicationFavorite('new-favorite-id', TEST_USER_ID, medicationData)

      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.findByUserAndMedication.mockResolvedValue(null) // No existing favorite
      mockRepository.create.mockResolvedValue(expectedFavorite)

      const result = await addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)

      expect(mockJwtService.verify).toHaveBeenCalledWith(TEST_ACCESS_TOKEN)
      expect(mockRepository.findByUserAndMedication).toHaveBeenCalledWith(TEST_USER_ID, medicationData.codEstab, medicationData.codProdE)
      expect(mockRepository.create).toHaveBeenCalledWith(TEST_USER_ID, medicationData)
      expect(result).toBe(expectedFavorite)
    })
  })

  describe('Authentication errors', () => {
    it('should throw error when token is invalid', async () => {
      const medicationData = createMockMedicationData()
      mockJwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token')
      })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Invalid token')
    })

    it('should throw error when user ID is not found in token', async () => {
      const medicationData = createMockMedicationData()
      mockJwtService.verify.mockReturnValue({ data: null })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Invalid token: user ID not found')
    })

    it('should throw error when user ID is undefined in token', async () => {
      const medicationData = createMockMedicationData()
      mockJwtService.verify.mockReturnValue({})

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Invalid token: user ID not found')
    })
  })

  describe('Duplicate prevention', () => {
    it('should throw error when medication is already in favorites', async () => {
      const medicationData = createMockMedicationData()
      const existingFavorite = createMockMedicationFavorite('existing-id', TEST_USER_ID, medicationData)

      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.findByUserAndMedication.mockResolvedValue(existingFavorite)

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Medication is already in favorites')

      expect(mockRepository.create).not.toHaveBeenCalled()
    })
  })

  describe('Validation errors', () => {
    beforeEach(() => {
      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.findByUserAndMedication.mockResolvedValue(null)
    })

    it('should throw error when codEstab is empty', async () => {
      const medicationData = createMockMedicationData({ codEstab: '' })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Establishment code is required')
    })

    it('should throw error when codEstab is only whitespace', async () => {
      const medicationData = createMockMedicationData({ codEstab: '   ' })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Establishment code is required')
    })

    it('should throw error when codProdE is zero', async () => {
      const medicationData = createMockMedicationData({ codProdE: 0 })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Product code is required and must be positive')
    })

    it('should throw error when codProdE is negative', async () => {
      const medicationData = createMockMedicationData({ codProdE: -1 })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Product code is required and must be positive')
    })

    it('should throw error when nombreProducto is empty', async () => {
      const medicationData = createMockMedicationData({ nombreProducto: '' })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Product name is required')
    })

    it('should throw error when nombreComercial is empty', async () => {
      const medicationData = createMockMedicationData({ nombreComercial: '' })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Commercial name is required')
    })

    it('should throw error when precio1 is negative', async () => {
      const medicationData = createMockMedicationData({ precio1: -1 })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Primary price must be a valid non-negative number')
    })

    it('should throw error when precio2 is negative', async () => {
      const medicationData = createMockMedicationData({ precio2: -1 })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Secondary price must be a valid non-negative number')
    })

    it('should throw error when precio3 is negative (when not null)', async () => {
      const medicationData = createMockMedicationData({ precio3: -1 })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow(
        'Tertiary price must be null or a valid non-negative number'
      )
    })

    it('should accept null precio3', async () => {
      const medicationData = createMockMedicationData({ precio3: null })
      const expectedFavorite = createMockMedicationFavorite('new-id', TEST_USER_ID, medicationData)
      mockRepository.create.mockResolvedValue(expectedFavorite)

      const result = await addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)
      expect(result).toBe(expectedFavorite)
    })

    it('should throw error when fracciones is zero', async () => {
      const medicationData = createMockMedicationData({ fracciones: 0 })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow('Fractions must be a positive number')
    })

    it('should throw error when totalRegistros is negative (when not null)', async () => {
      const medicationData = createMockMedicationData({ totalRegistros: -1 })

      expect(addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)).rejects.toThrow(
        'Total records must be null or a valid non-negative number'
      )
    })

    it('should accept null totalRegistros', async () => {
      const medicationData = createMockMedicationData({ totalRegistros: null })
      const expectedFavorite = createMockMedicationFavorite('new-id', TEST_USER_ID, medicationData)
      mockRepository.create.mockResolvedValue(expectedFavorite)

      const result = await addMedicationFavorite.execute(TEST_ACCESS_TOKEN, medicationData)
      expect(result).toBe(expectedFavorite)
    })
  })
})

import { beforeEach, describe, expect, it, mock } from 'bun:test'
import type { AddMedicationFavorite } from '../application/AddMedicationFavorite.js'
import type { ListMedicationFavorites } from '../application/ListMedicationFavorites.js'
import type { RemoveMedicationFavorite } from '../application/RemoveMedicationFavorite.js'
import type { SearchMedicationFavorites } from '../application/SearchMedicationFavorites.js'
import { MedicationFavoriteController } from '../infrastructure/controllers/MedicationFavoriteController.js'
import {
  TEST_ACCESS_TOKEN,
  TEST_FAVORITE_ID,
  TEST_USER_ID,
  createMockMedicationData,
  createMockMedicationFavorite,
  createMockPaginatedResult
} from './testUtils.js'

// Mock types for better type safety
interface MockAddMedicationFavorite {
  execute: ReturnType<typeof mock>
}

interface MockRemoveMedicationFavorite {
  executeByMedication: ReturnType<typeof mock>
  executeById: ReturnType<typeof mock>
}

interface MockListMedicationFavorites {
  execute: ReturnType<typeof mock>
}

interface MockSearchMedicationFavorites {
  execute: ReturnType<typeof mock>
}

describe('MedicationFavoriteController', () => {
  let controller: MedicationFavoriteController
  let mockAddMedicationFavorite: MockAddMedicationFavorite
  let mockRemoveMedicationFavorite: MockRemoveMedicationFavorite
  let mockListMedicationFavorites: MockListMedicationFavorites
  let mockSearchMedicationFavorites: MockSearchMedicationFavorites

  beforeEach(() => {
    mockAddMedicationFavorite = {
      execute: mock()
    }
    mockRemoveMedicationFavorite = {
      executeByMedication: mock(),
      executeById: mock()
    }
    mockListMedicationFavorites = {
      execute: mock()
    }
    mockSearchMedicationFavorites = {
      execute: mock()
    }

    controller = new MedicationFavoriteController(
      mockAddMedicationFavorite as unknown as AddMedicationFavorite,
      mockRemoveMedicationFavorite as unknown as RemoveMedicationFavorite,
      mockListMedicationFavorites as unknown as ListMedicationFavorites,
      mockSearchMedicationFavorites as unknown as SearchMedicationFavorites
    )
  })

  describe('addFavorite', () => {
    const medicationData = createMockMedicationData()

    it('should add favorite successfully', async () => {
      const favorite = createMockMedicationFavorite(TEST_FAVORITE_ID, TEST_USER_ID, medicationData)
      mockAddMedicationFavorite.execute.mockResolvedValue(favorite)

      const result = await controller.addFavorite({
        body: medicationData,
        token: TEST_ACCESS_TOKEN
      })

      expect(mockAddMedicationFavorite.execute).toHaveBeenCalledWith(TEST_ACCESS_TOKEN, medicationData)
      expect(result).toEqual({
        status: 201,
        data: favorite.toPublicJSON()
      })
    })

    it('should throw AuthenticationError when token is missing', async () => {
      await expect(
        controller.addFavorite({
          body: medicationData,
          token: null
        })
      ).rejects.toThrow('Unauthorized')

      expect(mockAddMedicationFavorite.execute).not.toHaveBeenCalled()
    })

    it('should throw ConflictError when medication is already in favorites', async () => {
      mockAddMedicationFavorite.execute.mockRejectedValue(new Error('Medication is already in favorites'))

      await expect(
        controller.addFavorite({
          body: medicationData,
          token: TEST_ACCESS_TOKEN
        })
      ).rejects.toThrow('Medication is already in favorites')
    })

    it('should throw AuthenticationError for invalid token errors', async () => {
      mockAddMedicationFavorite.execute.mockRejectedValue(new Error('Invalid token: user ID not found'))

      await expect(
        controller.addFavorite({
          body: medicationData,
          token: TEST_ACCESS_TOKEN
        })
      ).rejects.toThrow('Invalid token format')
    })

    it('should throw ValidationError for validation errors', async () => {
      mockAddMedicationFavorite.execute.mockRejectedValue(new Error('Product name is required'))

      await expect(
        controller.addFavorite({
          body: medicationData,
          token: TEST_ACCESS_TOKEN
        })
      ).rejects.toThrow('Product name is required')
    })

    it('should throw InternalServerError for unexpected errors', async () => {
      mockAddMedicationFavorite.execute.mockRejectedValue(new Error('Database connection failed'))

      await expect(
        controller.addFavorite({
          body: medicationData,
          token: TEST_ACCESS_TOKEN
        })
      ).rejects.toThrow('Database connection failed')
    })
  })

  describe('removeFavorite', () => {
    const medicationData = createMockMedicationData()

    it('should remove favorite successfully', async () => {
      mockRemoveMedicationFavorite.executeByMedication.mockResolvedValue(true)

      const result = await controller.removeFavorite({
        body: { codEstab: medicationData.codEstab, codProdE: medicationData.codProdE },
        token: TEST_ACCESS_TOKEN
      })

      expect(mockRemoveMedicationFavorite.executeByMedication).toHaveBeenCalledWith(
        TEST_ACCESS_TOKEN,
        medicationData.codEstab,
        medicationData.codProdE
      )
      expect(result).toEqual({
        status: 200,
        message: 'Medication removed from favorites'
      })
    })

    it('should throw AuthenticationError when token is missing', async () => {
      await expect(
        controller.removeFavorite({
          body: { codEstab: medicationData.codEstab, codProdE: medicationData.codProdE },
          token: null
        })
      ).rejects.toThrow('Unauthorized')
    })

    it('should throw NotFoundError when medication is not in favorites', async () => {
      mockRemoveMedicationFavorite.executeByMedication.mockRejectedValue(new Error('Medication is not in favorites'))

      await expect(
        controller.removeFavorite({
          body: { codEstab: medicationData.codEstab, codProdE: medicationData.codProdE },
          token: TEST_ACCESS_TOKEN
        })
      ).rejects.toThrow('Medication is not in favorites')
    })
  })

  describe('removeFavoriteById', () => {
    it('should remove favorite by ID successfully', async () => {
      mockRemoveMedicationFavorite.executeById.mockResolvedValue(true)

      const result = await controller.removeFavoriteById({
        params: { id: TEST_FAVORITE_ID },
        token: TEST_ACCESS_TOKEN
      })

      expect(mockRemoveMedicationFavorite.executeById).toHaveBeenCalledWith(TEST_ACCESS_TOKEN, TEST_FAVORITE_ID)
      expect(result).toEqual({
        status: 200,
        message: 'Medication removed from favorites'
      })
    })

    it('should return 401 when token is missing', async () => {
      expect(
        controller.removeFavoriteById({
          params: { id: TEST_FAVORITE_ID },
          token: null
        })
      ).rejects.toThrow('Unauthorized')
    })

    it('should return 404 when favorite is not found', async () => {
      mockRemoveMedicationFavorite.executeById.mockRejectedValue(new Error('Favorite not found or does not belong to user'))

      expect(
        controller.removeFavoriteById({
          params: { id: TEST_FAVORITE_ID },
          token: TEST_ACCESS_TOKEN
        })
      ).rejects.toThrow('Favorite not found or does not belong to user')
    })
  })

  describe('listFavorites', () => {
    it('should list favorites successfully with default pagination', async () => {
      const favorites = [createMockMedicationFavorite('fav-1', TEST_USER_ID)]
      const paginatedResult = createMockPaginatedResult(favorites, 1, 20, 1)
      mockListMedicationFavorites.execute.mockResolvedValue(paginatedResult)

      const result = await controller.listFavorites({
        query: {},
        token: TEST_ACCESS_TOKEN
      })

      expect(mockListMedicationFavorites.execute).toHaveBeenCalledWith(TEST_ACCESS_TOKEN, { page: 1, limit: 20 })
      expect(result).toEqual({
        status: 200,
        data: {
          favorites: favorites.map((f) => f.toPublicJSON()),
          pagination: paginatedResult.pagination
        }
      })
    })

    it('should list favorites with custom pagination', async () => {
      const favorites = [createMockMedicationFavorite('fav-1', TEST_USER_ID)]
      const paginatedResult = createMockPaginatedResult(favorites, 2, 10, 25)
      mockListMedicationFavorites.execute.mockResolvedValue(paginatedResult)

      const result = await controller.listFavorites({
        query: { page: '2', limit: '10' },
        token: TEST_ACCESS_TOKEN
      })

      expect(mockListMedicationFavorites.execute).toHaveBeenCalledWith(TEST_ACCESS_TOKEN, { page: 2, limit: 10 })
      expect(result.status).toBe(200)
    })

    it('should throw AuthenticationError when token is missing', async () => {
      await expect(
        controller.listFavorites({
          query: {},
          token: null
        })
      ).rejects.toThrow('Unauthorized')
    })

    it('should throw ValidationError for invalid pagination parameters', async () => {
      mockListMedicationFavorites.execute.mockRejectedValue(new Error('Page number must be greater than 0'))

      await expect(
        controller.listFavorites({
          query: { page: '0' },
          token: TEST_ACCESS_TOKEN
        })
      ).rejects.toThrow('Page number must be greater than 0')
    })
  })

  describe('searchFavorites', () => {
    it('should search favorites successfully', async () => {
      const favorites = [createMockMedicationFavorite('fav-1', TEST_USER_ID)]
      const paginatedResult = createMockPaginatedResult(favorites, 1, 20, 1)
      mockSearchMedicationFavorites.execute.mockResolvedValue(paginatedResult)

      const result = await controller.searchFavorites({
        query: { q: 'paracetamol' },
        token: TEST_ACCESS_TOKEN
      })

      expect(mockSearchMedicationFavorites.execute).toHaveBeenCalledWith(TEST_ACCESS_TOKEN, {
        query: 'paracetamol',
        page: 1,
        limit: 20
      })
      expect(result).toEqual({
        status: 200,
        data: {
          favorites: favorites.map((f) => f.toPublicJSON()),
          pagination: paginatedResult.pagination,
          query: 'paracetamol'
        }
      })
    })

    it('should search favorites with custom pagination', async () => {
      const paginatedResult = createMockPaginatedResult([], 2, 5, 0)
      mockSearchMedicationFavorites.execute.mockResolvedValue(paginatedResult)

      const result = await controller.searchFavorites({
        query: { q: 'ibuprofeno', page: '2', limit: '5' },
        token: TEST_ACCESS_TOKEN
      })

      expect(mockSearchMedicationFavorites.execute).toHaveBeenCalledWith(TEST_ACCESS_TOKEN, {
        query: 'ibuprofeno',
        page: 2,
        limit: 5
      })
      expect(result.status).toBe(200)
    })

    it('should throw AuthenticationError when token is missing', async () => {
      await expect(
        controller.searchFavorites({
          query: { q: 'test' },
          token: null
        })
      ).rejects.toThrow('Unauthorized')
    })

    it('should throw ValidationError for invalid search parameters', async () => {
      mockSearchMedicationFavorites.execute.mockRejectedValue(new Error('Search query is required'))

      await expect(
        controller.searchFavorites({
          query: { q: '' },
          token: TEST_ACCESS_TOKEN
        })
      ).rejects.toThrow('Search query is required')
    })
  })
})

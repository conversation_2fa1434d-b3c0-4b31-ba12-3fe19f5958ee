import { type Mock, mock } from 'bun:test'
import { JwtPayload } from 'jsonwebtoken'
import type { IJWT } from '../../services/interfaces/IJWT.js'
import type { IMedicationFavoriteRepository, PaginatedResult } from '../domain/IMedicationFavoriteRepository.js'
import { MedicationData, MedicationFavorite } from '../domain/MedicationFavorite.js'

// Utility type for mocked interfaces
export type Mocked<T> = {
  [K in keyof T]: T[K] extends (...args: any[]) => any ? Mock<T[K]> : T[K]
}

// Test data fixtures
export const createMockMedicationData = (overrides: Partial<MedicationData> = {}): MedicationData => ({
  codEstab: '0094084',
  codProdE: 8850,
  fecha: '21/04/2025 05:18:50 PM',
  nombreProducto: 'PARACETAMOL',
  precio1: 5.3,
  precio2: 0.05,
  precio3: null,
  codGrupoFF: '3',
  ubicodigo: '150110',
  direccion: 'AV. TUPAC AMARU CARABAYLLO 361',
  telefono: '01-3142020',
  nomGrupoFF: 'Tableta - Capsula',
  setcodigo: 'Privado',
  nombreComercial: 'BOTICA INKAFARMA',
  grupo: '2926',
  totalPA: '1',
  concent: '500 mg',
  nombreFormaFarmaceutica: 'Tableta',
  fracciones: 100,
  totalRegistros: null,
  nombreLaboratorio: 'INSTITUTO QUIMIOTERAPICO S.A.',
  nombreTitular: 'INSTITUTO QUIMIOTERAPICO S.A. - IQFARMA',
  catCodigo: '04',
  nombreSustancia: 'PARACETAMOL',
  fabricante: null,
  departamento: 'LIMA',
  provincia: 'LIMA',
  distrito: 'COMAS',
  ...overrides
})

export const createMockMedicationData2 = (): MedicationData => ({
  codEstab: '0118356',
  codProdE: 8830,
  fecha: '21/04/2025 05:20:30 PM',
  nombreProducto: 'IBUPROFENO',
  precio1: 8.5,
  precio2: 0.08,
  precio3: null,
  codGrupoFF: '3',
  ubicodigo: '150101',
  direccion: 'AV. JAVIER PRADO ESTE 4200',
  telefono: '01-4567890',
  nomGrupoFF: 'Tableta - Capsula',
  setcodigo: 'Privado',
  nombreComercial: 'FARMACIA UNIVERSAL',
  grupo: '2927',
  totalPA: '1',
  concent: '400 mg',
  nombreFormaFarmaceutica: 'Tableta',
  fracciones: 50,
  totalRegistros: null,
  nombreLaboratorio: 'LABORATORIO FARMACEUTICO S.A.',
  nombreTitular: 'LABORATORIO FARMACEUTICO S.A.',
  catCodigo: '04',
  nombreSustancia: 'IBUPROFENO',
  fabricante: null,
  departamento: 'LIMA',
  provincia: 'LIMA',
  distrito: 'LIMA'
})

export const createMockMedicationFavorite = (
  id: string = '507f1f77bcf86cd799439013',
  userId: string = '507f1f77bcf86cd799439014',
  medicationData: MedicationData = createMockMedicationData()
): MedicationFavorite => {
  return new MedicationFavorite(
    id,
    userId,
    medicationData.codEstab,
    medicationData.codProdE,
    medicationData,
    new Date('2025-01-27T10:00:00Z'),
    new Date('2025-01-27T10:00:00Z')
  )
}

// Mock JWT Service
export const createMockJwtService = (): Mocked<IJWT> => ({
  sign: mock() as unknown as Mock<IJWT['sign']>,
  verify: mock(() => ({ data: 'test-user-id' }) as JwtPayload) as unknown as Mock<IJWT['verify']>,
  verifyRefresh: mock() as unknown as Mock<IJWT['verifyRefresh']>,
  refreshAccessToken: mock() as unknown as Mock<IJWT['refreshAccessToken']>
})

// Mock Repository
export const createMockRepository = (): Mocked<IMedicationFavoriteRepository> => ({
  create: mock() as unknown as Mock<IMedicationFavoriteRepository['create']>,
  findByUserAndMedication: mock() as unknown as Mock<IMedicationFavoriteRepository['findByUserAndMedication']>,
  findByIdAndUser: mock() as unknown as Mock<IMedicationFavoriteRepository['findByIdAndUser']>,
  findByUser: mock() as unknown as Mock<IMedicationFavoriteRepository['findByUser']>,
  searchByUser: mock() as unknown as Mock<IMedicationFavoriteRepository['searchByUser']>,
  removeByIdAndUser: mock() as unknown as Mock<IMedicationFavoriteRepository['removeByIdAndUser']>,
  removeByUserAndMedication: mock() as unknown as Mock<IMedicationFavoriteRepository['removeByUserAndMedication']>,
  existsByUserAndMedication: mock() as unknown as Mock<IMedicationFavoriteRepository['existsByUserAndMedication']>,
  countByUser: mock() as unknown as Mock<IMedicationFavoriteRepository['countByUser']>,
  updateMedicationData: mock() as unknown as Mock<IMedicationFavoriteRepository['updateMedicationData']>
})

// Helper to create paginated result
export const createMockPaginatedResult = <T>(data: T[], page: number = 1, limit: number = 20, total: number = data.length): PaginatedResult<T> => {
  const totalPages = Math.ceil(total / limit)
  return {
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages,
      hasNext: page < totalPages,
      hasPrev: page > 1
    }
  }
}

// Test constants
export const TEST_USER_ID = '507f1f77bcf86cd799439011'
export const TEST_ACCESS_TOKEN = process.env.TEST_ACCESS_TOKEN || 'mock-test-token'
export const TEST_FAVORITE_ID = '507f1f77bcf86cd799439012'

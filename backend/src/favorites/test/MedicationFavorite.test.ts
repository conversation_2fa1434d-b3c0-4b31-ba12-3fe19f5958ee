import { describe, expect, it } from 'bun:test'
import { MedicationFavorite } from '../domain/MedicationFavorite.js'
import { TEST_USER_ID, createMockMedicationData, createMockMedicationData2 } from './testUtils.js'

describe('MedicationFavorite Entity', () => {
  const mockMedicationData = createMockMedicationData()
  const mockId = 'favorite-id-123'
  const mockCreatedAt = new Date('2025-01-27T10:00:00Z')
  const mockUpdatedAt = new Date('2025-01-27T10:00:00Z')

  describe('Constructor', () => {
    it('should create a MedicationFavorite instance with all properties', () => {
      const favorite = new MedicationFavorite(
        mockId,
        TEST_USER_ID,
        mockMedicationData.codEstab,
        mockMedicationData.codProdE,
        mockMedicationData,
        mockCreatedAt,
        mockUpdatedAt
      )

      expect(favorite.id).toBe(mockId)
      expect(favorite.userId).toBe(TEST_USER_ID)
      expect(favorite.codEstab).toBe(mockMedicationData.codEstab)
      expect(favorite.codProdE).toBe(mockMedicationData.codProdE)
      expect(favorite.medicationData).toEqual(mockMedicationData)
      expect(favorite.createdAt).toBe(mockCreatedAt)
      expect(favorite.updatedAt).toBe(mockUpdatedAt)
    })
  })

  describe('Static create method', () => {
    it('should create a new medication favorite without id and timestamps', () => {
      const favoriteData = MedicationFavorite.create(TEST_USER_ID, mockMedicationData)

      expect(favoriteData.userId).toBe(TEST_USER_ID)
      expect(favoriteData.codEstab).toBe(mockMedicationData.codEstab)
      expect(favoriteData.codProdE).toBe(mockMedicationData.codProdE)
      expect(favoriteData.medicationData).toEqual(mockMedicationData)
      expect('id' in favoriteData).toBe(false)
      expect('createdAt' in favoriteData).toBe(false)
      expect('updatedAt' in favoriteData).toBe(false)
    })
  })

  describe('getUniqueKey method', () => {
    it('should return a unique key combining userId, codEstab, and codProdE', () => {
      const favorite = new MedicationFavorite(
        mockId,
        TEST_USER_ID,
        mockMedicationData.codEstab,
        mockMedicationData.codProdE,
        mockMedicationData,
        mockCreatedAt,
        mockUpdatedAt
      )

      const expectedKey = `${TEST_USER_ID}-${mockMedicationData.codEstab}-${mockMedicationData.codProdE}`
      expect(favorite.getUniqueKey()).toBe(expectedKey)
    })
  })

  describe('matches method', () => {
    const favorite = new MedicationFavorite(
      mockId,
      TEST_USER_ID,
      mockMedicationData.codEstab,
      mockMedicationData.codProdE,
      mockMedicationData,
      mockCreatedAt,
      mockUpdatedAt
    )

    it('should return true when codEstab and codProdE match', () => {
      expect(favorite.matches(mockMedicationData.codEstab, mockMedicationData.codProdE)).toBe(true)
    })

    it('should return false when codEstab does not match', () => {
      expect(favorite.matches('different-estab', mockMedicationData.codProdE)).toBe(false)
    })

    it('should return false when codProdE does not match', () => {
      expect(favorite.matches(mockMedicationData.codEstab, 9999)).toBe(false)
    })

    it('should return false when both do not match', () => {
      expect(favorite.matches('different-estab', 9999)).toBe(false)
    })
  })

  describe('Helper methods', () => {
    const favorite = new MedicationFavorite(
      mockId,
      TEST_USER_ID,
      mockMedicationData.codEstab,
      mockMedicationData.codProdE,
      mockMedicationData,
      mockCreatedAt,
      mockUpdatedAt
    )

    it('getMedicationName should return the product name', () => {
      expect(favorite.getMedicationName()).toBe(mockMedicationData.nombreProducto)
    })

    it('getEstablishmentName should return the commercial name', () => {
      expect(favorite.getEstablishmentName()).toBe(mockMedicationData.nombreComercial)
    })

    it('getPrimaryPrice should return precio1', () => {
      expect(favorite.getPrimaryPrice()).toBe(mockMedicationData.precio1)
    })
  })

  describe('JSON serialization', () => {
    const favorite = new MedicationFavorite(
      mockId,
      TEST_USER_ID,
      mockMedicationData.codEstab,
      mockMedicationData.codProdE,
      mockMedicationData,
      mockCreatedAt,
      mockUpdatedAt
    )

    it('toJSON should return complete object including userId', () => {
      const json = favorite.toJSON()

      expect(json).toEqual({
        id: mockId,
        userId: TEST_USER_ID,
        codEstab: mockMedicationData.codEstab,
        codProdE: mockMedicationData.codProdE,
        medicationData: mockMedicationData,
        createdAt: mockCreatedAt,
        updatedAt: mockUpdatedAt
      })
    })

    it('toPublicJSON should return object without userId', () => {
      const publicJson = favorite.toPublicJSON()

      expect(publicJson).toEqual({
        id: mockId,
        codEstab: mockMedicationData.codEstab,
        codProdE: mockMedicationData.codProdE,
        medicationData: mockMedicationData,
        createdAt: mockCreatedAt,
        updatedAt: mockUpdatedAt
      })
      expect('userId' in publicJson).toBe(false)
    })
  })

  describe('Different medication data', () => {
    it('should handle different medication data correctly', () => {
      const medicationData2 = createMockMedicationData2()
      const favorite = new MedicationFavorite(
        'favorite-2',
        TEST_USER_ID,
        medicationData2.codEstab,
        medicationData2.codProdE,
        medicationData2,
        mockCreatedAt,
        mockUpdatedAt
      )

      expect(favorite.getMedicationName()).toBe('IBUPROFENO')
      expect(favorite.getEstablishmentName()).toBe('FARMACIA UNIVERSAL')
      expect(favorite.getPrimaryPrice()).toBe(8.5)
      expect(favorite.matches(medicationData2.codEstab, medicationData2.codProdE)).toBe(true)
      expect(favorite.matches(mockMedicationData.codEstab, mockMedicationData.codProdE)).toBe(false)
    })
  })
})

import { beforeEach, describe, expect, it } from 'bun:test'
import type { IJWT } from '../../services/interfaces/IJWT.js'
import { SearchMedicationFavorites } from '../application/SearchMedicationFavorites.js'
import type { IMedicationFavoriteRepository } from '../domain/IMedicationFavoriteRepository.js'
import {
  type Mocked,
  TEST_ACCESS_TOKEN,
  TEST_USER_ID,
  createMockJwtService,
  createMockMedicationData,
  createMockMedicationFavorite,
  createMockPaginatedResult,
  createMockRepository
} from './testUtils.js'

describe('SearchMedicationFavorites Use Case', () => {
  let searchMedicationFavorites: SearchMedicationFavorites
  let mockRepository: Mocked<IMedicationFavoriteRepository>
  let mockJwtService: Mocked<IJWT>

  beforeEach(() => {
    mockRepository = createMockRepository()
    mockJwtService = createMockJwtService()
    searchMedicationFavorites = new SearchMedicationFavorites(mockRepository, mockJwtService)
  })

  describe('Successful execution', () => {
    it('should search favorites with default pagination', async () => {
      const favorites = [createMockMedicationFavorite('fav-1', TEST_USER_ID, createMockMedicationData({ nombreProducto: 'PARACETAMOL' }))]
      const paginatedResult = createMockPaginatedResult(favorites, 1, 20, 1)

      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.searchByUser.mockResolvedValue(paginatedResult)

      const result = await searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'paracetamol' })

      expect(mockJwtService.verify).toHaveBeenCalledWith(TEST_ACCESS_TOKEN)
      expect(mockRepository.searchByUser).toHaveBeenCalledWith(TEST_USER_ID, {
        query: 'paracetamol',
        page: 1,
        limit: 20
      })
      expect(result).toBe(paginatedResult)
    })

    it('should search favorites with custom pagination', async () => {
      const favorites = [createMockMedicationFavorite('fav-1', TEST_USER_ID)]
      const paginatedResult = createMockPaginatedResult(favorites, 2, 10, 15)

      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.searchByUser.mockResolvedValue(paginatedResult)

      const result = await searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, {
        query: 'ibuprofeno',
        page: 2,
        limit: 10
      })

      expect(mockRepository.searchByUser).toHaveBeenCalledWith(TEST_USER_ID, {
        query: 'ibuprofeno',
        page: 2,
        limit: 10
      })
      expect(result).toBe(paginatedResult)
    })

    it('should trim whitespace from query', async () => {
      const paginatedResult = createMockPaginatedResult([], 1, 20, 0)

      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.searchByUser.mockResolvedValue(paginatedResult)

      await searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: '  paracetamol  ' })

      expect(mockRepository.searchByUser).toHaveBeenCalledWith(TEST_USER_ID, {
        query: 'paracetamol',
        page: 1,
        limit: 20
      })
    })

    it('should handle empty search results', async () => {
      const paginatedResult = createMockPaginatedResult([], 1, 20, 0)

      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      mockRepository.searchByUser.mockResolvedValue(paginatedResult)

      const result = await searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'nonexistent' })

      expect(result.data).toEqual([])
      expect(result.pagination.total).toBe(0)
    })
  })

  describe('Authentication errors', () => {
    it('should throw error when token is invalid', async () => {
      mockJwtService.verify.mockImplementation(() => {
        throw new Error('Invalid token')
      })

      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'test' })).rejects.toThrow('Invalid token')
    })

    it('should throw error when user ID is not found in token', async () => {
      mockJwtService.verify.mockReturnValue({ data: null })

      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'test' })).rejects.toThrow('Invalid token: user ID not found')
    })

    it('should throw error when user ID is undefined in token', async () => {
      mockJwtService.verify.mockReturnValue({})

      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'test' })).rejects.toThrow('Invalid token: user ID not found')
    })
  })

  describe('Query validation', () => {
    beforeEach(() => {
      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
    })

    it('should throw error when query is missing', async () => {
      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, {})).rejects.toThrow('Search query is required')
    })

    it('should throw error when query is empty string', async () => {
      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: '' })).rejects.toThrow('Search query is required')
    })

    it('should throw error when query is only whitespace', async () => {
      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: '   ' })).rejects.toThrow('Search query is required')
    })

    it('should throw error when query is too short', async () => {
      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'a' })).rejects.toThrow('Search query must be at least 2 characters long')
    })

    it('should accept query with exactly 2 characters', async () => {
      const paginatedResult = createMockPaginatedResult([], 1, 20, 0)
      mockRepository.searchByUser.mockResolvedValue(paginatedResult)

      const result = await searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'ab' })

      expect(mockRepository.searchByUser).toHaveBeenCalledWith(TEST_USER_ID, {
        query: 'ab',
        page: 1,
        limit: 20
      })
      expect(result).toBe(paginatedResult)
    })

    it('should throw error when query is too long', async () => {
      const longQuery = 'a'.repeat(101)
      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: longQuery })).rejects.toThrow('Search query cannot exceed 100 characters')
    })

    it('should accept query with exactly 100 characters', async () => {
      const maxQuery = 'a'.repeat(100)
      const paginatedResult = createMockPaginatedResult([], 1, 20, 0)
      mockRepository.searchByUser.mockResolvedValue(paginatedResult)

      const result = await searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: maxQuery })

      expect(mockRepository.searchByUser).toHaveBeenCalledWith(TEST_USER_ID, {
        query: maxQuery,
        page: 1,
        limit: 20
      })
      expect(result).toBe(paginatedResult)
    })
  })

  describe('Pagination validation', () => {
    beforeEach(() => {
      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
    })

    it('should throw error when page is less than 1', async () => {
      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'test', page: 0 })).rejects.toThrow('Page number must be greater than 0')
      expect(mockRepository.searchByUser).not.toHaveBeenCalled()
    })

    it('should throw error when page is negative', async () => {
      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'test', page: -1 })).rejects.toThrow('Page number must be greater than 0')
      expect(mockRepository.searchByUser).not.toHaveBeenCalled()
    })

    it('should throw error when limit is less than 1', async () => {
      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'test', limit: 0 })).rejects.toThrow('Limit must be greater than 0')
      expect(mockRepository.searchByUser).not.toHaveBeenCalled()
    })

    it('should throw error when limit is negative', async () => {
      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'test', limit: -1 })).rejects.toThrow('Limit must be greater than 0')
      expect(mockRepository.searchByUser).not.toHaveBeenCalled()
    })

    it('should throw error when limit exceeds maximum', async () => {
      expect(searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'test', limit: 101 })).rejects.toThrow(
        'Limit cannot exceed 100 items per page'
      )
    })

    it('should accept limit of exactly 100', async () => {
      const paginatedResult = createMockPaginatedResult([], 1, 100, 0)
      mockRepository.searchByUser.mockResolvedValue(paginatedResult)

      const result = await searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'test', limit: 100 })

      expect(mockRepository.searchByUser).toHaveBeenCalledWith(TEST_USER_ID, {
        query: 'test',
        page: 1,
        limit: 100
      })
      expect(result).toBe(paginatedResult)
    })
  })

  describe('Repository integration', () => {
    beforeEach(() => {
      mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
    })

    it('should pass through repository results correctly', async () => {
      const favorites = [
        createMockMedicationFavorite('fav-1', TEST_USER_ID, createMockMedicationData({ nombreProducto: 'PARACETAMOL 500mg' })),
        createMockMedicationFavorite('fav-2', TEST_USER_ID, createMockMedicationData({ nombreProducto: 'PARACETAMOL 1000mg' }))
      ]
      const paginatedResult = createMockPaginatedResult(favorites, 1, 20, 2)

      mockRepository.searchByUser.mockResolvedValue(paginatedResult)

      const result = await searchMedicationFavorites.execute(TEST_ACCESS_TOKEN, { query: 'paracetamol' })

      expect(result.data).toHaveLength(2)
      expect(result.data[0].getMedicationName()).toBe('PARACETAMOL 500mg')
      expect(result.data[1].getMedicationName()).toBe('PARACETAMOL 1000mg')
      expect(result.pagination.total).toBe(2)
    })
  })
})

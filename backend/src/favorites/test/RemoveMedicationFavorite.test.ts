import { beforeEach, describe, expect, it } from 'bun:test'
import type { IJWT } from '../../services/interfaces/IJWT.js'
import { RemoveMedicationFavorite } from '../application/RemoveMedicationFavorite.js'
import type { IMedicationFavoriteRepository } from '../domain/IMedicationFavoriteRepository.js'
import {
  type Mocked,
  TEST_ACCESS_TOKEN,
  TEST_FAVORITE_ID,
  TEST_USER_ID,
  createMockJwtService,
  createMockMedicationData,
  createMockMedicationFavorite,
  createMockRepository
} from './testUtils.js'

describe('RemoveMedicationFavorite Use Case', () => {
  let removeMedicationFavorite: RemoveMedicationFavorite
  let mockRepository: Mocked<IMedicationFavoriteRepository>
  let mockJwtService: Mocked<IJWT>

  beforeEach(() => {
    mockRepository = createMockRepository()
    mockJwtService = createMockJwtService()
    removeMedicationFavorite = new RemoveMedicationFavorite(mockRepository, mockJwtService)
  })

  describe('executeById method', () => {
    describe('Successful execution', () => {
      it('should remove favorite by ID successfully', async () => {
        const existingFavorite = createMockMedicationFavorite(TEST_FAVORITE_ID, TEST_USER_ID)

        mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
        mockRepository.findByIdAndUser.mockResolvedValue(existingFavorite)
        mockRepository.removeByIdAndUser.mockResolvedValue(true)

        const result = await removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, TEST_FAVORITE_ID)

        expect(mockJwtService.verify).toHaveBeenCalledWith(TEST_ACCESS_TOKEN)
        expect(mockRepository.findByIdAndUser).toHaveBeenCalledWith(TEST_FAVORITE_ID, TEST_USER_ID)
        expect(mockRepository.removeByIdAndUser).toHaveBeenCalledWith(TEST_FAVORITE_ID, TEST_USER_ID)
        expect(result).toBe(true)
      })
    })

    describe('Authentication errors', () => {
      it('should throw error when token is invalid', async () => {
        mockJwtService.verify.mockImplementation(() => {
          throw new Error('Invalid token')
        })

        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, TEST_FAVORITE_ID)).rejects.toThrow('Invalid token')
      })

      it('should throw error when user ID is not found in token', async () => {
        mockJwtService.verify.mockReturnValue({ data: null })

        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, TEST_FAVORITE_ID)).rejects.toThrow('Invalid token: user ID not found')
      })
    })

    describe('Validation errors', () => {
      beforeEach(() => {
        mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      })

      it('should throw error when favorite ID is empty', async () => {
        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, '')).rejects.toThrow('Favorite ID is required')
      })

      it('should throw error when favorite ID is only whitespace', async () => {
        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, '   ')).rejects.toThrow('Favorite ID is required')
      })

      it('should throw error when favorite ID format is invalid', async () => {
        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, 'invalid-id')).rejects.toThrow('Invalid favorite ID format')
      })

      it('should throw error when favorite ID is too short', async () => {
        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, '123')).rejects.toThrow('Invalid favorite ID format')
      })

      it('should throw error when favorite ID contains non-hex characters', async () => {
        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, '68338873dfc504ae02a0773g')).rejects.toThrow('Invalid favorite ID format')
      })

      it('should accept valid ObjectId format', async () => {
        const validObjectId = '68338873dfc504ae02a0773c'
        mockRepository.findByIdAndUser.mockResolvedValue(null)

        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, validObjectId)).rejects.toThrow(
          'Favorite not found or does not belong to user'
        )
      })
    })

    describe('Authorization errors', () => {
      beforeEach(() => {
        mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      })

      it('should throw error when favorite does not exist', async () => {
        mockRepository.findByIdAndUser.mockResolvedValue(null)

        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, TEST_FAVORITE_ID)).rejects.toThrow(
          'Favorite not found or does not belong to user'
        )
      })

      it('should throw error when favorite belongs to different user', async () => {
        // This is handled by findByIdAndUser returning null for different user
        mockRepository.findByIdAndUser.mockResolvedValue(null)

        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, TEST_FAVORITE_ID)).rejects.toThrow(
          'Favorite not found or does not belong to user'
        )
      })
    })

    describe('Repository errors', () => {
      beforeEach(() => {
        mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
        const existingFavorite = createMockMedicationFavorite(TEST_FAVORITE_ID, TEST_USER_ID)
        mockRepository.findByIdAndUser.mockResolvedValue(existingFavorite)
      })

      it('should throw error when removal fails', async () => {
        mockRepository.removeByIdAndUser.mockResolvedValue(false)

        expect(removeMedicationFavorite.executeById(TEST_ACCESS_TOKEN, TEST_FAVORITE_ID)).rejects.toThrow('Failed to remove favorite')
      })
    })
  })

  describe('executeByMedication method', () => {
    const medicationData = createMockMedicationData()

    describe('Successful execution', () => {
      it('should remove favorite by medication identifiers successfully', async () => {
        const existingFavorite = createMockMedicationFavorite(TEST_FAVORITE_ID, TEST_USER_ID, medicationData)

        mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
        mockRepository.findByUserAndMedication.mockResolvedValue(existingFavorite)
        mockRepository.removeByUserAndMedication.mockResolvedValue(true)

        const result = await removeMedicationFavorite.executeByMedication(TEST_ACCESS_TOKEN, medicationData.codEstab, medicationData.codProdE)

        expect(mockJwtService.verify).toHaveBeenCalledWith(TEST_ACCESS_TOKEN)
        expect(mockRepository.findByUserAndMedication).toHaveBeenCalledWith(TEST_USER_ID, medicationData.codEstab, medicationData.codProdE)
        expect(mockRepository.removeByUserAndMedication).toHaveBeenCalledWith(TEST_USER_ID, medicationData.codEstab, medicationData.codProdE)
        expect(result).toBe(true)
      })
    })

    describe('Authentication errors', () => {
      it('should throw error when token is invalid', async () => {
        mockJwtService.verify.mockImplementation(() => {
          throw new Error('Invalid token')
        })

        expect(removeMedicationFavorite.executeByMedication(TEST_ACCESS_TOKEN, medicationData.codEstab, medicationData.codProdE)).rejects.toThrow(
          'Invalid token'
        )
      })

      it('should throw error when user ID is not found in token', async () => {
        mockJwtService.verify.mockReturnValue({ data: null })

        expect(removeMedicationFavorite.executeByMedication(TEST_ACCESS_TOKEN, medicationData.codEstab, medicationData.codProdE)).rejects.toThrow(
          'Invalid token: user ID not found'
        )
      })
    })

    describe('Validation errors', () => {
      beforeEach(() => {
        mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      })

      it('should throw error when codEstab is empty', async () => {
        expect(removeMedicationFavorite.executeByMedication(TEST_ACCESS_TOKEN, '', medicationData.codProdE)).rejects.toThrow(
          'Establishment code is required'
        )
      })

      it('should throw error when codEstab is only whitespace', async () => {
        expect(removeMedicationFavorite.executeByMedication(TEST_ACCESS_TOKEN, '   ', medicationData.codProdE)).rejects.toThrow(
          'Establishment code is required'
        )
      })

      it('should throw error when codProdE is zero', async () => {
        expect(removeMedicationFavorite.executeByMedication(TEST_ACCESS_TOKEN, medicationData.codEstab, 0)).rejects.toThrow(
          'Product code is required and must be positive'
        )
      })

      it('should throw error when codProdE is negative', async () => {
        expect(removeMedicationFavorite.executeByMedication(TEST_ACCESS_TOKEN, medicationData.codEstab, -1)).rejects.toThrow(
          'Product code is required and must be positive'
        )
      })
    })

    describe('Not found errors', () => {
      beforeEach(() => {
        mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
      })

      it('should throw error when medication is not in favorites', async () => {
        mockRepository.findByUserAndMedication.mockResolvedValue(null)

        expect(removeMedicationFavorite.executeByMedication(TEST_ACCESS_TOKEN, medicationData.codEstab, medicationData.codProdE)).rejects.toThrow(
          'Medication is not in favorites'
        )
      })
    })

    describe('Repository errors', () => {
      beforeEach(() => {
        mockJwtService.verify.mockReturnValue({ data: TEST_USER_ID })
        const existingFavorite = createMockMedicationFavorite(TEST_FAVORITE_ID, TEST_USER_ID, medicationData)
        mockRepository.findByUserAndMedication.mockResolvedValue(existingFavorite)
      })

      it('should throw error when removal fails', async () => {
        mockRepository.removeByUserAndMedication.mockResolvedValue(false)

        expect(removeMedicationFavorite.executeByMedication(TEST_ACCESS_TOKEN, medicationData.codEstab, medicationData.codProdE)).rejects.toThrow(
          'Failed to remove favorite'
        )
      })
    })
  })
})

import { afterEach, beforeEach, describe, expect, it, mock } from 'bun:test'
import { Elysia, t } from 'elysia'
import { TEST_USER_ID, createMockMedicationData, createMockMedicationFavorite } from './testUtils.js'

// Mock controller
const mockController = {
  addFavorite: mock(),
  removeFavorite: mock(),
  removeFavoriteById: mock(),
  listFavorites: mock(),
  searchFavorites: mock()
}

// Create a test router that mimics the real one but uses our mock controller
const createTestRouter = () => {
  const addMedicationFavoriteDTO = {
    body: t.Object({
      codEstab: t.String(),
      codProdE: t.Number(),
      nombreProducto: t.String(),
      precio1: t.Number(),
      precio2: t.Optional(t.Number()),
      precio3: t.Optional(t.Union([t.Number(), t.Null()])),
      fecha: t.String(),
      codGrupoFF: t.String(),
      ubicodigo: t.String(),
      direccion: t.String(),
      telefono: t.String(),
      nomGrupoFF: t.String(),
      setcodigo: t.String(),
      nombreComercial: t.String(),
      grupo: t.String(),
      totalPA: t.String(),
      concent: t.String(),
      nombreFormaFarmaceutica: t.String(),
      fracciones: t.Number(),
      totalRegistros: t.Optional(t.Union([t.String(), t.Null()])),
      nombreLaboratorio: t.String(),
      nombreTitular: t.String(),
      catCodigo: t.String(),
      nombreSustancia: t.String(),
      departamento: t.Optional(t.String()),
      provincia: t.Optional(t.String()),
      distrito: t.Optional(t.String()),
      fabricante: t.Optional(t.Union([t.String(), t.Null()]))
    })
  }

  const removeMedicationFavoriteDTO = {
    body: t.Object({
      codEstab: t.String(),
      codProdE: t.Number()
    })
  }

  const removeMedicationFavoriteByIdDTO = {
    params: t.Object({
      id: t.String()
    })
  }

  return new Elysia({ prefix: '/favorites' })
    .derive(({ headers }) => {
      try {
        const auth = headers?.['authorization'] ?? headers?.['Authorization']
        if (auth && typeof auth === 'string' && auth.startsWith('Bearer ')) {
          return { token: auth.slice(7) }
        }
        return { token: null }
      } catch (_error) {
        return { token: null }
      }
    })
    .post(
      '/medications',
      async (context) =>
        await mockController.addFavorite({
          body: context.body,
          token: context.token
        }),
      addMedicationFavoriteDTO
    )
    .delete(
      '/medications',
      async (context) =>
        await mockController.removeFavorite({
          body: context.body,
          token: context.token
        }),
      removeMedicationFavoriteDTO
    )
    .delete(
      '/medications/:id',
      async (context) =>
        await mockController.removeFavoriteById({
          params: context.params,
          token: context.token
        }),
      removeMedicationFavoriteByIdDTO
    )
    .get(
      '/medications',
      async (context) =>
        await mockController.listFavorites({
          query: context.query,
          token: context.token
        })
    )
    .get(
      '/medications/search',
      async (context) =>
        await mockController.searchFavorites({
          query: context.query,
          token: context.token
        })
    )
}

// Test app setup
let app: Elysia

describe('favoritesRouter', () => {
  beforeEach(() => {
    // Reset all mocks
    Object.values(mockController).forEach((mockFn) => mockFn.mockReset())

    // Create test app with our test router
    app = new Elysia().use(createTestRouter())
  })

  afterEach(() => {
    // Clean up after each test
    app = null as any
  })

  describe('POST /favorites/medications', () => {
    it('should add medication to favorites with valid data', async () => {
      const medicationData = createMockMedicationData()
      const expectedFavorite = createMockMedicationFavorite('test-id', TEST_USER_ID, medicationData)

      mockController.addFavorite.mockResolvedValue({
        success: true,
        data: expectedFavorite,
        message: 'Medication added to favorites'
      })

      const response = await app.handle(
        new Request('http://localhost/favorites/medications', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer valid-token'
          },
          body: JSON.stringify(medicationData)
        })
      )

      expect(response.status).toBe(200)
      expect(mockController.addFavorite).toHaveBeenCalledTimes(1)
      const callArgs = (mockController.addFavorite as any).mock.calls[0][0]
      expect(callArgs.token).toBe('valid-token')
      expect(callArgs.body.codEstab).toBe(medicationData.codEstab)
      expect(callArgs.body.codProdE).toBe(medicationData.codProdE)
      expect(callArgs.body.nombreProducto).toBe(medicationData.nombreProducto)
    })

    it('should handle missing authorization header', async () => {
      const medicationData = createMockMedicationData()

      const _response = await app.handle(
        new Request('http://localhost/favorites/medications', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(medicationData)
        })
      )

      expect(mockController.addFavorite).toHaveBeenCalledTimes(1)
      const callArgs = (mockController.addFavorite as any).mock.calls[0][0]
      expect(callArgs.token).toBe(null)
      expect(callArgs.body.codEstab).toBe(medicationData.codEstab)
      expect(callArgs.body.codProdE).toBe(medicationData.codProdE)
      expect(callArgs.body.nombreProducto).toBe(medicationData.nombreProducto)
    })
  })

  describe('DELETE /favorites/medications', () => {
    it('should remove medication from favorites by identifiers', async () => {
      const removeData = { codEstab: '0094084', codProdE: 8850 }

      mockController.removeFavorite.mockResolvedValue({
        success: true,
        message: 'Medication removed from favorites'
      })

      const response = await app.handle(
        new Request('http://localhost/favorites/medications', {
          method: 'DELETE',
          headers: {
            'Content-Type': 'application/json',
            Authorization: 'Bearer valid-token'
          },
          body: JSON.stringify(removeData)
        })
      )

      expect(response.status).toBe(200)
      expect(mockController.removeFavorite).toHaveBeenCalledWith({
        body: removeData,
        token: 'valid-token'
      })
    })
  })

  describe('DELETE /favorites/medications/:id', () => {
    it('should remove medication from favorites by ID', async () => {
      const favoriteId = 'test-favorite-id'

      mockController.removeFavoriteById.mockResolvedValue({
        success: true,
        message: 'Medication removed from favorites'
      })

      const response = await app.handle(
        new Request(`http://localhost/favorites/medications/${favoriteId}`, {
          method: 'DELETE',
          headers: {
            Authorization: 'Bearer valid-token'
          }
        })
      )

      expect(response.status).toBe(200)
      expect(mockController.removeFavoriteById).toHaveBeenCalledWith({
        params: { id: favoriteId },
        token: 'valid-token'
      })
    })
  })

  describe('GET /favorites/medications', () => {
    it('should list favorites with default pagination', async () => {
      const mockFavorites = [createMockMedicationFavorite('1', TEST_USER_ID), createMockMedicationFavorite('2', TEST_USER_ID)]

      mockController.listFavorites.mockResolvedValue({
        success: true,
        data: {
          favorites: mockFavorites,
          pagination: {
            page: 1,
            limit: 20,
            total: 2,
            totalPages: 1
          }
        }
      })

      const response = await app.handle(
        new Request('http://localhost/favorites/medications', {
          method: 'GET',
          headers: {
            Authorization: 'Bearer valid-token'
          }
        })
      )

      expect(response.status).toBe(200)
      expect(mockController.listFavorites).toHaveBeenCalledWith({
        query: {},
        token: 'valid-token'
      })
    })

    it('should list favorites with custom pagination', async () => {
      const _response = await app.handle(
        new Request('http://localhost/favorites/medications?page=2&limit=10', {
          method: 'GET',
          headers: {
            Authorization: 'Bearer valid-token'
          }
        })
      )

      expect(mockController.listFavorites).toHaveBeenCalledWith({
        query: { page: '2', limit: '10' },
        token: 'valid-token'
      })
    })
  })

  describe('GET /favorites/medications/search', () => {
    it('should search favorites with query parameter', async () => {
      const searchQuery = 'PARACETAMOL'
      const mockResults = [createMockMedicationFavorite('1', TEST_USER_ID)]

      mockController.searchFavorites.mockResolvedValue({
        success: true,
        data: {
          favorites: mockResults,
          pagination: {
            page: 1,
            limit: 20,
            total: 1,
            totalPages: 1
          }
        }
      })

      const response = await app.handle(
        new Request(`http://localhost/favorites/medications/search?q=${searchQuery}`, {
          method: 'GET',
          headers: {
            Authorization: 'Bearer valid-token'
          }
        })
      )

      expect(response.status).toBe(200)
      expect(mockController.searchFavorites).toHaveBeenCalledWith({
        query: { q: searchQuery },
        token: 'valid-token'
      })
    })
  })

  describe('Authorization header parsing', () => {
    it('should extract token from Bearer authorization header', async () => {
      const _response = await app.handle(
        new Request('http://localhost/favorites/medications', {
          method: 'GET',
          headers: {
            Authorization: 'Bearer test-token-123'
          }
        })
      )

      expect(mockController.listFavorites).toHaveBeenCalledWith({
        query: {},
        token: 'test-token-123'
      })
    })

    it('should handle malformed authorization header', async () => {
      const _response = await app.handle(
        new Request('http://localhost/favorites/medications', {
          method: 'GET',
          headers: {
            Authorization: 'InvalidFormat'
          }
        })
      )

      expect(mockController.listFavorites).toHaveBeenCalledWith({
        query: {},
        token: null
      })
    })
  })
})

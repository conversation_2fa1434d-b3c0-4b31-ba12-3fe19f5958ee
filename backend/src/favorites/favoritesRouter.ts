import { Elysia, t } from 'elysia'
import { medicationFavoriteController } from '../server/dependencies.js'
import {
  addMedicationFavoriteDTO,
  listMedicationFavoritesDTO,
  removeMedicationFavoriteDTO,
  searchMedicationFavoritesDTO
} from './domain/medicationFavoriteDTO.js'

// DTO for removing by ID (path parameter)
const removeMedicationFavoriteByIdDTO = {
  params: t.Object({
    id: t.String()
  })
}

export const favoritesRouter = new Elysia({ prefix: '/favorites' })
  .derive(({ headers }) => {
    try {
      const auth = headers?.['authorization'] ?? headers?.['Authorization']
      if (auth && typeof auth === 'string' && auth.startsWith('Bearer ')) {
        return { token: auth.slice(7) }
      }
      return { token: null }
    } catch (_error) {
      return { token: null }
    }
  })
  // Add medication to favorites
  .post(
    '/medications',
    async (context) =>
      await medicationFavoriteController.addFavorite({
        body: context.body,
        token: context.token
      }),
    addMedicationFavoriteDTO
  )

  // Remove medication from favorites by medication identifiers
  .delete(
    '/medications',
    async (context) =>
      await medicationFavoriteController.removeFavorite({
        body: context.body,
        token: context.token
      }),
    removeMedicationFavoriteDTO
  )

  // Remove medication from favorites by favorite ID
  .delete(
    '/medications/:id',
    async (context) =>
      await medicationFavoriteController.removeFavoriteById({
        params: context.params,
        token: context.token
      }),
    removeMedicationFavoriteByIdDTO
  )

  // List user's favorite medications with pagination
  .get(
    '/medications',
    async (context) =>
      await medicationFavoriteController.listFavorites({
        query: context.query,
        token: context.token
      }),
    listMedicationFavoritesDTO
  )

  // Search within user's favorite medications
  .get(
    '/medications/search',
    async (context) =>
      await medicationFavoriteController.searchFavorites({
        query: context.query,
        token: context.token
      }),
    searchMedicationFavoritesDTO
  )

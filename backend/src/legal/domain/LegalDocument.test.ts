import { describe, expect, it } from 'bun:test'
import { LegalDocument } from './LegalDocument.js'

describe('LegalDocument', () => {
  const mockMetadata = {
    lastUpdated: new Date('2024-12-28'),
    version: '1.0',
    language: 'es'
  }

  describe('constructor', () => {
    it('should create a legal document with all properties', () => {
      const document = new LegalDocument('test-id', 'privacy-policy', 'Test Title', '<p>Test content</p>', mockMetadata)

      expect(document.id).toBe('test-id')
      expect(document.type).toBe('privacy-policy')
      expect(document.title).toBe('Test Title')
      expect(document.content).toBe('<p>Test content</p>')
      expect(document.metadata).toEqual(mockMetadata)
    })
  })

  describe('toHTML', () => {
    it('should generate valid HTML with proper structure', () => {
      const document = new LegalDocument('test-id', 'privacy-policy', 'Test Privacy Policy', '<h2>Section 1</h2><p>Content here</p>', mockMetadata)

      const html = document.toHTML()

      expect(html).toContain('<!DOCTYPE html>')
      expect(html).toContain('<html lang="es">')
      expect(html).toContain('<meta charset="UTF-8">')
      expect(html).toContain('<title>Test Privacy Policy</title>')
      expect(html).toContain('<h1>Test Privacy Policy</h1>')
      expect(html).toContain('<h2>Section 1</h2><p>Content here</p>')
    })

    it('should include metadata in HTML', () => {
      const document = new LegalDocument('test-id', 'privacy-policy', 'Test Title', '<p>Content</p>', mockMetadata)

      const html = document.toHTML()

      expect(html).toContain('Última actualización:')
      expect(html).toContain('28/12/2024')
      expect(html).toContain('Versión:</strong> 1.0')
    })

    it('should include CSS styling', () => {
      const document = new LegalDocument('test-id', 'privacy-policy', 'Test Title', '<p>Content</p>', mockMetadata)

      const html = document.toHTML()

      expect(html).toContain('<style>')
      expect(html).toContain('font-family:')
      expect(html).toContain('background-color:')
      expect(html).toContain('border-radius:')
    })

    it('should handle different languages', () => {
      const englishMetadata = {
        ...mockMetadata,
        language: 'en'
      }

      const document = new LegalDocument('test-id', 'privacy-policy', 'Privacy Policy', '<p>English content</p>', englishMetadata)

      const html = document.toHTML()

      expect(html).toContain('<html lang="en">')
    })
  })

  describe('toJSON', () => {
    it('should serialize document to JSON format', () => {
      const document = new LegalDocument('test-id', 'privacy-policy', 'Test Title', '<p>Content</p>', mockMetadata)

      const json = document.toJSON()

      expect(json).toEqual({
        id: 'test-id',
        type: 'privacy-policy',
        title: 'Test Title',
        content: '<p>Content</p>',
        metadata: {
          lastUpdated: '2024-12-28T00:00:00.000Z',
          version: '1.0',
          language: 'es'
        }
      })
    })

    it('should convert date to ISO string', () => {
      const document = new LegalDocument('test-id', 'privacy-policy', 'Test Title', '<p>Content</p>', mockMetadata)

      const json = document.toJSON()

      expect(typeof json.metadata.lastUpdated).toBe('string')
      expect(json.metadata.lastUpdated).toMatch(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}\.\d{3}Z$/)
    })
  })

  describe('document types', () => {
    it('should support privacy-policy type', () => {
      const document = new LegalDocument('test-id', 'privacy-policy', 'Privacy Policy', '<p>Content</p>', mockMetadata)

      expect(document.type).toBe('privacy-policy')
    })

    it('should support terms-of-service type', () => {
      const document = new LegalDocument('test-id', 'terms-of-service', 'Terms of Service', '<p>Content</p>', mockMetadata)

      expect(document.type).toBe('terms-of-service')
    })

    it('should support cookie-policy type', () => {
      const document = new LegalDocument('test-id', 'cookie-policy', 'Cookie Policy', '<p>Content</p>', mockMetadata)

      expect(document.type).toBe('cookie-policy')
    })
  })
})

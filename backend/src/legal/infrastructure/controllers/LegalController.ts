import { InternalServerError, NotFoundError, getErrorStatusCode, isCustomError } from '../../../server/errors/index.js'
import { GetLegalDocument } from '../../application/GetLegalDocument.js'
import type { LegalDocumentType } from '../../domain/LegalDocument.js'

export class LegalController {
  constructor(private readonly getLegalDocumentService: GetLegalDocument) {}

  async getPrivacyPolicy() {
    try {
      const document = await this.getLegalDocumentService.execute('privacy-policy')
      return { status: 200, data: document.toHTML() }
    } catch (error) {
      // Handle custom errors with proper status codes
      if (isCustomError(error)) {
        throw error
      }

      // Handle unexpected errors
      throw new InternalServerError((error as Error).message)
    }
  }

  async getLegalDocument(documentType: LegalDocumentType) {
    try {
      const document = await this.getLegalDocumentService.execute(documentType)
      return { status: 200, data: document.toHTML() }
    } catch (error) {
      // Handle specific business logic errors
      if (error instanceof NotFoundError) {
        return { status: 404, data: this.getNotFoundHTML(documentType) }
      }

      // Handle custom errors with proper status codes
      if (isCustomError(error)) {
        throw error
      }

      // Handle unexpected errors
      throw new InternalServerError((error as Error).message)
    }
  }

  private getNotFoundHTML(documentType: string): string {
    return `<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Documento no encontrado - BuscaFarma</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f9f9f9;
            text-align: center;
        }
        .container {
            background: white;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e74c3c;
            margin-bottom: 20px;
        }
        p {
            color: #7f8c8d;
            margin-bottom: 15px;
        }
        .error-code {
            font-size: 4em;
            color: #ecf0f1;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-code">404</div>
        <h1>Documento no encontrado</h1>
        <p>El documento legal "${documentType}" no está disponible en este momento.</p>
        <p>Por favor, contacta con nuestro equipo de soporte si necesitas acceder a este documento.</p>
    </div>
</body>
</html>`
  }
}

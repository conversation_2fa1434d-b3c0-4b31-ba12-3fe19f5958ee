#!/usr/bin/env bun

/**
 * Pre-commit Hook Setup Script
 *
 * This script sets up the comprehensive pre-commit hook system for the Buscafarma backend.
 * It installs <PERSON>sky, configures Git hooks, and validates the development environment.
 */

import { execSync } from 'child_process'
import { chmodSync, existsSync, mkdirSync, writeFileSync } from 'fs'
import { join } from 'path'

const PROJECT_ROOT = process.cwd()
const HUSKY_DIR = join(PROJECT_ROOT, '.husky')

interface SetupResult {
  success: boolean
  message: string
  details?: string[]
}

/**
 * Executes a command and returns the result
 */
function executeCommand(command: string, description: string): SetupResult {
  try {
    console.log(`🔄 ${description}...`)
    execSync(command, { stdio: 'inherit', cwd: PROJECT_ROOT })
    console.log(`✅ ${description} completed successfully`)
    return { success: true, message: `${description} completed` }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error(`❌ ${description} failed: ${errorMessage}`)
    return { success: false, message: `${description} failed`, details: [errorMessage] }
  }
}

/**
 * Checks if required dependencies are installed
 */
function checkDependencies(): SetupResult {
  console.log('🔍 Checking dependencies...')

  const requiredDeps = ['husky', 'lint-staged']
  const missingDeps: string[] = []

  for (const dep of requiredDeps) {
    try {
      require.resolve(dep)
      console.log(`  ✅ ${dep} is installed`)
    } catch {
      missingDeps.push(dep)
      console.log(`  ❌ ${dep} is missing`)
    }
  }

  if (missingDeps.length > 0) {
    return {
      success: false,
      message: 'Missing required dependencies',
      details: [`Please install: ${missingDeps.join(', ')}`]
    }
  }

  return { success: true, message: 'All dependencies are installed' }
}

/**
 * Initializes Husky
 */
function initializeHusky(): SetupResult {
  return executeCommand('bun run husky init', 'Initializing Husky')
}

/**
 * Creates the pre-commit hook
 */
function createPreCommitHook(): SetupResult {
  console.log('🔄 Creating pre-commit hook...')

  try {
    const hookPath = join(HUSKY_DIR, 'pre-commit')
    const hookContent = `#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

# Buscafarma Backend Pre-commit Hook
echo "🚀 Running Buscafarma pre-commit checks..."

# Run lint-staged for staged files
bun run lint-staged

# Check if bypass flag is set
if [ "$HUSKY_SKIP_HOOKS" = "1" ]; then
  echo "⚠️  WARNING: Pre-commit hooks were bypassed!"
  echo "   This should only be used in emergency situations."
  echo "   Please ensure code quality manually."
  exit 0
fi

echo "✅ All pre-commit checks passed!"
`

    writeFileSync(hookPath, hookContent)
    chmodSync(hookPath, 0o755)

    console.log('✅ Pre-commit hook created successfully')
    return { success: true, message: 'Pre-commit hook created' }
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error'
    console.error(`❌ Failed to create pre-commit hook: ${errorMessage}`)
    return { success: false, message: 'Failed to create pre-commit hook', details: [errorMessage] }
  }
}

/**
 * Validates the setup
 */
function validateSetup(): SetupResult {
  console.log('🔍 Validating setup...')

  const checks = [
    { path: join(HUSKY_DIR, 'pre-commit'), name: 'Pre-commit hook' },
    { path: join(PROJECT_ROOT, '.lintstagedrc.json'), name: 'Lint-staged config' },
    { path: join(PROJECT_ROOT, 'scripts', 'pre-commit'), name: 'Pre-commit scripts directory' }
  ]

  const missing: string[] = []

  for (const check of checks) {
    if (existsSync(check.path)) {
      console.log(`  ✅ ${check.name} exists`)
    } else {
      console.log(`  ❌ ${check.name} is missing`)
      missing.push(check.name)
    }
  }

  if (missing.length > 0) {
    return {
      success: false,
      message: 'Setup validation failed',
      details: [`Missing: ${missing.join(', ')}`]
    }
  }

  return { success: true, message: 'Setup validation passed' }
}

/**
 * Main setup function
 */
async function main() {
  console.log('🎯 Setting up Buscafarma Backend Pre-commit Hooks\n')

  const steps = [
    { name: 'Check Dependencies', fn: checkDependencies },
    { name: 'Initialize Husky', fn: initializeHusky },
    { name: 'Create Pre-commit Hook', fn: createPreCommitHook },
    { name: 'Validate Setup', fn: validateSetup }
  ]

  let allSuccessful = true

  for (const step of steps) {
    console.log(`\n📋 Step: ${step.name}`)
    const result = step.fn()

    if (!result.success) {
      allSuccessful = false
      console.error(`\n❌ Setup failed at step: ${step.name}`)
      console.error(`   ${result.message}`)
      if (result.details) {
        result.details.forEach((detail) => console.error(`   - ${detail}`))
      }
      break
    }
  }

  if (allSuccessful) {
    console.log('\n🎉 Pre-commit hook setup completed successfully!')
    console.log('\n📖 Usage:')
    console.log('   • Hooks will run automatically on git commit')
    console.log('   • To bypass in emergencies: HUSKY_SKIP_HOOKS=1 git commit -m "message"')
    console.log('   • To test manually: bun run pre-commit:code-quality')
    console.log('\n🔧 Available commands:')
    console.log('   • bun run pre-commit:code-quality  - Run code quality checks')
    console.log('   • bun run pre-commit:security      - Run security checks')
    console.log('   • bun run pre-commit:api-standards - Check API standards')
    console.log('   • bun run pre-commit:tests         - Run tests with coverage')
  } else {
    console.log('\n💥 Setup failed. Please fix the issues above and try again.')
    process.exit(1)
  }
}

// Run the setup
main().catch((error) => {
  console.error('💥 Unexpected error during setup:', error)
  process.exit(1)
})

#!/usr/bin/env bun

/**
 * TypeScript Type Check Wrapper for Pre-commit
 *
 * This script wraps the TypeScript type checking to ensure it runs
 * on the entire project using tsconfig.json settings, regardless
 * of which individual files are passed by lint-staged.
 *
 * The script ignores file arguments and always runs a full project
 * type check to maintain consistency and proper configuration usage.
 */

import { execSync } from 'node:child_process'

interface TypeCheckResult {
  success: boolean
  message: string
  details?: string[]
}

/**
 * Execute TypeScript compilation check
 */
function executeTypeCheck(): TypeCheckResult {
  console.log('🔍 Running TypeScript compilation check...')

  try {
    // Execute tsc --noEmit using project configuration
    // This ignores any file arguments passed by lint-staged
    execSync('bun run tsc --noEmit', {
      stdio: 'pipe',
      encoding: 'utf8',
      cwd: process.cwd()
    })

    console.log('✅ TypeScript compilation passed')
    return {
      success: true,
      message: 'TypeScript compilation passed'
    }
  } catch (error: unknown) {
    return {
      success: false,
      message: 'TypeScript compilation failed',
      details: [
        'Fix TypeScript errors before committing',
        error && typeof error === 'object' && 'stdout' in error
          ? (error.stdout as string)
          : error && typeof error === 'object' && 'stderr' in error
            ? (error.stderr as string)
            : error instanceof Error
              ? error.message
              : String(error)
      ]
    }
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('🎯 TypeScript Type Check (Full Project)\n')

  const result = executeTypeCheck()

  if (result.success) {
    console.log('\n🎉 TypeScript type check completed successfully!')
    process.exit(0)
  } else {
    console.error(`\n❌ ${result.message}`)
    if (result.details) {
      result.details.forEach((detail) => console.error(`   ${detail}`))
    }
    process.exit(1)
  }
}

// Run the type check
main().catch((error) => {
  console.error('💥 Unexpected error during TypeScript type check:', error)
  process.exit(1)
})

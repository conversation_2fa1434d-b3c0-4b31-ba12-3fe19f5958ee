#!/usr/bin/env bun

/**
 * Security Validation Script
 *
 * This script performs comprehensive security checks including:
 * - Sensitive data detection (API keys, passwords, tokens)
 * - JWT token validation patterns
 * - Environment variable security checks
 * - Common security anti-patterns
 */

import { execSync } from 'child_process'
import { existsSync, readFileSync } from 'fs'

interface SecurityResult {
  success: boolean
  message: string
  details?: string[]
  severity?: 'low' | 'medium' | 'high' | 'critical'
}

interface SecurityCheck {
  name: string
  description: string
  execute: () => SecurityResult
}

/**
 * Gets list of staged files
 */
function getStagedFiles(): string[] {
  try {
    const result = execSync('git diff --cached --name-only --diff-filter=ACM', { encoding: 'utf8' })
    return result
      .split('\n')
      .filter((file) => file.trim())
      .filter((file) => existsSync(file))
  } catch {
    return []
  }
}

/**
 * Sensitive data patterns to detect
 */
const SENSITIVE_PATTERNS = [
  {
    pattern: /(?:password|pwd|pass)\s*[:=]\s*['"][^'"]{3,}['"]/gi,
    description: 'Hardcoded password',
    severity: 'critical' as const
  },
  {
    pattern: /(?:api[_-]?key|apikey)\s*[:=]\s*['"][^'"]{10,}['"]/gi,
    description: 'Hardcoded API key',
    severity: 'critical' as const
  },
  {
    pattern: /(?:secret|token)\s*[:=]\s*['"][^'"]{10,}['"]/gi,
    description: 'Hardcoded secret or token',
    severity: 'high' as const
  },
  {
    pattern: /(?:private[_-]?key|privatekey)\s*[:=]\s*['"][^'"]{20,}['"]/gi,
    description: 'Hardcoded private key',
    severity: 'critical' as const
  },
  {
    pattern: /(?:database[_-]?url|db[_-]?url)\s*[:=]\s*['"][^'"]+['"]/gi,
    description: 'Hardcoded database URL',
    severity: 'high' as const
  },
  {
    pattern: /mongodb:\/\/[^'"]+/gi,
    description: 'MongoDB connection string',
    severity: 'high' as const
  },
  {
    pattern: /postgres:\/\/[^'"]+/gi,
    description: 'PostgreSQL connection string',
    severity: 'high' as const
  },
  {
    pattern: /Bearer\s+[A-Za-z0-9\-_]{20,}/g,
    description: 'Hardcoded Bearer token',
    severity: 'high' as const
  },
  {
    pattern: /jwt\s*[:=]\s*['"]eyJ[^'"]+['"]/gi,
    description: 'Hardcoded JWT token',
    severity: 'high' as const
  }
]

/**
 * Allowed patterns that should not trigger alerts
 */
const ALLOWED_PATTERNS = [
  /process\.env\./,
  /your-secret-key/,
  /your-refresh-secret-key/,
  /test-token/,
  /mock/i,
  /example/i,
  /placeholder/i,
  /TODO/i,
  /FIXME/i,
  // Test file specific patterns
  /valid-token/,
  /firebase-token-123/,
  /access-token/,
  /valid-refresh-token/,
  /new-access-token/,
  /invalid\.token\.here/,
  /not\.a\.valid\.jwt\.token/,
  /invalid\.refresh\.token/,
  // Generic test patterns
  /test.*token/i,
  /token.*test/i,
  /fake.*token/i,
  /dummy.*token/i
]

/**
 * Checks for sensitive data in files
 */
function sensitiveDataCheck(): SecurityResult {
  console.log('🔍 Scanning for sensitive data...')

  const stagedFiles = getStagedFiles().filter(
    (file) => /\.(ts|js|json|env|yml|yaml)$/.test(file) && !file.includes('node_modules') && !file.includes('.git')
  )

  if (stagedFiles.length === 0) {
    return { success: true, message: 'No files to scan for sensitive data' }
  }

  const issues: Array<{ file: string; line: number; issue: string; severity: string }> = []

  for (const file of stagedFiles) {
    try {
      const content = readFileSync(file, 'utf8')
      const lines = content.split('\n')

      lines.forEach((line, index) => {
        for (const pattern of SENSITIVE_PATTERNS) {
          const matches = line.match(pattern.pattern)
          if (matches) {
            // Check if this is an allowed pattern
            const isAllowed = ALLOWED_PATTERNS.some((allowedPattern) => allowedPattern.test(line))

            // Additional check for test files - be more lenient with obvious test tokens
            const isTestFile = file.includes('test') || file.includes('spec')
            const isObviousTestToken =
              isTestFile &&
              (line.includes('test') ||
                line.includes('mock') ||
                line.includes('fake') ||
                line.includes('dummy') ||
                line.includes('invalid') ||
                line.includes('example') ||
                matches[0].length < 20) // Short tokens are likely test tokens

            if (!isAllowed && !isObviousTestToken) {
              issues.push({
                file,
                line: index + 1,
                issue: `${pattern.description}: ${matches[0]}`,
                severity: pattern.severity
              })
            }
          }
        }
      })
    } catch (error) {
      issues.push({
        file,
        line: 0,
        issue: `Error reading file: ${error}`,
        severity: 'medium'
      })
    }
  }

  if (issues.length > 0) {
    const criticalIssues = issues.filter((i) => i.severity === 'critical')
    const _highIssues = issues.filter((i) => i.severity === 'high')

    return {
      success: false,
      message: 'Sensitive data detected',
      severity: criticalIssues.length > 0 ? 'critical' : 'high',
      details: [
        `Found ${issues.length} potential security issues:`,
        ...issues.map((issue) => `  ${issue.file}:${issue.line} [${issue.severity.toUpperCase()}] ${issue.issue}`),
        '',
        'Please remove sensitive data and use environment variables instead.'
      ]
    }
  }

  console.log('✅ No sensitive data detected')
  return { success: true, message: 'No sensitive data detected' }
}

/**
 * Validates JWT implementation patterns
 */
function jwtSecurityCheck(): SecurityResult {
  console.log('🔍 Checking JWT security patterns...')

  const stagedFiles = getStagedFiles().filter(
    (file) => /\.(ts|js)$/.test(file) && !file.includes('scripts/') && !file.includes('test') && !file.includes('spec')
  )
  const issues: string[] = []

  for (const file of stagedFiles) {
    try {
      const content = readFileSync(file, 'utf8')

      // Check for weak JWT secrets
      if (content.includes('jwt.sign') || content.includes('jwt.verify')) {
        if (content.match(/jwt\.sign\([^,]+,\s*['"][^'"]{1,10}['"]/)) {
          issues.push(`${file}: JWT secret appears to be too short (< 10 characters)`)
        }

        if (content.match(/jwt\.sign\([^,]+,\s*['"]secret['"]/i)) {
          issues.push(`${file}: Using default 'secret' for JWT signing`)
        }

        // Check for missing expiration
        if (content.includes('jwt.sign') && !content.includes('expiresIn')) {
          issues.push(`${file}: JWT tokens should have expiration time`)
        }
      }

      // Check for proper error handling in JWT verification
      if (content.includes('jwt.verify') && !content.includes('try') && !content.includes('catch')) {
        issues.push(`${file}: JWT verification should be wrapped in try-catch`)
      }
    } catch (error) {
      issues.push(`${file}: Error reading file - ${error}`)
    }
  }

  if (issues.length > 0) {
    return {
      success: false,
      message: 'JWT security issues detected',
      severity: 'medium',
      details: issues
    }
  }

  console.log('✅ JWT security patterns look good')
  return { success: true, message: 'JWT security patterns validated' }
}

/**
 * Checks for common security anti-patterns
 */
function securityAntiPatternsCheck(): SecurityResult {
  console.log('🔍 Checking for security anti-patterns...')

  const stagedFiles = getStagedFiles().filter(
    (file) => /\.(ts|js)$/.test(file) && !file.includes('scripts/') && !file.includes('test') && !file.includes('spec')
  )
  const issues: string[] = []

  for (const file of stagedFiles) {
    try {
      const content = readFileSync(file, 'utf8')

      // Check for eval usage
      if (content.match(/\beval\s*\(/)) {
        issues.push(`${file}: Avoid using eval() - security risk`)
      }

      // Check for Function constructor
      if (content.match(/new\s+Function\s*\(/)) {
        issues.push(`${file}: Avoid using Function constructor - security risk`)
      }

      // Check for SQL injection patterns
      if (content.match(/\$\{[^}]*\}.*(?:SELECT|INSERT|UPDATE|DELETE)/i)) {
        issues.push(`${file}: Potential SQL injection - use parameterized queries`)
      }

      // Check for unsafe regex patterns
      if (content.match(/new\s+RegExp\s*\(\s*[^,)]*\+/)) {
        issues.push(`${file}: Potential ReDoS vulnerability - avoid dynamic regex`)
      }

      // Check for console.log in production code (not in test files or scripts)
      if (!file.includes('test') && !file.includes('spec') && !file.includes('scripts/') && content.includes('console.log')) {
        issues.push(`${file}: Remove console.log statements from production code`)
      }
    } catch (error) {
      issues.push(`${file}: Error reading file - ${error}`)
    }
  }

  if (issues.length > 0) {
    return {
      success: false,
      message: 'Security anti-patterns detected',
      severity: 'medium',
      details: issues
    }
  }

  console.log('✅ No security anti-patterns detected')
  return { success: true, message: 'No security anti-patterns detected' }
}

/**
 * Main security validation function
 */
async function main() {
  console.log('🛡️  Running Security Checks\n')

  const checks: SecurityCheck[] = [
    {
      name: 'Sensitive Data Detection',
      description: 'Scanning for hardcoded secrets and credentials',
      execute: sensitiveDataCheck
    },
    {
      name: 'JWT Security Patterns',
      description: 'Validating JWT implementation security',
      execute: jwtSecurityCheck
    },
    {
      name: 'Security Anti-patterns',
      description: 'Checking for common security vulnerabilities',
      execute: securityAntiPatternsCheck
    }
  ]

  let allPassed = true
  let hasCriticalIssues = false
  const results: SecurityResult[] = []

  for (const check of checks) {
    console.log(`\n📋 ${check.name}: ${check.description}`)
    const result = check.execute()
    results.push(result)

    if (!result.success) {
      allPassed = false
      if (result.severity === 'critical') {
        hasCriticalIssues = true
      }

      const severityIcon = result.severity === 'critical' ? '🚨' : result.severity === 'high' ? '⚠️' : '⚡'

      console.error(`${severityIcon} ${check.name} failed: ${result.message}`)
      if (result.details) {
        result.details.forEach((detail) => console.error(`   ${detail}`))
      }
    }
  }

  console.log('\n🛡️  Security Summary:')
  results.forEach((result, index) => {
    const status = result.success ? '✅' : result.severity === 'critical' ? '🚨' : result.severity === 'high' ? '⚠️' : '⚡'
    console.log(`   ${status} ${checks[index].name}: ${result.message}`)
  })

  if (allPassed) {
    console.log('\n🎉 All security checks passed!')
    process.exit(0)
  } else {
    if (hasCriticalIssues) {
      console.log('\n🚨 CRITICAL security issues found! Commit blocked.')
    } else {
      console.log('\n⚠️  Security issues found. Please review and fix.')
    }
    process.exit(1)
  }
}

// Run the security validation
main().catch((error) => {
  console.error('💥 Unexpected error during security check:', error)
  process.exit(1)
})

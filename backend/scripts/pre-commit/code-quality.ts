#!/usr/bin/env bun

/**
 * Code Quality Validation Script
 *
 * This script performs comprehensive code quality checks including:
 * - Biome linting with existing project rules
 * - TypeScript compilation checks
 * - Import/export validation
 * - Code complexity analysis
 */

import { execSync } from 'child_process'
import { existsSync, readFileSync } from 'fs'
import { join } from 'path'

interface ValidationResult {
  success: boolean
  message: string
  details?: string[]
}

interface CodeQualityCheck {
  name: string
  description: string
  execute: () => ValidationResult
}

/**
 * Executes a command and captures output
 */
function executeCommand(command: string, options: { silent?: boolean } = {}): { success: boolean; output: string } {
  try {
    const output = execSync(command, {
      encoding: 'utf8',
      stdio: options.silent ? 'pipe' : 'inherit',
      cwd: process.cwd()
    })
    return { success: true, output: output.toString() }
  } catch (error) {
    const output =
      error instanceof Error && 'stdout' in error ? (error as Error & { stdout?: string }).stdout?.toString() || error.message : 'Unknown error'
    return { success: false, output }
  }
}

/**
 * Gets list of staged TypeScript/JavaScript files
 */
function getStagedFiles(): string[] {
  try {
    const result = execSync('git diff --cached --name-only --diff-filter=ACM', { encoding: 'utf8' })
    return result
      .split('\n')
      .filter((file) => file.trim())
      .filter((file) => /\.(ts|js)$/.test(file))
      .filter((file) => existsSync(file))
  } catch {
    return []
  }
}

/**
 * Biome linting check
 */
function biomeLintCheck(): ValidationResult {
  console.log('🔍 Running Biome linting...')

  const stagedFiles = getStagedFiles()
  if (stagedFiles.length === 0) {
    return { success: true, message: 'No TypeScript/JavaScript files to lint' }
  }

  // Run Biome check on staged files
  const result = executeCommand(`bun run biome check ${stagedFiles.join(' ')}`, { silent: true })

  if (!result.success) {
    return {
      success: false,
      message: 'Biome linting failed',
      details: ['Run "bun run lint:fix" to automatically fix issues', 'Or fix manually and stage the changes', result.output]
    }
  }

  console.log('✅ Biome linting passed')
  return { success: true, message: 'Biome linting passed' }
}

/**
 * TypeScript compilation check
 */
function typeScriptCheck(): ValidationResult {
  console.log('🔍 Running TypeScript compilation check...')

  const result = executeCommand('bun run tsc --noEmit', { silent: true })

  if (!result.success) {
    return {
      success: false,
      message: 'TypeScript compilation failed',
      details: ['Fix TypeScript errors before committing', result.output]
    }
  }

  console.log('✅ TypeScript compilation passed')
  return { success: true, message: 'TypeScript compilation passed' }
}

/**
 * Import/export validation
 */
function importExportCheck(): ValidationResult {
  console.log('🔍 Checking import/export patterns...')

  const stagedFiles = getStagedFiles()
  if (stagedFiles.length === 0) {
    return { success: true, message: 'No files to check for import/export patterns' }
  }

  const issues: string[] = []

  for (const file of stagedFiles) {
    try {
      const content = readFileSync(file, 'utf8')

      // Check for relative imports going up more than 2 levels
      // Allow deep imports for server/errors directory (centralized error handling)
      const deepRelativeImports = content.match(/from\s+['"]\.\.\/\.\.\/\.\.\//g)
      if (deepRelativeImports) {
        const hasServerErrorsImport = content.includes('server/errors')
        if (!hasServerErrorsImport) {
          issues.push(`${file}: Avoid deep relative imports (../../../)`)
        }
      }

      // Check for missing file extensions in relative imports
      const relativeImportsWithoutExt = content.match(/from\s+['"]\.\.?\/[^'"]*(?<!\.js)['"]/)
      if (relativeImportsWithoutExt) {
        issues.push(`${file}: Use .js extensions in relative imports`)
      }

      // Check for unused imports (basic check)
      const importMatches = content.match(/import\s+\{([^}]+)\}\s+from/g)
      if (importMatches) {
        for (const importMatch of importMatches) {
          const imports = importMatch.match(/\{([^}]+)\}/)?.[1]
          if (imports) {
            const importNames = imports.split(',').map((name) => name.trim())
            for (const importName of importNames) {
              const cleanName = importName.replace(/\s+as\s+\w+/, '').trim()
              if (!content.includes(cleanName) || content.indexOf(cleanName) === content.indexOf(importMatch)) {
                issues.push(`${file}: Potentially unused import: ${cleanName}`)
              }
            }
          }
        }
      }
    } catch (error) {
      issues.push(`${file}: Error reading file - ${error}`)
    }
  }

  if (issues.length > 0) {
    return {
      success: false,
      message: 'Import/export validation failed',
      details: issues
    }
  }

  console.log('✅ Import/export validation passed')
  return { success: true, message: 'Import/export validation passed' }
}

/**
 * Code complexity check using madge
 */
function complexityCheck(): ValidationResult {
  console.log('🔍 Running code complexity analysis...')

  try {
    // Check for circular dependencies
    const result = executeCommand('bun run madge --circular src/', { silent: true })

    if (!result.success && result.output.includes('Found')) {
      return {
        success: false,
        message: 'Circular dependencies detected',
        details: ['Fix circular dependencies before committing', result.output]
      }
    }

    console.log('✅ Code complexity check passed')
    return { success: true, message: 'Code complexity check passed' }
  } catch (_error) {
    // If madge fails, don't block the commit but warn
    console.log('⚠️  Code complexity check skipped (madge not available)')
    return { success: true, message: 'Code complexity check skipped' }
  }
}

/**
 * Main validation function
 */
async function main() {
  console.log('🎯 Running Code Quality Checks\n')

  const checks: CodeQualityCheck[] = [
    {
      name: 'Biome Linting',
      description: 'Checking code style and formatting',
      execute: biomeLintCheck
    },
    {
      name: 'TypeScript Compilation',
      description: 'Validating TypeScript types',
      execute: typeScriptCheck
    },
    {
      name: 'Import/Export Validation',
      description: 'Checking import patterns and dependencies',
      execute: importExportCheck
    },
    {
      name: 'Code Complexity',
      description: 'Analyzing code complexity and circular dependencies',
      execute: complexityCheck
    }
  ]

  let allPassed = true
  const results: ValidationResult[] = []

  for (const check of checks) {
    console.log(`\n📋 ${check.name}: ${check.description}`)
    const result = check.execute()
    results.push(result)

    if (!result.success) {
      allPassed = false
      console.error(`❌ ${check.name} failed: ${result.message}`)
      if (result.details) {
        result.details.forEach((detail) => console.error(`   ${detail}`))
      }
    }
  }

  console.log('\n📊 Code Quality Summary:')
  results.forEach((result, index) => {
    const status = result.success ? '✅' : '❌'
    console.log(`   ${status} ${checks[index].name}: ${result.message}`)
  })

  if (allPassed) {
    console.log('\n🎉 All code quality checks passed!')
    process.exit(0)
  } else {
    console.log('\n💥 Code quality checks failed. Please fix the issues above.')
    process.exit(1)
  }
}

// Run the validation
main().catch((error) => {
  console.error('💥 Unexpected error during code quality check:', error)
  process.exit(1)
})

// Test Prisma schema file for SQLite integration testing
// This mirrors the production MongoDB schema but uses SQLite for testing

generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/test-client"
}

datasource db {
  provider = "sqlite"
  url      = env("TEST_DATABASE_URL")
}

model user {
  id                    String                @id @default(cuid())
  name                  String
  email                 String                @unique
  firebase_token        String
  current_location      String?               // JSON string for Point { latitude: number, longitude: number }
  preferences           String                // JSON string for UserPreferences
  health_data           String                // JSON string for HealthData
  profile_picture_url   String?
  refreshToken          String?               // JWT refresh token
  medication_favorites  medication_favorite[]
  created_at            DateTime              @default(now())
  updated_at            DateTime              @updatedAt
}

model medication_favorite {
  id              String   @id @default(cuid())
  user_id         String
  user            user     @relation(fields: [user_id], references: [id], onDelete: Cascade)

  // Medication identification fields for deduplication
  cod_estab       String   // Establishment code
  cod_prod_e      Int      // Product code

  // Complete medication data from MINSA API (stored as JSON string)
  medication_data String   // JSON string of full medication object

  // Metadata
  created_at      DateTime @default(now())
  updated_at      DateTime @updatedAt

  // Compound indexes for efficient querying
  @@index([user_id, created_at])
  @@unique([user_id, cod_estab, cod_prod_e])
}

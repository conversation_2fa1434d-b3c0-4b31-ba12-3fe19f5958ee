# Pre-commit Hooks System

This document describes the comprehensive pre-commit hook system implemented for the Buscafarma backend to ensure code quality, security, and adherence to project standards.

## Overview

The pre-commit system automatically validates code quality before each commit, ensuring that:
- Code follows established patterns and standards
- Security vulnerabilities are detected early
- Tests pass and coverage is maintained
- API standards are consistently applied
- JWT authentication patterns are correct

## Quick Start

### Initial Setup

```bash
# Install dependencies (if not already done)
bun install

# Set up pre-commit hooks
bun run pre-commit:setup
```

### Manual Testing

You can run individual checks manually:

```bash
# Run all checks
bun run pre-commit:code-quality
bun run pre-commit:security
bun run pre-commit:api-standards
bun run pre-commit:tests

# Run specific linting
bun run lint
bun run type-check
```

## System Components

### 1. Code Quality Checks (`pre-commit:code-quality`)

**What it checks:**
- **Biome Linting**: Code style and formatting using existing project rules
- **TypeScript Compilation**: Type safety and compilation errors
- **Import/Export Validation**: Proper import patterns and dependency management
- **Code Complexity**: Circular dependencies and complexity analysis

**Common Issues:**
- Formatting violations → Run `bun run lint:fix`
- TypeScript errors → Fix type issues
- Deep relative imports → Use absolute imports or barrel exports
- Missing file extensions → Add `.js` extensions to relative imports

### 2. Security Checks (`pre-commit:security`)

**What it checks:**
- **Sensitive Data Detection**: Hardcoded passwords, API keys, tokens
- **JWT Security Patterns**: Proper JWT implementation and security
- **Security Anti-patterns**: Common vulnerabilities (eval, SQL injection, etc.)

**Severity Levels:**
- 🚨 **Critical**: Hardcoded credentials, private keys
- ⚠️ **High**: API keys, database URLs, Bearer tokens
- ⚡ **Medium**: Weak JWT patterns, console.log statements

**Common Issues:**
- Hardcoded secrets → Use environment variables
- Weak JWT secrets → Use strong, environment-based secrets
- Missing JWT expiration → Add `expiresIn` to JWT tokens

### 3. API Standards Validation (`pre-commit:api-standards`)

**What it checks:**
- **Error Handling Patterns**: Use of centralized error classes
- **JWT Authentication**: Consistent token handling patterns
- **API Response Structure**: Standardized response formats

**Expected Patterns:**
```typescript
// ✅ Good: Use custom error classes
throw new AuthenticationError('Invalid token')

// ❌ Bad: Generic errors
throw new Error('Invalid token')

// ✅ Good: Consistent response structure
return { status: 200, data: result }

// ❌ Bad: Inconsistent structure
return result
```

### 4. Test Requirements (`pre-commit:tests`)

**What it checks:**
- **Test Naming Conventions**: Proper describe/it blocks with descriptive names
- **New Code Coverage**: Tests for new functionality
- **Unit Tests**: All unit tests must pass
- **Integration Tests**: All integration tests must pass
- **Code Coverage**: Minimum 70% coverage threshold

**Test Naming Standards:**
```typescript
// ✅ Good
describe('UserController', () => {
  it('should authenticate user with valid credentials', () => {
    // test implementation
  })
})

// ❌ Bad
describe('test', () => {
  it('works', () => {
    // test implementation
  })
})
```

## Configuration

### Coverage Thresholds

Edit `scripts/pre-commit/test-runner.ts` to adjust coverage requirements:

```typescript
const minimumCoverage = 70 // Adjust as needed
```

### Security Patterns

Add new security patterns in `scripts/pre-commit/security-check.ts`:

```typescript
const SENSITIVE_PATTERNS = [
  {
    pattern: /your-pattern-here/gi,
    description: 'Description of what this detects',
    severity: 'high' as const
  }
]
```

### Lint-staged Configuration

Modify `.lintstagedrc.json` to change which checks run on which files:

```json
{
  "*.{ts,js}": [
    "bun run pre-commit:code-quality",
    "bun run pre-commit:security"
  ]
}
```

## Emergency Bypass

In critical situations, you can bypass pre-commit hooks:

```bash
# Bypass all hooks (use with caution!)
HUSKY_SKIP_HOOKS=1 git commit -m "Emergency fix"

# Or use git's built-in bypass
git commit --no-verify -m "Emergency fix"
```

**⚠️ Warning**: Bypassed commits should be reviewed and fixed as soon as possible.

## Troubleshooting

### Common Issues

1. **"Command not found" errors**
   ```bash
   # Reinstall dependencies
   bun install
   # Re-run setup
   bun run pre-commit:setup
   ```

2. **Tests taking too long**
   - Integration tests have a 2-minute timeout
   - Unit tests have a 1-minute timeout
   - Consider optimizing slow tests

3. **False positive security alerts**
   - Add patterns to `ALLOWED_PATTERNS` in security-check.ts
   - Use environment variables for legitimate secrets

4. **Coverage issues**
   - Add tests for new functionality
   - Adjust coverage threshold if needed
   - Exclude test files and types from coverage

### Performance Optimization

The system uses `lint-staged` to only check staged files, improving performance:

- Only modified files are linted
- Tests run on the full suite (as they should)
- Security checks focus on changed files
- API standards validation targets relevant files

## Integration with CI/CD

This pre-commit system complements CI/CD pipelines:

- **Pre-commit**: Fast feedback during development
- **CI/CD**: Comprehensive validation on all code
- **Both**: Ensure consistent quality standards

## Maintenance

### Regular Updates

1. **Update dependencies**: Keep Husky and lint-staged current
2. **Review patterns**: Update security and API patterns as needed
3. **Adjust thresholds**: Modify coverage and complexity thresholds
4. **Add new checks**: Extend validation as the project grows

### Monitoring

Track pre-commit effectiveness:
- Monitor bypass frequency
- Review common failure patterns
- Gather developer feedback
- Adjust rules based on team needs

## Support

For issues or questions about the pre-commit system:

1. Check this documentation
2. Review existing patterns in the codebase
3. Test individual components manually
4. Consult with the development team

## Files Structure

```
backend/
├── .husky/
│   └── pre-commit              # Main Git hook
├── .lintstagedrc.json         # Staged files configuration
├── scripts/
│   ├── setup-hooks.ts         # Setup and installation
│   └── pre-commit/
│       ├── code-quality.ts    # Code quality validation
│       ├── security-check.ts  # Security scanning
│       ├── api-standards.ts   # API standards validation
│       └── test-runner.ts     # Test execution and coverage
└── docs/
    └── PRE_COMMIT_HOOKS.md    # This documentation
```

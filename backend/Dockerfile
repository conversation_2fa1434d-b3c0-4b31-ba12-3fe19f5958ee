# Use the official Bun image as a base
FROM oven/bun:latest AS base

# Set the working directory
WORKDIR /app

# Set NODE_ENV to production
ENV NODE_ENV production

# Install OpenSSL (as root)
# Prisma needs this, and it's good practice to clean up apt cache
USER root
RUN apt-get update -y &&     apt-get install -y openssl &&     rm -rf /var/lib/apt/lists/*

# Copy package.json, bun.lock, and tsconfig.json
COPY package.json bun.lock tsconfig.json ./

# Copy source code and prisma directory
COPY src/ ./src/
COPY prisma/ ./prisma/

# Create a non-root user
# We do this before bun install so that files created (like node_modules) are owned by bun
RUN (getent group bun || groupadd --gid 1001 bun) && (getent passwd bun || useradd --uid 1001 --gid 1001 --shell /bin/bash --create-home bun)

# Change ownership of the app directory to the bun user
RUN chown -R bun:bun /app

# Switch to the non-root user before installing dependencies and building
USER bun

# Install dependencies (prisma generate will run due to postinstall)
# Ensure all files are copied before this, especially prisma/schema.prisma
RUN bun install --frozen-lockfile

# Build the application
RUN bun run build

# Expose the port the app runs on
EXPOSE 3000

# Define the command to run the app
CMD ["bun", "run", "./dist/index.js"]

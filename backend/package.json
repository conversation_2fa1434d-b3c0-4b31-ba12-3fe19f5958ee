{"name": "backend", "version": "1.0.50", "scripts": {"test": "bun test", "test:unit": "bun test src/services/test src/user/test src/favorites/test src/server/test", "test:integration": "NODE_ENV=test bun test src/__tests__/integration", "test:all": "bun run test:unit && bun run test:integration", "test:coverage": "bun test --coverage", "test:db:generate": "prisma generate --schema=prisma/schema.test.prisma", "test:db:push": "prisma db push --schema=prisma/schema.test.prisma", "dev": "bun run --watch src/index.ts", "build": "bun build ./src/index.ts --outdir ./dist --target node --external bcrypt --external @prisma/client", "start": "NODE_ENV=production bun run ./dist/index.js", "fix": "biome check --write", "lint": "biome check", "lint:fix": "biome check --write", "type-check": "tsc --noEmit", "pre-commit:setup": "bun run scripts/setup-hooks.ts", "pre-commit:code-quality": "bun run scripts/pre-commit/code-quality.ts", "pre-commit:security": "bun run scripts/pre-commit/security-check.ts", "pre-commit:api-standards": "bun run scripts/pre-commit/api-standards.ts", "pre-commit:type-check": "bun run scripts/pre-commit/type-check-wrapper.ts", "pre-commit:tests": "bun run scripts/pre-commit/test-runner.ts", "postinstall": "prisma generate && bun run test:db:generate", "prepare": "husky"}, "dependencies": {"@elysiajs/swagger": "^1.3.0", "@prisma/client": "^6.8.2", "@types/jsonwebtoken": "^9.0.9", "elysia": "^1.3.1", "jsonwebtoken": "^9.0.2"}, "devDependencies": {"@biomejs/biome": "^1.9.4", "@types/node": "^22.15.21", "bun-types": "^1.2.14", "husky": "^9.1.7", "lint-staged": "^16.0.0", "madge": "^8.0.0", "prisma": "^6.8.2", "typescript": "^5.8.3"}, "module": "src/index.js"}
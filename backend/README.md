# Backend API Service

This project is a backend API service for user authentication (via Firebase) and user profile management. It is built using ElysiaJS with the Bun runtime.

## Development

### Prerequisites

Before you begin, ensure you have the following installed and configured:

- **Bun JS runtime:** This project uses Bun as its JavaScript runtime. You can find the official installation guide at [https://bun.sh/docs/installation](https://bun.sh/docs/installation).
- **Node.js:** While Bun provides its own runtime, having Node.js installed is often beneficial for general JavaScript development and for managing some tools or packages that might not be fully compatible with Bun yet.
- **Firebase Account and Project Setup:** The user authentication endpoint (`/users/firebase/login`) relies on Firebase. You will need to:
    - Create a Firebase project at [https://console.firebase.google.com/](https://console.firebase.google.com/).
    - Set up Firebase Authentication.
    - The application will likely require environment variables to be configured with your Firebase project's details (e.g., API Key, Project ID, etc.). Please refer to the application's environment variable setup guide or `.env.example` file (if available) for the specific variable names.

### Running the application

To start the development server run:
```bash
bun run dev
```

Open http://localhost:3000/ with your browser to see the result.

## API Endpoints

All endpoints are prefixed with `/api/v1`.

### **POST /users/firebase/login**
- **Description:** Handles user login or registration via Firebase. If the user doesn't exist, a new user is created.
- **Request Body:**
    - `firebaseId` (string, required): The user's unique Firebase ID.
    - `email` (string, required): The user's email address.
    - `name` (string, optional): The user's display name.
    - `firebaseToken` (string, required): The Firebase ID token for authentication.
- **Response:** Returns access and refresh tokens upon successful login or registration.
- **Authentication:** None required for this endpoint.

### **POST /users/refresh-token**
- **Description:** Refreshes an expired access token using a valid refresh token.
- **Request Body:**
    - `refreshToken` (string, required): The refresh token issued during login.
- **Response:** Returns a new access token.
- **Authentication:** None required for this endpoint.

### **PUT /users/preferences**
- **Description:** Updates the authenticated user's preferences.
- **Request Body:**
    - `language` (string, optional): The user's preferred language (e.g., "en", "es").
    - `theme` (string, optional): The user's preferred theme (e.g., "light", "dark").
    - `notifications` (boolean, optional): Whether the user wants to receive notifications.
    - `defaultLocation` (object, optional): The user's default location.
        *   `departmentCode` (string)
        *   `provinceCode` (string)
        *   `districtCode` (string)
- **Response:** Returns a success message and the updated user preferences.
- **Authentication:** Requires JWT via Authorization Bearer token.

### **PUT /users/health-data**
- **Description:** Updates the authenticated user's health data.
- **Request Body:**
    - `allergies` (array of strings, optional): A list of the user's allergies.
    - `conditions` (array of strings, optional): A list of the user's medical conditions.
    - `medications` (array of objects, optional): A list of the user's current medications. Each object can have:
        *   `name` (string)
        *   `dosage` (string)
        *   `frequency` (string)
- **Response:** Returns a success message and the updated user health data.
- **Authentication:** Requires JWT via Authorization Bearer token.

### **PUT /users/location**
- **Description:** Updates the authenticated user's current geographical location.
- **Request Body:**
    - `latitude` (number, required): The user's current latitude.
    - `longitude` (number, required): The user's current longitude.
- **Response:** Returns a success message.
- **Authentication:** Requires JWT via Authorization Bearer token.

### **PUT /users/profile-picture**
- **Description:** Updates the authenticated user's profile picture URL.
- **Request Body:**
    - `url` (string, required): The new URL for the user's profile picture.
- **Response:** Returns a success message and the updated profile picture URL.
- **Authentication:** Requires JWT via Authorization Bearer token.

### **GET /users/profile**
- **Description:** Retrieves the authenticated user's profile information.
- **Request Body:** None.
- **Response:** Returns the user's profile data, including email, name, preferences, health data, etc.
- **Authentication:** Requires JWT via Authorization Bearer token.

## Using the API

### Interacting with the API

The `endpoints/ownProject/users.http` file (not included in this repository but typically present in the full project structure) provides a convenient way to make requests to the API. This file is compatible with tools like the VS Code REST Client extension, allowing you to easily test the endpoints.

Before sending requests using such a file, you will need to replace placeholder variables (e.g., `{{firebaseId}}`, `{{email}}`, `{{firebaseToken}}`, `{{refreshToken}}`, `{{accessToken}}`) with your actual data.

### Authentication Flow Example

Here's a step-by-step example of how to authenticate and interact with the API:

1.  **Login or Register:**
    Make a `POST` request to the `/api/v1/users/firebase/login` endpoint.
    Include `firebaseId`, `email`, `name` (optional), and `firebaseToken` in the request body.

    Example Request Body:
    ```json
    {
        "firebaseId": "your-firebase-id",
        "email": "<EMAIL>",
        "name": "Test User",
        "firebaseToken": "your-firebase-id-token"
    }
    ```

2.  **Receive Tokens:**
    The response from the login endpoint will contain an `accessToken` and a `refreshToken`.

3.  **Access Protected Endpoints:**
    To access protected endpoints (those requiring JWT authentication), include the `accessToken` in the `Authorization` header as a Bearer token.

    Example Header:
    ```
    GET /api/v1/users/profile
    Authorization: Bearer YOUR_ACCESS_TOKEN
    ```

4.  **Refresh Access Token:**
    If your `accessToken` expires, you can obtain a new one by making a `POST` request to `/api/v1/users/refresh-token` with your `refreshToken` in the request body.

---
*This README was last updated on YYYY-MM-DD.*

### List Departments
# Response:
# {"codigo":"00","mensaje":"La operación se realizó correctamente","data":[{"codigo":"01","descripcion":"AMAZONAS"}],"entidad":null,"cantidad":26,"codigoEntidad":null,"codigosValidos":null,"paginas":null,"totalData":null,"resultadoProceso":null,"mensajesErrores":null}
POST https://ms-opm.minsa.gob.pe/msopmcovid/parametro/departamentos
Content-Type: application/json
Origin: https://opm-digemid.minsa.gob.pe
Referer: https://opm-digemid.minsa.gob.pe/

{
	"filtro": {
		"codigo":null,
		"codigoDos":null
	}
}

### List Provinces
# Response:
# {"codigo":"00","mensaje":"La operación se realizó correctamente","data":[{"codigo":"01","descripcion":"LIMA","codigoDepartamento":"15"}],"entidad":null,"cantidad":10,"codigoEntidad":null,"codigosValidos":null,"paginas":null,"totalData":null,"resultadoProceso":null,"mensajesErrores":null}
POST https://ms-opm.minsa.gob.pe/msopmcovid/parametro/provincias
Content-Type: application/json
Origin: https://opm-digemid.minsa.gob.pe
Referer: https://opm-digemid.minsa.gob.pe/

{
	"filtro": {
		"codigo":"01",
		"codigoDos":null
	}
}

### List Districts
# Response:
# {"codigo":"00","mensaje":"La operación se realizó correctamente","data":[{"codigo":"150101","descripcion":"LIMA","codigoProvincia":"01","codigoDepartamento":"15"}],"entidad":null,"cantidad":43,"codigoEntidad":null,"codigosValidos":null,"paginas":null,"totalData":null,"resultadoProceso":null,"mensajesErrores":null}
POST  https://ms-opm.minsa.gob.pe/msopmcovid/parametro/distritos
Content-Type: application/json
Origin: https://opm-digemid.minsa.gob.pe
Referer: https://opm-digemid.minsa.gob.pe/

{
	"filtro": {
		"codigo":"01",
		"codigoDos":"01"
	}
}

### List Product auto complete
# Response:
# {"codigo":"00","mensaje":"La operación se realizó correctamente","data":[{"codigoProducto":null,"nombreProducto":"PANADOL","concent":"500mg","presentacion":null,"fracciones":null,"nombreFormaFarmaceutica":"Tableta - Capsula","nroRegistroSanitario":null,"titular":null,"grupo":2926,"codGrupoFF":"3"}],"entidad":null,"cantidad":null,"codigoEntidad":null,"codigosValidos":null,"paginas":null,"totalData":null,"resultadoProceso":null,"mensajesErrores":null}
POST https://ms-opm.minsa.gob.pe/msopmcovid/producto/autocompleteciudadano
Content-Type: application/json
Origin: https://opm-digemid.minsa.gob.pe
Referer: https://opm-digemid.minsa.gob.pe/

{
	"filtro": {
		"nombreProducto":"panadol",
		"pagina":1,
		"tamanio":10,
		"tokenGoogle":""
	}
}

### Find Product
# Request:
# codigoProducto: from List Product auto complete
# codigoDepartamento: from List Districts
# codigoProvincia: from List Districts
# codigoUbigeo: from List Districts
# codTipoEstablecimiento 1: Fares 2: Farmacias 3: Boticas
# catEstablecimiento 01: Públicos 02: Privados
# nombreEstablecimiento: null or string
# nombreLaboratorio: null or string
# codGrupoFF: from List Product auto complete
# concent: from List Product auto complete
# tamanio: skip
# pagina: page
# nombreProducto: from List Product auto complete
#
# Response:
#  {"codigo":"00","mensaje":"La operación se realizó correctamente","data":[{"codEstab":"0082831","codProdE":8850,"fecha":"24/01/2025 04:37:39 PM","nombreProducto":"PARACETAMOL","precio1":5.30,"precio2":0.05,"precio3":null,"codGrupoFF":"3","ubicodigo":"150110","direccion":"AV. TUPAC AMARU N° 3006 MZ L, LOTE 25 URB. REPARTICION  ","telefono":"988827315","nomGrupoFF":"Tableta - Capsula","setcodigo":"Privado","nombreComercial":"BOTICA INKAFARMA","grupo":"2926","totalPA":"1","concent":"500 mg","nombreFormaFarmaceutica":"Tableta","fracciones":100,"totalRegistros":null,"nombreLaboratorio":"INSTITUTO QUIMIOTERAPICO S.A.","nombreTitular":"INSTITUTO QUIMIOTERAPICO S.A. - IQFARMA","catCodigo":"04","nombreSustancia":"PARACETAMOL","fabricante":null,"departamento":"LIMA","provincia":"LIMA","distrito":"COMAS"}],"entidad":{"codEstab":"0088054","codProdE":51396,"fecha":null,"nombreProducto":null,"precio1":0.02,"precio2":null,"precio3":null,"codGrupoFF":null,"ubicodigo":null,"direccion":null,"telefono":null,"nomGrupoFF":null,"setcodigo":"Público","nombreComercial":null,"grupo":null,"totalPA":null,"concent":"500 mg","nombreFormaFarmaceutica":"Tableta","fracciones":100,"totalRegistros":null,"nombreLaboratorio":null,"nombreTitular":null,"catCodigo":null,"nombreSustancia":"PARACETAMOL","fabricante":null,"departamento":null,"provincia":null,"distrito":null},"cantidad":17,"codigoEntidad":null,"codigosValidos":null,"paginas":null,"totalData":null,"resultadoProceso":null,"mensajesErrores":null}
POST  https://ms-opm.minsa.gob.pe/msopmcovid/preciovista/ciudadano HTTP/1.1
Content-Type: application/json
Origin: https://opm-digemid.minsa.gob.pe
Referer: https://opm-digemid.minsa.gob.pe/

{
	"filtro": {
		"codigoProducto":2926,
		"codigoDepartamento":"15",
		"codigoProvincia":"01",
		"codigoUbigeo":"150110",
		"codTipoEstablecimiento":null,
		"catEstablecimiento":null,
		"nombreEstablecimiento": "BOTICA INKAFARMA",
		"nombreLaboratorio": "INSTITUTO QUIMIOTERAPICO S.A.",
		"codGrupoFF":"3",
		"concent":"500mg",
		"tamanio":1,
		"pagina":2,
		"nombreProducto":null
	}
}

### Find detail of product and establishment
# Request:
# codigoProducto: from Find Product = codProdE
# codEstablecimiento: from Find Product = codEstab
# Response:
# {"codigo":"00","mensaje":"La operación se realizó correctamente","data":null,"entidad":{"precio1":1.00,"precio2":0.01,"nombreProducto":"PARACETAMOL","paisFabricacion":"Perú","registroSanitario":"NG2989","condicionVenta":"Sin receta medica","tipoProducto":"Genérico DCI","nombreTitular":"LABORATORIOS UNIDOS S.A. - LUSA","nombreFabricante":"UNIDOS","presentacion":"Caja Envase Blister Tabletas","laboratorio":"UNIDOS","directorTecnico":"PINEDO TAPIA SONIA","nombreComercial":"BOTICA PERFUMERÍA MI SALUD RC","telefono":"972927451","direccion":"CALLE JUNIN CUADRA 2, CASERIO SANTA CRUZ S/N","departamento":"CAJAMARCA","provincia":"JAEN","distrito":"BELLAVISTA","horarioAtencion":"SAB: 07:00 A 13:00","ubigeo":"060802","catCodigo":"04","email":"<EMAIL>","ruc":"10479202112"},"cantidad":null,"codigoEntidad":null,"codigosValidos":null,"paginas":null,"totalData":null,"resultadoProceso":null,"mensajesErrores":null}
POST  https://ms-opm.minsa.gob.pe/msopmcovid/precioproducto/obtener
Content-Type: application/json
Origin: https://opm-digemid.minsa.gob.pe
Referer: https://opm-digemid.minsa.gob.pe/

{
    "filtro": {
        "codigoProducto": 8830,
        "codEstablecimiento": "0118356"
    }
}
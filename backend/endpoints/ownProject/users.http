@baseUrl = http://localhost:3000/api/v1
@contentType = application/json

# @name login
POST {{baseUrl}}/users/firebase/login
Content-Type: {{contentType}}

{
    "firebaseId": "{{firebaseId}}",
    "email": "{{email}}",
    "name": "{{name}}",
    "firebaseToken": "{{firebaseToken}}"
}

###

@authToken = {{login.response.body.data.accessToken}}
@refreshToken = {{login.response.body.data.refreshToken}}

# @name refreshAccessToken
POST {{baseUrl}}/users/refresh-token
Content-Type: {{contentType}}

{
    "refreshToken": "{{refreshToken}}"
}

###

# @name updatePreferences
PUT {{baseUrl}}/users/preferences
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
    "language": "es",
    "theme": "dark",
    "notifications": true,
    "defaultLocation": {
        "departmentCode": "15",
        "provinceCode": "01",
        "districtCode": "01"
    }
}

###

# @name updateHealthData
PUT {{baseUrl}}/users/health-data
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
    "allergies": ["penicillin", "pollen"],
    "conditions": ["asthma"],
    "medications": [
        {
            "name": "Ventolin",
            "dosage": "100mcg",
            "frequency": "as needed"
        }
    ]
}

###

# @name updateLocation
PUT {{baseUrl}}/users/location
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
    "latitude": -12.0464,
    "longitude": -77.0428
}

###

# @name updateProfilePicture
PUT {{baseUrl}}/users/profile-picture
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
    "url": "https://example.com/profile-picture.jpg"
}

###

# @name getProfile
GET {{baseUrl}}/users/profile
Authorization: Bearer {{authToken}} 
@baseUrl = http://localhost:3000/api/v1
@contentType = application/json

# First, login to get authentication token
# @name login
POST {{baseUrl}}/users/firebase/login
Content-Type: {{contentType}}

{
    "firebaseId": "{{firebaseId}}",
    "email": "{{email}}",
    "name": "{{name}}",
    "firebaseToken": "{{firebaseToken}}"
}

###

@authToken = {{login.response.body.data.accessToken}}

# @name addMedicationFavorite
POST {{baseUrl}}/favorites/medications
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
    "codEstab": "0094084",
    "codProdE": 8850,
    "fecha": "21/04/2025 05:18:50 PM",
    "nombreProducto": "PARACETAMOL",
    "precio1": 5.30,
    "precio2": 0.05,
    "precio3": null,
    "codGrupoFF": "3",
    "ubicodigo": "150110",
    "direccion": "AV. TUPAC AMARU CARABAYLLO 361",
    "telefono": "01-3142020",
    "nomGrupoFF": "Tableta - Capsula",
    "setcodigo": "Privado",
    "nombreComercial": "BOTICA INKAFARMA",
    "grupo": "2926",
    "totalPA": "1",
    "concent": "500 mg",
    "nombreFormaFarmaceutica": "Tableta",
    "fracciones": 100,
    "totalRegistros": null,
    "nombreLaboratorio": "INSTITUTO QUIMIOTERAPICO S.A.",
    "nombreTitular": "INSTITUTO QUIMIOTERAPICO S.A. - IQFARMA",
    "catCodigo": "04",
    "nombreSustancia": "PARACETAMOL",
    "fabricante": null,
    "departamento": "LIMA",
    "provincia": "LIMA",
    "distrito": "COMAS"
}

###

# @name addSecondMedicationFavorite
POST {{baseUrl}}/favorites/medications
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
    "codEstab": "0118356",
    "codProdE": 8830,
    "fecha": "21/04/2025 05:20:30 PM",
    "nombreProducto": "IBUPROFENO",
    "precio1": 8.50,
    "precio2": 0.08,
    "precio3": null,
    "codGrupoFF": "3",
    "ubicodigo": "150101",
    "direccion": "AV. JAVIER PRADO ESTE 4200",
    "telefono": "01-4567890",
    "nomGrupoFF": "Tableta - Capsula",
    "setcodigo": "Privado",
    "nombreComercial": "FARMACIA UNIVERSAL",
    "grupo": "2927",
    "totalPA": "1",
    "concent": "400 mg",
    "nombreFormaFarmaceutica": "Tableta",
    "fracciones": 50,
    "totalRegistros": null,
    "nombreLaboratorio": "LABORATORIO FARMACEUTICO S.A.",
    "nombreTitular": "LABORATORIO FARMACEUTICO S.A.",
    "catCodigo": "04",
    "nombreSustancia": "IBUPROFENO",
    "fabricante": null,
    "departamento": "LIMA",
    "provincia": "LIMA",
    "distrito": "LIMA"
}

###

# @name listMedicationFavorites
GET {{baseUrl}}/favorites/medications?page=1&limit=10
Authorization: Bearer {{authToken}}

###

# @name searchMedicationFavorites
GET {{baseUrl}}/favorites/medications/search?q=paracetamol&page=1&limit=10
Authorization: Bearer {{authToken}}

###

# @name removeMedicationFavoriteByIdentifiers
DELETE {{baseUrl}}/favorites/medications
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
    "codEstab": "0094084",
    "codProdE": 8850
}

###

# Get the favorite ID from the list response for this test
@favoriteId = {{listMedicationFavorites.response.body.data.favorites.0.id}}

# @name removeMedicationFavoriteById
DELETE {{baseUrl}}/favorites/medications/{{favoriteId}}
Authorization: Bearer {{authToken}}

###

# Test unauthorized access
# @name testUnauthorized
GET {{baseUrl}}/favorites/medications

###

# Test adding duplicate favorite (should return 409)
# @name testDuplicateFavorite
POST {{baseUrl}}/favorites/medications
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
    "codEstab": "0118356",
    "codProdE": 8830,
    "fecha": "21/04/2025 05:20:30 PM",
    "nombreProducto": "IBUPROFENO",
    "precio1": 8.50,
    "precio2": 0.08,
    "precio3": null,
    "codGrupoFF": "3",
    "ubicodigo": "150101",
    "direccion": "AV. JAVIER PRADO ESTE 4200",
    "telefono": "01-4567890",
    "nomGrupoFF": "Tableta - Capsula",
    "setcodigo": "Privado",
    "nombreComercial": "FARMACIA UNIVERSAL",
    "grupo": "2927",
    "totalPA": "1",
    "concent": "400 mg",
    "nombreFormaFarmaceutica": "Tableta",
    "fracciones": 50,
    "totalRegistros": null,
    "nombreLaboratorio": "LABORATORIO FARMACEUTICO S.A.",
    "nombreTitular": "LABORATORIO FARMACEUTICO S.A.",
    "catCodigo": "04",
    "nombreSustancia": "IBUPROFENO",
    "fabricante": null,
    "departamento": "LIMA",
    "provincia": "LIMA",
    "distrito": "LIMA"
}

###

# Test removing non-existent favorite (should return 404)
# @name testRemoveNonExistent
DELETE {{baseUrl}}/favorites/medications
Content-Type: {{contentType}}
Authorization: Bearer {{authToken}}

{
    "codEstab": "9999999",
    "codProdE": 99999
}

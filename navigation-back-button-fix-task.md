# Context
Filename: navigation-back-button-fix-task.md
Created On: 2024-12-19
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
Fix the navigation behavior in the BuscaFarma Flutter app's favorites page. Currently, when users navigate to the favorites page and press the back button (or use back navigation), the app exits completely instead of returning to the previous screen.

The expected behavior should be:
- When on the favorites page and pressing back, navigate to the maps page (not exit the app)
- Ensure proper navigation stack management so users can navigate between favorites and maps seamlessly
- Maintain consistency with standard Android/iOS navigation patterns where back navigation should return to the previous screen in the app's navigation stack

# Project Overview
BuscaFarma is a Flutter application that helps users find pharmacies and medications. The app uses:
- Flutter with Riverpod for state management
- Fluro router for navigation
- Bottom navigation bar with two main tabs: Maps (index 0) and Favorites (index 1)
- MainNavigationPage as the container that manages tab switching using IndexedStack

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## Current Navigation Architecture
The app uses a hybrid navigation approach:
1. **Fluro Router**: Handles route-based navigation between major app sections (splash, login, main navigation)
2. **Bottom Navigation with IndexedStack**: Manages tab switching within MainNavigationPage

## Key Files Identified
- `lib/features/navigation/presentation/pages/main_navigation_page.dart`: Container with bottom navigation
- `lib/features/favorites/presentation/pages/favorites_page.dart`: Favorites page implementation
- `lib/core/router/app_router.dart`: Fluro router configuration
- `lib/core/router/route_handlers.dart`: Route handlers that return MainNavigationPage for both root and map routes

## Current Navigation Flow
1. App starts with splash screen
2. After authentication, navigates to MainNavigationPage (which shows MapPage by default)
3. MainNavigationPage uses IndexedStack to switch between MapPage (index 0) and FavoritesPage (index 1)
4. Bottom navigation bar controls which tab is active via `navigationIndexProvider`

## Problem Analysis
The issue occurs because:
1. **No PopScope/WillPopScope handling**: MainNavigationPage doesn't handle back button presses
2. **IndexedStack behavior**: When on favorites tab (index 1), pressing back exits the app instead of switching to maps tab
3. **Missing navigation logic**: No custom back button handling to switch tabs instead of popping the route

## Current Back Button Behavior
- When on favorites page, system back button tries to pop the current route
- Since MainNavigationPage is the only route in the stack (after navigation from splash/login), popping it exits the app
- Expected behavior: Back button should switch from favorites tab to maps tab

## Constraints Identified
- Must maintain existing Fluro router structure
- Must preserve current bottom navigation functionality
- Must not break existing navigation patterns for other parts of the app
- Should follow Flutter best practices for tab navigation

# Proposed Solution (Populated by INNOVATE mode)

## Solution Approaches Evaluated

### Approach 1: PopScope with Custom Logic (Recommended)
Implement PopScope widget around MainNavigationPage that intercepts back button presses. When on favorites tab, switch to maps tab instead of popping route.

**Pros:**
- Modern Flutter approach (3.16+)
- Handles all back navigation methods (system button, gestures, AppBar)
- Maintains existing architecture
- Consistent cross-platform behavior

**Cons:**
- Requires Flutter 3.16+ for PopScope widget

### Approach 2: Custom AppBar Back Button
Override back button behavior specifically in FavoritesPage AppBar.

**Pros:**
- Simple targeted fix
- No system-wide changes

**Cons:**
- Only handles AppBar back button
- Doesn't handle Android system back button or gesture navigation

### Approach 3: Navigation Stack Restructuring
Create separate routes for maps and favorites pages.

**Pros:**
- Traditional navigation pattern
- Cleaner separation

**Cons:**
- Major architectural change
- Could break existing functionality
- Requires extensive testing

## Recommended Solution: PopScope Implementation

The PopScope approach provides the most comprehensive solution while maintaining the existing architecture. Implementation involves:

1. Wrap MainNavigationPage Scaffold with PopScope widget
2. Implement custom canPop logic that checks current tab index
3. When on favorites tab (index 1): switch to maps tab (index 0) and prevent route pop
4. When on maps tab (index 0): allow normal route pop behavior
5. Maintain all existing functionality and state management

# Implementation Plan (Generated by PLAN mode)

## File Modifications Required

### File: `lib/features/navigation/presentation/pages/main_navigation_page.dart`
**Rationale:** MainNavigationPage manages tab switching with IndexedStack. Implementing PopScope here intercepts back button presses and enables custom navigation logic.

**Technical Details:**
- Wrap existing Scaffold with PopScope widget
- Implement canPop callback checking current tab index
- Add onPopInvokedWithResult callback for custom back navigation
- Preserve all existing Riverpod state management and functionality

**PopScope Logic:**
- currentIndex == 1 (favorites): Switch to index 0 (maps), prevent route pop
- currentIndex == 0 (maps): Allow normal route pop behavior
- Maintain existing bottom navigation tap functionality

**Error Handling:**
- Ensure PopScope doesn't interfere with existing navigation
- Handle edge cases with navigation state inconsistencies
- Preserve existing state management patterns

Implementation Checklist:
1. ✅ Examine current MainNavigationPage implementation and identify PopScope insertion point
2. ✅ Import PopScope widget (verify Flutter 3.16+ compatibility)
3. ✅ Wrap existing Scaffold widget with PopScope widget
4. ✅ Implement canPop callback that checks navigationIndexProvider state
5. ✅ Implement onPopInvokedWithResult callback for custom back navigation logic
6. Test back button behavior on both tabs (maps and favorites)
7. Verify bottom navigation tap functionality remains intact
8. Test system back button, gesture navigation, and AppBar back button behavior

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "Implementation of PopScope widget with custom back navigation logic"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   2024-12-19
    *   Step: Implementation of PopScope widget with custom back navigation logic (Steps 1-5)
    *   Modifications:
        - Modified `lib/features/navigation/presentation/pages/main_navigation_page.dart`
        - Wrapped existing Scaffold with PopScope widget
        - Implemented canPop logic: `canPop: currentIndex == 0` (only allow popping when on maps tab)
        - Implemented onPopInvokedWithResult callback with custom logic to switch from favorites to maps tab
        - Added logic: when on favorites tab (index 1) and pop is prevented, switch to maps tab (index 0)
        - Preserved all existing Riverpod state management and bottom navigation functionality
    *   Change Summary: Successfully implemented PopScope widget to handle back button navigation between tabs
    *   Reason: Executing plan steps 1-5 for PopScope implementation
    *   Blockers: None
    *   Status: Pending Confirmation


# Context
Filename: tab-transition-animations-task.md
Created On: 2024-12-19
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
Add smooth transition animations to the tab switching behavior in the BuscaFarma Flutter app's MainNavigationPage to improve user experience. Specifically:

1. **Tab Switch Animation**: When the back button triggers a switch from the favorites tab to the maps tab (the PopScope logic we just implemented), add a smooth animated transition instead of an instant switch.

2. **Bottom Navigation Animation**: Enhance the existing bottom navigation bar tab switching with smooth animations when users tap between Maps and Favorites tabs.

3. **Animation Requirements**:
   - Use Material Design 3 motion principles as defined in the app's animation standards (@buscafarma/.cursor/rules/buscafarma.mdc)
   - Implement smooth, performant transitions (duration: 200-300ms recommended)
   - Consider using AnimatedSwitcher, PageTransition, or custom animation controllers
   - Ensure animations work consistently for both user-initiated tab taps and programmatic tab switches (like the back button behavior)

4. **Maintain Existing Functionality**: Preserve all current navigation logic, state management, and the PopScope back button behavior we just implemented.

5. **Performance Considerations**: Ensure animations don't interfere with the IndexedStack performance or cause unnecessary rebuilds of the MapPage and FavoritesPage widgets.

# Project Overview
BuscaFarma is a Flutter application with comprehensive animation standards and existing animation infrastructure:
- Uses Material Design 3 motion principles
- Has established animation constants and patterns in `lib/core/animations/`
- Currently uses IndexedStack for tab switching without animations
- Recently implemented PopScope for back button navigation between tabs

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## Current Animation Infrastructure
The app has a well-established animation system:

### Animation Constants (`lib/core/animations/animation_constants.dart`)
- **Duration Standards**:
  - `stateTransitionDuration`: 300ms (perfect for tab transitions)
  - `microInteractionDuration`: 150ms (for quick interactions)
  - `hoverDuration`: 200ms
- **Curves**: Material Design 3 compliant curves
  - `entranceCurve`: Curves.easeOutCubic
  - `exitCurve`: Curves.easeInCubic
  - `microInteractionCurve`: Curves.easeInOut
  - `emphasizedCurve`: Curves.easeInOutCubicEmphasized

### Existing Animation Patterns
- **Staggered Animation Controller**: Sophisticated animation orchestration
- **AnimatedLoadingIndicator**: State transition animations
- **Google Sign-In Button**: Micro-interaction animations
- **Splash/Login Pages**: Complex entrance animations
- **AnimatedSwitcher**: Used in various components for smooth transitions

## Current Navigation Implementation
- **MainNavigationPage**: Uses IndexedStack for instant tab switching
- **PopScope Integration**: Recently added for back button handling
- **No Transition Animations**: Current tab switching is instant without visual feedback

## Problem Analysis
1. **Instant Tab Switching**: IndexedStack shows/hides widgets instantly without transitions
2. **Back Button Behavior**: PopScope logic switches tabs instantly, creating jarring UX
3. **Missing Visual Feedback**: Users don't get smooth transition feedback when switching tabs
4. **Inconsistent with App Standards**: Other parts of app use smooth animations, but tab switching doesn't

## Technical Constraints
- **IndexedStack Performance**: Must maintain widget state preservation
- **PopScope Integration**: Must work with existing back button logic
- **State Management**: Must preserve Riverpod state management patterns
- **Memory Efficiency**: Avoid creating unnecessary widget rebuilds

# Proposed Solution (Populated by INNOVATE mode)

## Solution Approaches Evaluated

### Approach 1: AnimatedSwitcher with IndexedStack (Recommended)
Replace IndexedStack with AnimatedSwitcher that wraps current page widget, maintaining performance while adding smooth transitions.

**Pros:**
- Maintains widget state preservation like IndexedStack
- Uses existing app animation constants and curves
- Minimal performance impact
- Easy integration with PopScope logic
- Consistent with existing AnimatedSwitcher usage in the app

**Cons:**
- Requires careful key management for proper transitions
- Need to handle widget disposal properly

### Approach 2: PageView with Animation Controller
Replace IndexedStack with PageView and custom animation controller for more control.

**Pros:**
- Full control over transition animations
- Can implement custom transition effects
- Natural swipe gesture support

**Cons:**
- More complex implementation
- Potential performance overhead
- May interfere with existing state management
- Could break existing navigation patterns

### Approach 3: Custom Animation Controller with IndexedStack
Keep IndexedStack but add custom animation overlay that animates opacity/scale during transitions.

**Pros:**
- Maintains all existing IndexedStack benefits
- Can create sophisticated transition effects
- Minimal architectural changes

**Cons:**
- More complex animation coordination
- Potential for animation conflicts
- Higher implementation complexity

## Recommended Solution: AnimatedSwitcher Implementation

The AnimatedSwitcher approach aligns best with the app's existing animation patterns and provides optimal balance of performance, maintainability, and user experience.

**Implementation Strategy:**
1. Replace IndexedStack with AnimatedSwitcher using app's standard `stateTransitionDuration` (300ms)
2. Implement proper widget keys for transition detection
3. Create custom slide transition builder matching Material Design 3 principles
4. Integrate smoothly with existing PopScope logic
5. Maintain widget state and avoid unnecessary rebuilds

**Animation Specifications:**
- Duration: `AnimationConstants.stateTransitionDuration` (300ms)
- Curve: `AnimationConstants.emphasizedCurve` (Material Design 3 compliant)
- Transition: Horizontal slide (left/right based on tab direction)
- Fallback: Fade transition for edge cases

# Implementation Plan (Generated by PLAN mode)

## File Modifications Required

### File: `lib/features/navigation/presentation/pages/main_navigation_page.dart`
**Rationale:** MainNavigationPage currently uses IndexedStack for instant tab switching. Replacing with AnimatedSwitcher adds smooth transitions while maintaining widget state preservation and PopScope integration.

**Technical Details:**
- Import animation constants from existing infrastructure
- Replace IndexedStack with AnimatedSwitcher using custom transition builder
- Implement proper widget keys for transition detection
- Create horizontal slide transitions based on tab direction
- Maintain all existing PopScope and state management functionality

**Animation Logic:**
- Maps to Favorites (0→1): Slide left (new page from right)
- Favorites to Maps (1→0): Slide right (new page from left)
- Duration: 300ms (`AnimationConstants.stateTransitionDuration`)
- Curve: `AnimationConstants.emphasizedCurve` (Material Design 3)

**Integration Requirements:**
- Preserve existing Riverpod state management patterns
- Maintain PopScope functionality for back button handling
- Keep existing bottom navigation tap logic intact
- Ensure favorites loading logic remains functional

Implementation Checklist:
1. ✅ Import required animation constants and utilities
2. ✅ Create helper method to get current page widget with proper keys
3. ✅ Create custom slide transition builder function
4. ✅ Replace IndexedStack with AnimatedSwitcher in build method
5. ✅ Implement proper widget keys based on current index
6. ✅ Configure AnimatedSwitcher with duration, curve, and transition builder
7. Test tab switching animations (both tap and back button triggered)
8. Verify PopScope integration works smoothly with animations
9. Test performance and state preservation
10. Update task progress documentation

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "AnimatedSwitcher implementation with slide transitions"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   2024-12-19
    *   Step: AnimatedSwitcher implementation with slide transitions (Steps 1-6)
    *   Modifications:
        - Modified `lib/features/navigation/presentation/pages/main_navigation_page.dart`
        - Added import for `AnimationConstants` from existing animation infrastructure
        - Created `_getCurrentPageWidget()` helper method with proper ValueKey for each page
        - Created `_slideTransitionBuilder()` custom transition builder with horizontal slide animations
        - Replaced IndexedStack with AnimatedSwitcher using 300ms duration and emphasizedCurve
        - Implemented slide direction logic: Maps slides from left, Favorites slides from right
        - Maintained all existing PopScope logic and state management functionality
        - Preserved existing bottom navigation tap logic and favorites loading behavior
    *   Change Summary: Successfully implemented smooth tab transition animations using AnimatedSwitcher
    *   Reason: Executing plan steps 1-6 for AnimatedSwitcher implementation
    *   Blockers: None
    *   Status: Pending Confirmation

import 'package:flutter_test/flutter_test.dart';
import 'package:buscafarma/core/services/display_mode_service.dart';
import 'package:buscafarma/core/providers/display_mode_provider.dart';

void main() {
  group('DisplayModeService Tests', () {
    test('should create singleton instance', () {
      final service1 = DisplayModeService.getInstance();
      final service2 = DisplayModeService.getInstance();
      
      expect(service1, equals(service2));
    });

    test('should handle platform detection gracefully', () {
      final service = DisplayModeService.getInstance();
      
      // Should not throw exception regardless of platform
      expect(() => service.isPlatformSupported, returnsNormally);
      expect(() => service.isFeatureEnabled, returnsNormally);
    });

    test('should provide debug information', () {
      final service = DisplayModeService.getInstance();
      final debugInfo = service.getDebugInfo();
      
      expect(debugInfo, isA<Map<String, dynamic>>());
      expect(debugInfo.containsKey('isPlatformSupported'), isTrue);
      expect(debugInfo.containsKey('isFeatureEnabled'), isTrue);
      expect(debugInfo.containsKey('platform'), isTrue);
    });
  });

  group('DisplayModeState Tests', () {
    test('should create default state', () {
      const state = DisplayModeState();
      
      expect(state.isInitialized, isFalse);
      expect(state.isPlatformSupported, isFalse);
      expect(state.isFeatureEnabled, isTrue);
      expect(state.supportedModes, isEmpty);
      expect(state.preference, equals(DisplayModePreference.auto));
      expect(state.isLoading, isFalse);
      expect(state.errorMessage, isNull);
    });

    test('should create state with custom values', () {
      const state = DisplayModeState(
        isInitialized: true,
        isPlatformSupported: true,
        preference: DisplayModePreference.high,
        isLoading: true,
      );
      
      expect(state.isInitialized, isTrue);
      expect(state.isPlatformSupported, isTrue);
      expect(state.preference, equals(DisplayModePreference.high));
      expect(state.isLoading, isTrue);
    });

    test('should copy state with new values', () {
      const originalState = DisplayModeState();
      final newState = originalState.copyWith(
        isInitialized: true,
        preference: DisplayModePreference.low,
      );
      
      expect(newState.isInitialized, isTrue);
      expect(newState.preference, equals(DisplayModePreference.low));
      // Other values should remain the same
      expect(newState.isPlatformSupported, equals(originalState.isPlatformSupported));
      expect(newState.isFeatureEnabled, equals(originalState.isFeatureEnabled));
    });

    test('should handle equality correctly', () {
      const state1 = DisplayModeState(isInitialized: true);
      const state2 = DisplayModeState(isInitialized: true);
      const state3 = DisplayModeState(isInitialized: false);
      
      expect(state1, equals(state2));
      expect(state1, isNot(equals(state3)));
    });
  });

  group('DisplayModePreference Tests', () {
    test('should have correct enum values', () {
      expect(DisplayModePreference.values.length, equals(3));
      expect(DisplayModePreference.values, contains(DisplayModePreference.auto));
      expect(DisplayModePreference.values, contains(DisplayModePreference.high));
      expect(DisplayModePreference.values, contains(DisplayModePreference.low));
    });
  });
}

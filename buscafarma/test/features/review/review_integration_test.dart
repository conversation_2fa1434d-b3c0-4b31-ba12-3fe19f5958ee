import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:buscafarma/features/review/domain/entities/review_prompt_entity.dart';
import 'package:buscafarma/features/review/data/providers/review_local_provider.dart';
import 'package:buscafarma/features/review/data/repositories/review_repository_impl.dart';
import 'package:buscafarma/features/review/presentation/services/review_service.dart';
import 'package:buscafarma/features/review/presentation/providers/review_manager_provider.dart';

void main() {
  group('Review Integration Tests', () {
    late ReviewLocalProvider localProvider;
    late ReviewRepositoryImpl repository;
    late ReviewService reviewService;

    setUpAll(() {
      // Initialize SharedPreferences with mock values for testing
      SharedPreferences.setMockInitialValues({});
    });

    setUp(() {
      localProvider = ReviewLocalProvider();
      repository = ReviewRepositoryImpl(localProvider);
      reviewService = ReviewService(repository);
    });

    tearDown(() async {
      // Clean up after each test
      await localProvider.clearReviewData();
      // Reset SharedPreferences for next test
      SharedPreferences.setMockInitialValues({});
    });

    group('Review Local Storage', () {
      test('should save and retrieve review prompt state', () async {
        const state = ReviewPromptState(sessionCount: 5, positiveInteractionCount: 3, hasReviewed: false, preference: ReviewPromptPreference.enabled);

        await localProvider.saveReviewPromptState(state);
        final retrievedState = await localProvider.getReviewPromptState();

        expect(retrievedState, isNotNull);
        expect(retrievedState!.sessionCount, equals(5));
        expect(retrievedState.positiveInteractionCount, equals(3));
        expect(retrievedState.hasReviewed, equals(false));
        expect(retrievedState.preference, equals(ReviewPromptPreference.enabled));
      });

      test('should increment session count', () async {
        await localProvider.incrementSessionCount();
        await localProvider.incrementSessionCount();
        await localProvider.incrementSessionCount();

        final state = await localProvider.getReviewPromptState();
        expect(state?.sessionCount, equals(3));
      });

      test('should add interactions and update positive count', () async {
        final interaction1 = ReviewInteraction(type: ReviewInteractionType.successfulAuth, timestamp: DateTime.now());
        final interaction2 = ReviewInteraction(type: ReviewInteractionType.favoriteAdded, timestamp: DateTime.now());
        final interaction3 = ReviewInteraction(type: ReviewInteractionType.appSession, timestamp: DateTime.now());

        await localProvider.addInteraction(interaction1);
        await localProvider.addInteraction(interaction2);
        await localProvider.addInteraction(interaction3);

        final state = await localProvider.getReviewPromptState();
        expect(state?.interactions.length, equals(3));
        expect(state?.positiveInteractionCount, equals(2)); // Only auth and favorite are positive
      });

      test('should set first install date only once', () async {
        await localProvider.setFirstInstallDateIfNotExists();
        final firstDate = await localProvider.getFirstInstallDate();

        // Wait a bit and try to set again
        await Future.delayed(const Duration(milliseconds: 10));
        await localProvider.setFirstInstallDateIfNotExists();
        final secondDate = await localProvider.getFirstInstallDate();

        expect(firstDate, equals(secondDate));
      });
    });

    group('Review Repository', () {
      test('should check eligibility correctly - not eligible due to insufficient sessions', () async {
        final eligibility = await repository.checkEligibility();

        expect(eligibility.isEligible, isFalse);
        expect(eligibility.reason, contains('Insufficient sessions'));
      });

      test('should check eligibility correctly - not eligible due to insufficient interactions', () async {
        // Add enough sessions but not enough positive interactions
        await repository.incrementSessionCount();
        await repository.incrementSessionCount();
        await repository.incrementSessionCount();

        final eligibility = await repository.checkEligibility();

        expect(eligibility.isEligible, isFalse);
        expect(eligibility.reason, contains('Insufficient positive interactions'));
      });

      test('should check eligibility correctly - eligible when all criteria met', () async {
        // Set up sufficient sessions
        await repository.incrementSessionCount();
        await repository.incrementSessionCount();
        await repository.incrementSessionCount();

        // Add sufficient positive interactions
        await repository.recordInteraction(ReviewInteractionType.successfulAuth);
        await repository.recordInteraction(ReviewInteractionType.favoriteAdded);

        // Set install date to past
        final state = await repository.getReviewPromptState();
        final pastDate = DateTime.now().subtract(const Duration(days: 3));
        final updatedState = state.copyWith(firstInstallDate: pastDate);
        await repository.saveReviewPromptState(updatedState);

        final eligibility = await repository.checkEligibility();

        expect(eligibility.isEligible, isTrue);
        expect(eligibility.reason, equals('All eligibility criteria met'));
      });

      test('should not be eligible if user has already reviewed', () async {
        await repository.markAsReviewed();
        final eligibility = await repository.checkEligibility();

        expect(eligibility.isEligible, isFalse);
        expect(eligibility.reason, contains('already reviewed'));
      });

      test('should not be eligible if user opted out', () async {
        await repository.setReviewPromptPreference(ReviewPromptPreference.neverAsk);
        final eligibility = await repository.checkEligibility();

        expect(eligibility.isEligible, isFalse);
        expect(eligibility.reason, contains('opted out'));
      });

      test('should record prompt result correctly', () async {
        final result = ReviewPromptResult(action: ReviewPromptAction.reviewed, timestamp: DateTime.now());

        await repository.recordPromptResult(result);
        final state = await repository.getReviewPromptState();

        expect(state.hasReviewed, isTrue);
      });
    });

    group('Review Service', () {
      test('should record interactions correctly', () async {
        await reviewService.recordSuccessfulAuth(metadata: {'user_id': 'test123'});
        await reviewService.recordFavoriteAdded(metadata: {'medication': 'aspirin'});
        await reviewService.recordSuccessfulSearch(metadata: {'query': 'paracetamol'});

        final state = await reviewService.getReviewState();
        expect(state.interactions.length, equals(3));
        expect(state.positiveInteractionCount, equals(3));
      });

      test('should check native review availability', () async {
        // This will depend on the test environment
        final isAvailable = await reviewService.isNativeReviewAvailable();
        expect(isAvailable, isA<bool>());
      });

      test('should get and update configuration', () async {
        const newConfig = ReviewPromptConfig(minimumSessionCount: 5, minimumPositiveInteractions: 3, minimumDaysSinceInstall: 7);

        await reviewService.updateConfig(newConfig);
        final retrievedConfig = await reviewService.getConfig();

        expect(retrievedConfig.minimumSessionCount, equals(5));
        expect(retrievedConfig.minimumPositiveInteractions, equals(3));
        expect(retrievedConfig.minimumDaysSinceInstall, equals(7));
      });
    });

    group('Review Manager Provider', () {
      testWidgets('should initialize and track interactions', (WidgetTester tester) async {
        final container = ProviderContainer();
        addTearDown(container.dispose);

        final reviewManager = container.read(reviewManagerProvider.notifier);

        // Test session increment
        await reviewManager.incrementSessionCount();
        await reviewManager.incrementSessionCount();

        // Test interaction recording
        await reviewManager.recordSuccessfulAuth();
        await reviewManager.recordFavoriteAdded();

        final state = container.read(reviewManagerProvider);
        expect(state.promptState.sessionCount, equals(2));
        expect(state.promptState.positiveInteractionCount, equals(2));
      });

      testWidgets('should check eligibility correctly', (WidgetTester tester) async {
        final container = ProviderContainer();
        addTearDown(container.dispose);

        final reviewManager = container.read(reviewManagerProvider.notifier);

        // Initially not eligible
        final initialEligibility = await reviewManager.checkEligibility();
        expect(initialEligibility.isEligible, isFalse);

        // Make eligible
        await reviewManager.incrementSessionCount();
        await reviewManager.incrementSessionCount();
        await reviewManager.incrementSessionCount();
        await reviewManager.recordSuccessfulAuth();
        await reviewManager.recordFavoriteAdded();

        // Set past install date
        final repository = container.read(reviewRepositoryProvider);
        final state = await repository.getReviewPromptState();
        final pastDate = DateTime.now().subtract(const Duration(days: 3));
        final updatedState = state.copyWith(firstInstallDate: pastDate);
        await repository.saveReviewPromptState(updatedState);

        final eligibility = await reviewManager.checkEligibility();
        expect(eligibility.isEligible, isTrue);
      });
    });

    group('Review Prompt Timing', () {
      test('should respect cooldown periods', () async {
        // Set up eligible state
        await repository.incrementSessionCount();
        await repository.incrementSessionCount();
        await repository.incrementSessionCount();
        await repository.recordInteraction(ReviewInteractionType.successfulAuth);
        await repository.recordInteraction(ReviewInteractionType.favoriteAdded);

        // Set past install date
        final state = await repository.getReviewPromptState();
        final pastDate = DateTime.now().subtract(const Duration(days: 3));
        final updatedState = state.copyWith(firstInstallDate: pastDate);
        await repository.saveReviewPromptState(updatedState);

        // Should be eligible initially
        var eligibility = await repository.checkEligibility();
        expect(eligibility.isEligible, isTrue);

        // Record prompt shown
        await repository.recordPromptShown();

        // Should not be eligible immediately after showing prompt
        eligibility = await repository.checkEligibility();
        expect(eligibility.isEligible, isFalse);
        expect(eligibility.reason, contains('Too soon since last prompt'));
      });

      test('should handle remind later preference', () async {
        // Set up sufficient sessions and interactions first
        await repository.incrementSessionCount();
        await repository.incrementSessionCount();
        await repository.incrementSessionCount();
        await repository.recordInteraction(ReviewInteractionType.successfulAuth);
        await repository.recordInteraction(ReviewInteractionType.favoriteAdded);

        // Set past install date
        final state = await repository.getReviewPromptState();
        final pastDate = DateTime.now().subtract(const Duration(days: 3));
        final updatedState = state.copyWith(firstInstallDate: pastDate);
        await repository.saveReviewPromptState(updatedState);

        await repository.setReviewPromptPreference(ReviewPromptPreference.remindLater);
        await repository.recordPromptShown();

        final eligibility = await repository.checkEligibility();
        expect(eligibility.isEligible, isFalse);
        expect(eligibility.reason, contains('Remind later period active'));
      });
    });
  });
}

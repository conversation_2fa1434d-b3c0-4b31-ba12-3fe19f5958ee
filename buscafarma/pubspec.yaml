name: bus<PERSON><PERSON><PERSON>
description: "Una aplicación para encontrar farmacias cercanas a ti."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.4+5

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8

  # State management
  flutter_riverpod: ^2.6.1

  # Routing
  fluro: ^2.0.5

  # Map
  flutter_map: ^8.1.1
  latlong2: ^0.9.1

  # Location
  geolocator: ^14.0.1
  permission_handler: ^12.0.0+1

  # HTTP client
  dio: ^5.8.0+1

  # Local storage
  shared_preferences: ^2.5.3

  # UI
  dynamic_color: ^1.7.0
  font_awesome_flutter: ^10.8.0

  # Utilities
  intl: ^0.20.2
  freezed_annotation: ^3.0.0
  json_annotation: ^4.9.0
  dartz: ^0.10.1
  url_launcher: ^6.3.1

  # In-app review
  in_app_review: ^2.0.10

  # In-app update
  in_app_update: ^4.2.3
  package_info_plus: ^8.3.0

  # AppLovin MAX for monetization
  applovin_max: ^4.5.0

  # Firebase
  firebase_core: ^3.13.1
  firebase_crashlytics: ^4.3.6
  firebase_auth: ^5.5.4
  firebase_remote_config: ^5.4.4
  firebase_analytics: ^11.4.6
  google_sign_in: ^6.3.0
  flutter_displaymode: ^0.6.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

  # Testing
  mockito: ^5.4.4

  # Code generation
  build_runner: ^2.4.15
  freezed: ^3.0.6
  json_serializable: ^6.9.5

  # App icon generation
  flutter_launcher_icons: ^0.14.1

# Flutter launcher icons configuration
flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/img/logo.png"
  min_sdk_android: 21 # android min sdk min:16, default 21
  web:
    generate: true
    image_path: "assets/img/logo.png"
    background_color: "#ffffff"
    theme_color: "#2196f3"
  windows:
    generate: true
    image_path: "assets/img/logo.png"
    icon_size: 48 # min:48, max:256, default: 48
  macos:
    generate: true
    image_path: "assets/img/logo.png"

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/json/
    - assets/img/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';

part 'medication_favorite.freezed.dart';
part 'medication_favorite.g.dart';

@freezed
abstract class MedicationFavorite with _$MedicationFavorite {
  const factory MedicationFavorite({required String id, required String codEstab, required int codProdE, required ProductPrice medicationData, required DateTime createdAt, required DateTime updatedAt}) = _MedicationFavorite;

  factory MedicationFavorite.fromJson(Map<String, dynamic> json) => _$MedicationFavoriteFromJson(json);
}

@freezed
abstract class PaginatedFavorites with _$PaginatedFavorites {
  const factory PaginatedFavorites({required List<MedicationFavorite> favorites, required FavoritesPagination pagination}) = _PaginatedFavorites;

  factory PaginatedFavorites.fromJson(Map<String, dynamic> json) => _$PaginatedFavoritesFromJson(json);
}

@freezed
abstract class FavoritesPagination with _$FavoritesPagination {
  const factory FavoritesPagination({required int page, required int limit, required int total, required int totalPages, required bool hasNext, required bool hasPrev}) = _FavoritesPagination;

  factory FavoritesPagination.fromJson(Map<String, dynamic> json) => _$FavoritesPaginationFromJson(json);
}

@freezed
abstract class AddFavoriteRequest with _$AddFavoriteRequest {
  const factory AddFavoriteRequest({
    required String codEstab,
    required int codProdE,
    required String fecha,
    required String nombreProducto,
    required double precio1,
    required double precio2,
    double? precio3,
    required String codGrupoFF,
    required String ubicodigo,
    required String direccion,
    required String telefono,
    required String nomGrupoFF,
    required String setcodigo,
    required String nombreComercial,
    required String grupo,
    required String totalPA,
    required String concent,
    required String nombreFormaFarmaceutica,
    required int fracciones,
    String? totalRegistros,
    required String nombreLaboratorio,
    required String nombreTitular,
    required String catCodigo,
    required String nombreSustancia,
    String? fabricante,
    required String departamento,
    required String provincia,
    required String distrito,
  }) = _AddFavoriteRequest;

  factory AddFavoriteRequest.fromJson(Map<String, dynamic> json) => _$AddFavoriteRequestFromJson(json);

  factory AddFavoriteRequest.fromProductPrice(ProductPrice productPrice) {
    return AddFavoriteRequest(
      codEstab: productPrice.codEstab,
      codProdE: productPrice.codProdE,
      fecha: productPrice.fecha ?? DateTime.now().toString(),
      nombreProducto: productPrice.nombreProducto ?? '',
      precio1: productPrice.precio1,
      precio2: productPrice.precio2 ?? 0.0,
      precio3: productPrice.precio3,
      codGrupoFF: productPrice.codGrupoFF ?? '',
      ubicodigo: productPrice.ubicodigo ?? '',
      direccion: productPrice.direccion ?? '',
      telefono: productPrice.telefono ?? '',
      nomGrupoFF: productPrice.nomGrupoFF ?? '',
      setcodigo: productPrice.setcodigo,
      nombreComercial: productPrice.nombreComercial ?? '',
      grupo: productPrice.grupo ?? '',
      totalPA: productPrice.totalPA ?? '',
      concent: productPrice.concent,
      nombreFormaFarmaceutica: productPrice.nombreFormaFarmaceutica,
      fracciones: productPrice.fracciones,
      totalRegistros: productPrice.totalRegistros,
      nombreLaboratorio: productPrice.nombreLaboratorio ?? '',
      nombreTitular: productPrice.nombreTitular ?? '',
      catCodigo: productPrice.catCodigo ?? '',
      nombreSustancia: productPrice.nombreSustancia,
      fabricante: productPrice.fabricante,
      departamento: productPrice.departamento ?? '',
      provincia: productPrice.provincia ?? '',
      distrito: productPrice.distrito ?? '',
    );
  }
}

@freezed
abstract class RemoveFavoriteRequest with _$RemoveFavoriteRequest {
  const factory RemoveFavoriteRequest({required String codEstab, required int codProdE}) = _RemoveFavoriteRequest;

  factory RemoveFavoriteRequest.fromJson(Map<String, dynamic> json) => _$RemoveFavoriteRequestFromJson(json);
}

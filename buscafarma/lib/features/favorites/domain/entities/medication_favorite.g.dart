// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'medication_favorite.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_MedicationFavorite _$MedicationFavoriteFromJson(Map<String, dynamic> json) =>
    _MedicationFavorite(
      id: json['id'] as String,
      codEstab: json['codEstab'] as String,
      codProdE: (json['codProdE'] as num).toInt(),
      medicationData: ProductPrice.fromJson(
        json['medicationData'] as Map<String, dynamic>,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );

Map<String, dynamic> _$MedicationFavoriteToJson(_MedicationFavorite instance) =>
    <String, dynamic>{
      'id': instance.id,
      'codEstab': instance.codEstab,
      'codProdE': instance.codProdE,
      'medicationData': instance.medicationData,
      'createdAt': instance.createdAt.toIso8601String(),
      'updatedAt': instance.updatedAt.toIso8601String(),
    };

_PaginatedFavorites _$PaginatedFavoritesFromJson(Map<String, dynamic> json) =>
    _PaginatedFavorites(
      favorites:
          (json['favorites'] as List<dynamic>)
              .map(
                (e) => MedicationFavorite.fromJson(e as Map<String, dynamic>),
              )
              .toList(),
      pagination: FavoritesPagination.fromJson(
        json['pagination'] as Map<String, dynamic>,
      ),
    );

Map<String, dynamic> _$PaginatedFavoritesToJson(_PaginatedFavorites instance) =>
    <String, dynamic>{
      'favorites': instance.favorites,
      'pagination': instance.pagination,
    };

_FavoritesPagination _$FavoritesPaginationFromJson(Map<String, dynamic> json) =>
    _FavoritesPagination(
      page: (json['page'] as num).toInt(),
      limit: (json['limit'] as num).toInt(),
      total: (json['total'] as num).toInt(),
      totalPages: (json['totalPages'] as num).toInt(),
      hasNext: json['hasNext'] as bool,
      hasPrev: json['hasPrev'] as bool,
    );

Map<String, dynamic> _$FavoritesPaginationToJson(
  _FavoritesPagination instance,
) => <String, dynamic>{
  'page': instance.page,
  'limit': instance.limit,
  'total': instance.total,
  'totalPages': instance.totalPages,
  'hasNext': instance.hasNext,
  'hasPrev': instance.hasPrev,
};

_AddFavoriteRequest _$AddFavoriteRequestFromJson(Map<String, dynamic> json) =>
    _AddFavoriteRequest(
      codEstab: json['codEstab'] as String,
      codProdE: (json['codProdE'] as num).toInt(),
      fecha: json['fecha'] as String,
      nombreProducto: json['nombreProducto'] as String,
      precio1: (json['precio1'] as num).toDouble(),
      precio2: (json['precio2'] as num).toDouble(),
      precio3: (json['precio3'] as num?)?.toDouble(),
      codGrupoFF: json['codGrupoFF'] as String,
      ubicodigo: json['ubicodigo'] as String,
      direccion: json['direccion'] as String,
      telefono: json['telefono'] as String,
      nomGrupoFF: json['nomGrupoFF'] as String,
      setcodigo: json['setcodigo'] as String,
      nombreComercial: json['nombreComercial'] as String,
      grupo: json['grupo'] as String,
      totalPA: json['totalPA'] as String,
      concent: json['concent'] as String,
      nombreFormaFarmaceutica: json['nombreFormaFarmaceutica'] as String,
      fracciones: (json['fracciones'] as num).toInt(),
      totalRegistros: json['totalRegistros'] as String?,
      nombreLaboratorio: json['nombreLaboratorio'] as String,
      nombreTitular: json['nombreTitular'] as String,
      catCodigo: json['catCodigo'] as String,
      nombreSustancia: json['nombreSustancia'] as String,
      fabricante: json['fabricante'] as String?,
      departamento: json['departamento'] as String,
      provincia: json['provincia'] as String,
      distrito: json['distrito'] as String,
    );

Map<String, dynamic> _$AddFavoriteRequestToJson(_AddFavoriteRequest instance) =>
    <String, dynamic>{
      'codEstab': instance.codEstab,
      'codProdE': instance.codProdE,
      'fecha': instance.fecha,
      'nombreProducto': instance.nombreProducto,
      'precio1': instance.precio1,
      'precio2': instance.precio2,
      'precio3': instance.precio3,
      'codGrupoFF': instance.codGrupoFF,
      'ubicodigo': instance.ubicodigo,
      'direccion': instance.direccion,
      'telefono': instance.telefono,
      'nomGrupoFF': instance.nomGrupoFF,
      'setcodigo': instance.setcodigo,
      'nombreComercial': instance.nombreComercial,
      'grupo': instance.grupo,
      'totalPA': instance.totalPA,
      'concent': instance.concent,
      'nombreFormaFarmaceutica': instance.nombreFormaFarmaceutica,
      'fracciones': instance.fracciones,
      'totalRegistros': instance.totalRegistros,
      'nombreLaboratorio': instance.nombreLaboratorio,
      'nombreTitular': instance.nombreTitular,
      'catCodigo': instance.catCodigo,
      'nombreSustancia': instance.nombreSustancia,
      'fabricante': instance.fabricante,
      'departamento': instance.departamento,
      'provincia': instance.provincia,
      'distrito': instance.distrito,
    };

_RemoveFavoriteRequest _$RemoveFavoriteRequestFromJson(
  Map<String, dynamic> json,
) => _RemoveFavoriteRequest(
  codEstab: json['codEstab'] as String,
  codProdE: (json['codProdE'] as num).toInt(),
);

Map<String, dynamic> _$RemoveFavoriteRequestToJson(
  _RemoveFavoriteRequest instance,
) => <String, dynamic>{
  'codEstab': instance.codEstab,
  'codProdE': instance.codProdE,
};

// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'medication_favorite.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$MedicationFavorite {

 String get id; String get codEstab; int get codProdE; ProductPrice get medicationData; DateTime get createdAt; DateTime get updatedAt;
/// Create a copy of MedicationFavorite
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MedicationFavoriteCopyWith<MedicationFavorite> get copyWith => _$MedicationFavoriteCopyWithImpl<MedicationFavorite>(this as MedicationFavorite, _$identity);

  /// Serializes this MedicationFavorite to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MedicationFavorite&&(identical(other.id, id) || other.id == id)&&(identical(other.codEstab, codEstab) || other.codEstab == codEstab)&&(identical(other.codProdE, codProdE) || other.codProdE == codProdE)&&(identical(other.medicationData, medicationData) || other.medicationData == medicationData)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,codEstab,codProdE,medicationData,createdAt,updatedAt);

@override
String toString() {
  return 'MedicationFavorite(id: $id, codEstab: $codEstab, codProdE: $codProdE, medicationData: $medicationData, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class $MedicationFavoriteCopyWith<$Res>  {
  factory $MedicationFavoriteCopyWith(MedicationFavorite value, $Res Function(MedicationFavorite) _then) = _$MedicationFavoriteCopyWithImpl;
@useResult
$Res call({
 String id, String codEstab, int codProdE, ProductPrice medicationData, DateTime createdAt, DateTime updatedAt
});


$ProductPriceCopyWith<$Res> get medicationData;

}
/// @nodoc
class _$MedicationFavoriteCopyWithImpl<$Res>
    implements $MedicationFavoriteCopyWith<$Res> {
  _$MedicationFavoriteCopyWithImpl(this._self, this._then);

  final MedicationFavorite _self;
  final $Res Function(MedicationFavorite) _then;

/// Create a copy of MedicationFavorite
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? codEstab = null,Object? codProdE = null,Object? medicationData = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,codEstab: null == codEstab ? _self.codEstab : codEstab // ignore: cast_nullable_to_non_nullable
as String,codProdE: null == codProdE ? _self.codProdE : codProdE // ignore: cast_nullable_to_non_nullable
as int,medicationData: null == medicationData ? _self.medicationData : medicationData // ignore: cast_nullable_to_non_nullable
as ProductPrice,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}
/// Create a copy of MedicationFavorite
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductPriceCopyWith<$Res> get medicationData {
  
  return $ProductPriceCopyWith<$Res>(_self.medicationData, (value) {
    return _then(_self.copyWith(medicationData: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _MedicationFavorite implements MedicationFavorite {
  const _MedicationFavorite({required this.id, required this.codEstab, required this.codProdE, required this.medicationData, required this.createdAt, required this.updatedAt});
  factory _MedicationFavorite.fromJson(Map<String, dynamic> json) => _$MedicationFavoriteFromJson(json);

@override final  String id;
@override final  String codEstab;
@override final  int codProdE;
@override final  ProductPrice medicationData;
@override final  DateTime createdAt;
@override final  DateTime updatedAt;

/// Create a copy of MedicationFavorite
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MedicationFavoriteCopyWith<_MedicationFavorite> get copyWith => __$MedicationFavoriteCopyWithImpl<_MedicationFavorite>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MedicationFavoriteToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MedicationFavorite&&(identical(other.id, id) || other.id == id)&&(identical(other.codEstab, codEstab) || other.codEstab == codEstab)&&(identical(other.codProdE, codProdE) || other.codProdE == codProdE)&&(identical(other.medicationData, medicationData) || other.medicationData == medicationData)&&(identical(other.createdAt, createdAt) || other.createdAt == createdAt)&&(identical(other.updatedAt, updatedAt) || other.updatedAt == updatedAt));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,codEstab,codProdE,medicationData,createdAt,updatedAt);

@override
String toString() {
  return 'MedicationFavorite(id: $id, codEstab: $codEstab, codProdE: $codProdE, medicationData: $medicationData, createdAt: $createdAt, updatedAt: $updatedAt)';
}


}

/// @nodoc
abstract mixin class _$MedicationFavoriteCopyWith<$Res> implements $MedicationFavoriteCopyWith<$Res> {
  factory _$MedicationFavoriteCopyWith(_MedicationFavorite value, $Res Function(_MedicationFavorite) _then) = __$MedicationFavoriteCopyWithImpl;
@override @useResult
$Res call({
 String id, String codEstab, int codProdE, ProductPrice medicationData, DateTime createdAt, DateTime updatedAt
});


@override $ProductPriceCopyWith<$Res> get medicationData;

}
/// @nodoc
class __$MedicationFavoriteCopyWithImpl<$Res>
    implements _$MedicationFavoriteCopyWith<$Res> {
  __$MedicationFavoriteCopyWithImpl(this._self, this._then);

  final _MedicationFavorite _self;
  final $Res Function(_MedicationFavorite) _then;

/// Create a copy of MedicationFavorite
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? codEstab = null,Object? codProdE = null,Object? medicationData = null,Object? createdAt = null,Object? updatedAt = null,}) {
  return _then(_MedicationFavorite(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,codEstab: null == codEstab ? _self.codEstab : codEstab // ignore: cast_nullable_to_non_nullable
as String,codProdE: null == codProdE ? _self.codProdE : codProdE // ignore: cast_nullable_to_non_nullable
as int,medicationData: null == medicationData ? _self.medicationData : medicationData // ignore: cast_nullable_to_non_nullable
as ProductPrice,createdAt: null == createdAt ? _self.createdAt : createdAt // ignore: cast_nullable_to_non_nullable
as DateTime,updatedAt: null == updatedAt ? _self.updatedAt : updatedAt // ignore: cast_nullable_to_non_nullable
as DateTime,
  ));
}

/// Create a copy of MedicationFavorite
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ProductPriceCopyWith<$Res> get medicationData {
  
  return $ProductPriceCopyWith<$Res>(_self.medicationData, (value) {
    return _then(_self.copyWith(medicationData: value));
  });
}
}


/// @nodoc
mixin _$PaginatedFavorites {

 List<MedicationFavorite> get favorites; FavoritesPagination get pagination;
/// Create a copy of PaginatedFavorites
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$PaginatedFavoritesCopyWith<PaginatedFavorites> get copyWith => _$PaginatedFavoritesCopyWithImpl<PaginatedFavorites>(this as PaginatedFavorites, _$identity);

  /// Serializes this PaginatedFavorites to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is PaginatedFavorites&&const DeepCollectionEquality().equals(other.favorites, favorites)&&(identical(other.pagination, pagination) || other.pagination == pagination));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(favorites),pagination);

@override
String toString() {
  return 'PaginatedFavorites(favorites: $favorites, pagination: $pagination)';
}


}

/// @nodoc
abstract mixin class $PaginatedFavoritesCopyWith<$Res>  {
  factory $PaginatedFavoritesCopyWith(PaginatedFavorites value, $Res Function(PaginatedFavorites) _then) = _$PaginatedFavoritesCopyWithImpl;
@useResult
$Res call({
 List<MedicationFavorite> favorites, FavoritesPagination pagination
});


$FavoritesPaginationCopyWith<$Res> get pagination;

}
/// @nodoc
class _$PaginatedFavoritesCopyWithImpl<$Res>
    implements $PaginatedFavoritesCopyWith<$Res> {
  _$PaginatedFavoritesCopyWithImpl(this._self, this._then);

  final PaginatedFavorites _self;
  final $Res Function(PaginatedFavorites) _then;

/// Create a copy of PaginatedFavorites
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? favorites = null,Object? pagination = null,}) {
  return _then(_self.copyWith(
favorites: null == favorites ? _self.favorites : favorites // ignore: cast_nullable_to_non_nullable
as List<MedicationFavorite>,pagination: null == pagination ? _self.pagination : pagination // ignore: cast_nullable_to_non_nullable
as FavoritesPagination,
  ));
}
/// Create a copy of PaginatedFavorites
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FavoritesPaginationCopyWith<$Res> get pagination {
  
  return $FavoritesPaginationCopyWith<$Res>(_self.pagination, (value) {
    return _then(_self.copyWith(pagination: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _PaginatedFavorites implements PaginatedFavorites {
  const _PaginatedFavorites({required final  List<MedicationFavorite> favorites, required this.pagination}): _favorites = favorites;
  factory _PaginatedFavorites.fromJson(Map<String, dynamic> json) => _$PaginatedFavoritesFromJson(json);

 final  List<MedicationFavorite> _favorites;
@override List<MedicationFavorite> get favorites {
  if (_favorites is EqualUnmodifiableListView) return _favorites;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_favorites);
}

@override final  FavoritesPagination pagination;

/// Create a copy of PaginatedFavorites
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$PaginatedFavoritesCopyWith<_PaginatedFavorites> get copyWith => __$PaginatedFavoritesCopyWithImpl<_PaginatedFavorites>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$PaginatedFavoritesToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _PaginatedFavorites&&const DeepCollectionEquality().equals(other._favorites, _favorites)&&(identical(other.pagination, pagination) || other.pagination == pagination));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_favorites),pagination);

@override
String toString() {
  return 'PaginatedFavorites(favorites: $favorites, pagination: $pagination)';
}


}

/// @nodoc
abstract mixin class _$PaginatedFavoritesCopyWith<$Res> implements $PaginatedFavoritesCopyWith<$Res> {
  factory _$PaginatedFavoritesCopyWith(_PaginatedFavorites value, $Res Function(_PaginatedFavorites) _then) = __$PaginatedFavoritesCopyWithImpl;
@override @useResult
$Res call({
 List<MedicationFavorite> favorites, FavoritesPagination pagination
});


@override $FavoritesPaginationCopyWith<$Res> get pagination;

}
/// @nodoc
class __$PaginatedFavoritesCopyWithImpl<$Res>
    implements _$PaginatedFavoritesCopyWith<$Res> {
  __$PaginatedFavoritesCopyWithImpl(this._self, this._then);

  final _PaginatedFavorites _self;
  final $Res Function(_PaginatedFavorites) _then;

/// Create a copy of PaginatedFavorites
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? favorites = null,Object? pagination = null,}) {
  return _then(_PaginatedFavorites(
favorites: null == favorites ? _self._favorites : favorites // ignore: cast_nullable_to_non_nullable
as List<MedicationFavorite>,pagination: null == pagination ? _self.pagination : pagination // ignore: cast_nullable_to_non_nullable
as FavoritesPagination,
  ));
}

/// Create a copy of PaginatedFavorites
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FavoritesPaginationCopyWith<$Res> get pagination {
  
  return $FavoritesPaginationCopyWith<$Res>(_self.pagination, (value) {
    return _then(_self.copyWith(pagination: value));
  });
}
}


/// @nodoc
mixin _$FavoritesPagination {

 int get page; int get limit; int get total; int get totalPages; bool get hasNext; bool get hasPrev;
/// Create a copy of FavoritesPagination
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$FavoritesPaginationCopyWith<FavoritesPagination> get copyWith => _$FavoritesPaginationCopyWithImpl<FavoritesPagination>(this as FavoritesPagination, _$identity);

  /// Serializes this FavoritesPagination to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is FavoritesPagination&&(identical(other.page, page) || other.page == page)&&(identical(other.limit, limit) || other.limit == limit)&&(identical(other.total, total) || other.total == total)&&(identical(other.totalPages, totalPages) || other.totalPages == totalPages)&&(identical(other.hasNext, hasNext) || other.hasNext == hasNext)&&(identical(other.hasPrev, hasPrev) || other.hasPrev == hasPrev));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,page,limit,total,totalPages,hasNext,hasPrev);

@override
String toString() {
  return 'FavoritesPagination(page: $page, limit: $limit, total: $total, totalPages: $totalPages, hasNext: $hasNext, hasPrev: $hasPrev)';
}


}

/// @nodoc
abstract mixin class $FavoritesPaginationCopyWith<$Res>  {
  factory $FavoritesPaginationCopyWith(FavoritesPagination value, $Res Function(FavoritesPagination) _then) = _$FavoritesPaginationCopyWithImpl;
@useResult
$Res call({
 int page, int limit, int total, int totalPages, bool hasNext, bool hasPrev
});




}
/// @nodoc
class _$FavoritesPaginationCopyWithImpl<$Res>
    implements $FavoritesPaginationCopyWith<$Res> {
  _$FavoritesPaginationCopyWithImpl(this._self, this._then);

  final FavoritesPagination _self;
  final $Res Function(FavoritesPagination) _then;

/// Create a copy of FavoritesPagination
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? page = null,Object? limit = null,Object? total = null,Object? totalPages = null,Object? hasNext = null,Object? hasPrev = null,}) {
  return _then(_self.copyWith(
page: null == page ? _self.page : page // ignore: cast_nullable_to_non_nullable
as int,limit: null == limit ? _self.limit : limit // ignore: cast_nullable_to_non_nullable
as int,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,totalPages: null == totalPages ? _self.totalPages : totalPages // ignore: cast_nullable_to_non_nullable
as int,hasNext: null == hasNext ? _self.hasNext : hasNext // ignore: cast_nullable_to_non_nullable
as bool,hasPrev: null == hasPrev ? _self.hasPrev : hasPrev // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _FavoritesPagination implements FavoritesPagination {
  const _FavoritesPagination({required this.page, required this.limit, required this.total, required this.totalPages, required this.hasNext, required this.hasPrev});
  factory _FavoritesPagination.fromJson(Map<String, dynamic> json) => _$FavoritesPaginationFromJson(json);

@override final  int page;
@override final  int limit;
@override final  int total;
@override final  int totalPages;
@override final  bool hasNext;
@override final  bool hasPrev;

/// Create a copy of FavoritesPagination
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$FavoritesPaginationCopyWith<_FavoritesPagination> get copyWith => __$FavoritesPaginationCopyWithImpl<_FavoritesPagination>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$FavoritesPaginationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _FavoritesPagination&&(identical(other.page, page) || other.page == page)&&(identical(other.limit, limit) || other.limit == limit)&&(identical(other.total, total) || other.total == total)&&(identical(other.totalPages, totalPages) || other.totalPages == totalPages)&&(identical(other.hasNext, hasNext) || other.hasNext == hasNext)&&(identical(other.hasPrev, hasPrev) || other.hasPrev == hasPrev));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,page,limit,total,totalPages,hasNext,hasPrev);

@override
String toString() {
  return 'FavoritesPagination(page: $page, limit: $limit, total: $total, totalPages: $totalPages, hasNext: $hasNext, hasPrev: $hasPrev)';
}


}

/// @nodoc
abstract mixin class _$FavoritesPaginationCopyWith<$Res> implements $FavoritesPaginationCopyWith<$Res> {
  factory _$FavoritesPaginationCopyWith(_FavoritesPagination value, $Res Function(_FavoritesPagination) _then) = __$FavoritesPaginationCopyWithImpl;
@override @useResult
$Res call({
 int page, int limit, int total, int totalPages, bool hasNext, bool hasPrev
});




}
/// @nodoc
class __$FavoritesPaginationCopyWithImpl<$Res>
    implements _$FavoritesPaginationCopyWith<$Res> {
  __$FavoritesPaginationCopyWithImpl(this._self, this._then);

  final _FavoritesPagination _self;
  final $Res Function(_FavoritesPagination) _then;

/// Create a copy of FavoritesPagination
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? page = null,Object? limit = null,Object? total = null,Object? totalPages = null,Object? hasNext = null,Object? hasPrev = null,}) {
  return _then(_FavoritesPagination(
page: null == page ? _self.page : page // ignore: cast_nullable_to_non_nullable
as int,limit: null == limit ? _self.limit : limit // ignore: cast_nullable_to_non_nullable
as int,total: null == total ? _self.total : total // ignore: cast_nullable_to_non_nullable
as int,totalPages: null == totalPages ? _self.totalPages : totalPages // ignore: cast_nullable_to_non_nullable
as int,hasNext: null == hasNext ? _self.hasNext : hasNext // ignore: cast_nullable_to_non_nullable
as bool,hasPrev: null == hasPrev ? _self.hasPrev : hasPrev // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$AddFavoriteRequest {

 String get codEstab; int get codProdE; String get fecha; String get nombreProducto; double get precio1; double get precio2; double? get precio3; String get codGrupoFF; String get ubicodigo; String get direccion; String get telefono; String get nomGrupoFF; String get setcodigo; String get nombreComercial; String get grupo; String get totalPA; String get concent; String get nombreFormaFarmaceutica; int get fracciones; String? get totalRegistros; String get nombreLaboratorio; String get nombreTitular; String get catCodigo; String get nombreSustancia; String? get fabricante; String get departamento; String get provincia; String get distrito;
/// Create a copy of AddFavoriteRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AddFavoriteRequestCopyWith<AddFavoriteRequest> get copyWith => _$AddFavoriteRequestCopyWithImpl<AddFavoriteRequest>(this as AddFavoriteRequest, _$identity);

  /// Serializes this AddFavoriteRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AddFavoriteRequest&&(identical(other.codEstab, codEstab) || other.codEstab == codEstab)&&(identical(other.codProdE, codProdE) || other.codProdE == codProdE)&&(identical(other.fecha, fecha) || other.fecha == fecha)&&(identical(other.nombreProducto, nombreProducto) || other.nombreProducto == nombreProducto)&&(identical(other.precio1, precio1) || other.precio1 == precio1)&&(identical(other.precio2, precio2) || other.precio2 == precio2)&&(identical(other.precio3, precio3) || other.precio3 == precio3)&&(identical(other.codGrupoFF, codGrupoFF) || other.codGrupoFF == codGrupoFF)&&(identical(other.ubicodigo, ubicodigo) || other.ubicodigo == ubicodigo)&&(identical(other.direccion, direccion) || other.direccion == direccion)&&(identical(other.telefono, telefono) || other.telefono == telefono)&&(identical(other.nomGrupoFF, nomGrupoFF) || other.nomGrupoFF == nomGrupoFF)&&(identical(other.setcodigo, setcodigo) || other.setcodigo == setcodigo)&&(identical(other.nombreComercial, nombreComercial) || other.nombreComercial == nombreComercial)&&(identical(other.grupo, grupo) || other.grupo == grupo)&&(identical(other.totalPA, totalPA) || other.totalPA == totalPA)&&(identical(other.concent, concent) || other.concent == concent)&&(identical(other.nombreFormaFarmaceutica, nombreFormaFarmaceutica) || other.nombreFormaFarmaceutica == nombreFormaFarmaceutica)&&(identical(other.fracciones, fracciones) || other.fracciones == fracciones)&&(identical(other.totalRegistros, totalRegistros) || other.totalRegistros == totalRegistros)&&(identical(other.nombreLaboratorio, nombreLaboratorio) || other.nombreLaboratorio == nombreLaboratorio)&&(identical(other.nombreTitular, nombreTitular) || other.nombreTitular == nombreTitular)&&(identical(other.catCodigo, catCodigo) || other.catCodigo == catCodigo)&&(identical(other.nombreSustancia, nombreSustancia) || other.nombreSustancia == nombreSustancia)&&(identical(other.fabricante, fabricante) || other.fabricante == fabricante)&&(identical(other.departamento, departamento) || other.departamento == departamento)&&(identical(other.provincia, provincia) || other.provincia == provincia)&&(identical(other.distrito, distrito) || other.distrito == distrito));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,codEstab,codProdE,fecha,nombreProducto,precio1,precio2,precio3,codGrupoFF,ubicodigo,direccion,telefono,nomGrupoFF,setcodigo,nombreComercial,grupo,totalPA,concent,nombreFormaFarmaceutica,fracciones,totalRegistros,nombreLaboratorio,nombreTitular,catCodigo,nombreSustancia,fabricante,departamento,provincia,distrito]);

@override
String toString() {
  return 'AddFavoriteRequest(codEstab: $codEstab, codProdE: $codProdE, fecha: $fecha, nombreProducto: $nombreProducto, precio1: $precio1, precio2: $precio2, precio3: $precio3, codGrupoFF: $codGrupoFF, ubicodigo: $ubicodigo, direccion: $direccion, telefono: $telefono, nomGrupoFF: $nomGrupoFF, setcodigo: $setcodigo, nombreComercial: $nombreComercial, grupo: $grupo, totalPA: $totalPA, concent: $concent, nombreFormaFarmaceutica: $nombreFormaFarmaceutica, fracciones: $fracciones, totalRegistros: $totalRegistros, nombreLaboratorio: $nombreLaboratorio, nombreTitular: $nombreTitular, catCodigo: $catCodigo, nombreSustancia: $nombreSustancia, fabricante: $fabricante, departamento: $departamento, provincia: $provincia, distrito: $distrito)';
}


}

/// @nodoc
abstract mixin class $AddFavoriteRequestCopyWith<$Res>  {
  factory $AddFavoriteRequestCopyWith(AddFavoriteRequest value, $Res Function(AddFavoriteRequest) _then) = _$AddFavoriteRequestCopyWithImpl;
@useResult
$Res call({
 String codEstab, int codProdE, String fecha, String nombreProducto, double precio1, double precio2, double? precio3, String codGrupoFF, String ubicodigo, String direccion, String telefono, String nomGrupoFF, String setcodigo, String nombreComercial, String grupo, String totalPA, String concent, String nombreFormaFarmaceutica, int fracciones, String? totalRegistros, String nombreLaboratorio, String nombreTitular, String catCodigo, String nombreSustancia, String? fabricante, String departamento, String provincia, String distrito
});




}
/// @nodoc
class _$AddFavoriteRequestCopyWithImpl<$Res>
    implements $AddFavoriteRequestCopyWith<$Res> {
  _$AddFavoriteRequestCopyWithImpl(this._self, this._then);

  final AddFavoriteRequest _self;
  final $Res Function(AddFavoriteRequest) _then;

/// Create a copy of AddFavoriteRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? codEstab = null,Object? codProdE = null,Object? fecha = null,Object? nombreProducto = null,Object? precio1 = null,Object? precio2 = null,Object? precio3 = freezed,Object? codGrupoFF = null,Object? ubicodigo = null,Object? direccion = null,Object? telefono = null,Object? nomGrupoFF = null,Object? setcodigo = null,Object? nombreComercial = null,Object? grupo = null,Object? totalPA = null,Object? concent = null,Object? nombreFormaFarmaceutica = null,Object? fracciones = null,Object? totalRegistros = freezed,Object? nombreLaboratorio = null,Object? nombreTitular = null,Object? catCodigo = null,Object? nombreSustancia = null,Object? fabricante = freezed,Object? departamento = null,Object? provincia = null,Object? distrito = null,}) {
  return _then(_self.copyWith(
codEstab: null == codEstab ? _self.codEstab : codEstab // ignore: cast_nullable_to_non_nullable
as String,codProdE: null == codProdE ? _self.codProdE : codProdE // ignore: cast_nullable_to_non_nullable
as int,fecha: null == fecha ? _self.fecha : fecha // ignore: cast_nullable_to_non_nullable
as String,nombreProducto: null == nombreProducto ? _self.nombreProducto : nombreProducto // ignore: cast_nullable_to_non_nullable
as String,precio1: null == precio1 ? _self.precio1 : precio1 // ignore: cast_nullable_to_non_nullable
as double,precio2: null == precio2 ? _self.precio2 : precio2 // ignore: cast_nullable_to_non_nullable
as double,precio3: freezed == precio3 ? _self.precio3 : precio3 // ignore: cast_nullable_to_non_nullable
as double?,codGrupoFF: null == codGrupoFF ? _self.codGrupoFF : codGrupoFF // ignore: cast_nullable_to_non_nullable
as String,ubicodigo: null == ubicodigo ? _self.ubicodigo : ubicodigo // ignore: cast_nullable_to_non_nullable
as String,direccion: null == direccion ? _self.direccion : direccion // ignore: cast_nullable_to_non_nullable
as String,telefono: null == telefono ? _self.telefono : telefono // ignore: cast_nullable_to_non_nullable
as String,nomGrupoFF: null == nomGrupoFF ? _self.nomGrupoFF : nomGrupoFF // ignore: cast_nullable_to_non_nullable
as String,setcodigo: null == setcodigo ? _self.setcodigo : setcodigo // ignore: cast_nullable_to_non_nullable
as String,nombreComercial: null == nombreComercial ? _self.nombreComercial : nombreComercial // ignore: cast_nullable_to_non_nullable
as String,grupo: null == grupo ? _self.grupo : grupo // ignore: cast_nullable_to_non_nullable
as String,totalPA: null == totalPA ? _self.totalPA : totalPA // ignore: cast_nullable_to_non_nullable
as String,concent: null == concent ? _self.concent : concent // ignore: cast_nullable_to_non_nullable
as String,nombreFormaFarmaceutica: null == nombreFormaFarmaceutica ? _self.nombreFormaFarmaceutica : nombreFormaFarmaceutica // ignore: cast_nullable_to_non_nullable
as String,fracciones: null == fracciones ? _self.fracciones : fracciones // ignore: cast_nullable_to_non_nullable
as int,totalRegistros: freezed == totalRegistros ? _self.totalRegistros : totalRegistros // ignore: cast_nullable_to_non_nullable
as String?,nombreLaboratorio: null == nombreLaboratorio ? _self.nombreLaboratorio : nombreLaboratorio // ignore: cast_nullable_to_non_nullable
as String,nombreTitular: null == nombreTitular ? _self.nombreTitular : nombreTitular // ignore: cast_nullable_to_non_nullable
as String,catCodigo: null == catCodigo ? _self.catCodigo : catCodigo // ignore: cast_nullable_to_non_nullable
as String,nombreSustancia: null == nombreSustancia ? _self.nombreSustancia : nombreSustancia // ignore: cast_nullable_to_non_nullable
as String,fabricante: freezed == fabricante ? _self.fabricante : fabricante // ignore: cast_nullable_to_non_nullable
as String?,departamento: null == departamento ? _self.departamento : departamento // ignore: cast_nullable_to_non_nullable
as String,provincia: null == provincia ? _self.provincia : provincia // ignore: cast_nullable_to_non_nullable
as String,distrito: null == distrito ? _self.distrito : distrito // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _AddFavoriteRequest implements AddFavoriteRequest {
  const _AddFavoriteRequest({required this.codEstab, required this.codProdE, required this.fecha, required this.nombreProducto, required this.precio1, required this.precio2, this.precio3, required this.codGrupoFF, required this.ubicodigo, required this.direccion, required this.telefono, required this.nomGrupoFF, required this.setcodigo, required this.nombreComercial, required this.grupo, required this.totalPA, required this.concent, required this.nombreFormaFarmaceutica, required this.fracciones, this.totalRegistros, required this.nombreLaboratorio, required this.nombreTitular, required this.catCodigo, required this.nombreSustancia, this.fabricante, required this.departamento, required this.provincia, required this.distrito});
  factory _AddFavoriteRequest.fromJson(Map<String, dynamic> json) => _$AddFavoriteRequestFromJson(json);

@override final  String codEstab;
@override final  int codProdE;
@override final  String fecha;
@override final  String nombreProducto;
@override final  double precio1;
@override final  double precio2;
@override final  double? precio3;
@override final  String codGrupoFF;
@override final  String ubicodigo;
@override final  String direccion;
@override final  String telefono;
@override final  String nomGrupoFF;
@override final  String setcodigo;
@override final  String nombreComercial;
@override final  String grupo;
@override final  String totalPA;
@override final  String concent;
@override final  String nombreFormaFarmaceutica;
@override final  int fracciones;
@override final  String? totalRegistros;
@override final  String nombreLaboratorio;
@override final  String nombreTitular;
@override final  String catCodigo;
@override final  String nombreSustancia;
@override final  String? fabricante;
@override final  String departamento;
@override final  String provincia;
@override final  String distrito;

/// Create a copy of AddFavoriteRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$AddFavoriteRequestCopyWith<_AddFavoriteRequest> get copyWith => __$AddFavoriteRequestCopyWithImpl<_AddFavoriteRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$AddFavoriteRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _AddFavoriteRequest&&(identical(other.codEstab, codEstab) || other.codEstab == codEstab)&&(identical(other.codProdE, codProdE) || other.codProdE == codProdE)&&(identical(other.fecha, fecha) || other.fecha == fecha)&&(identical(other.nombreProducto, nombreProducto) || other.nombreProducto == nombreProducto)&&(identical(other.precio1, precio1) || other.precio1 == precio1)&&(identical(other.precio2, precio2) || other.precio2 == precio2)&&(identical(other.precio3, precio3) || other.precio3 == precio3)&&(identical(other.codGrupoFF, codGrupoFF) || other.codGrupoFF == codGrupoFF)&&(identical(other.ubicodigo, ubicodigo) || other.ubicodigo == ubicodigo)&&(identical(other.direccion, direccion) || other.direccion == direccion)&&(identical(other.telefono, telefono) || other.telefono == telefono)&&(identical(other.nomGrupoFF, nomGrupoFF) || other.nomGrupoFF == nomGrupoFF)&&(identical(other.setcodigo, setcodigo) || other.setcodigo == setcodigo)&&(identical(other.nombreComercial, nombreComercial) || other.nombreComercial == nombreComercial)&&(identical(other.grupo, grupo) || other.grupo == grupo)&&(identical(other.totalPA, totalPA) || other.totalPA == totalPA)&&(identical(other.concent, concent) || other.concent == concent)&&(identical(other.nombreFormaFarmaceutica, nombreFormaFarmaceutica) || other.nombreFormaFarmaceutica == nombreFormaFarmaceutica)&&(identical(other.fracciones, fracciones) || other.fracciones == fracciones)&&(identical(other.totalRegistros, totalRegistros) || other.totalRegistros == totalRegistros)&&(identical(other.nombreLaboratorio, nombreLaboratorio) || other.nombreLaboratorio == nombreLaboratorio)&&(identical(other.nombreTitular, nombreTitular) || other.nombreTitular == nombreTitular)&&(identical(other.catCodigo, catCodigo) || other.catCodigo == catCodigo)&&(identical(other.nombreSustancia, nombreSustancia) || other.nombreSustancia == nombreSustancia)&&(identical(other.fabricante, fabricante) || other.fabricante == fabricante)&&(identical(other.departamento, departamento) || other.departamento == departamento)&&(identical(other.provincia, provincia) || other.provincia == provincia)&&(identical(other.distrito, distrito) || other.distrito == distrito));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,codEstab,codProdE,fecha,nombreProducto,precio1,precio2,precio3,codGrupoFF,ubicodigo,direccion,telefono,nomGrupoFF,setcodigo,nombreComercial,grupo,totalPA,concent,nombreFormaFarmaceutica,fracciones,totalRegistros,nombreLaboratorio,nombreTitular,catCodigo,nombreSustancia,fabricante,departamento,provincia,distrito]);

@override
String toString() {
  return 'AddFavoriteRequest(codEstab: $codEstab, codProdE: $codProdE, fecha: $fecha, nombreProducto: $nombreProducto, precio1: $precio1, precio2: $precio2, precio3: $precio3, codGrupoFF: $codGrupoFF, ubicodigo: $ubicodigo, direccion: $direccion, telefono: $telefono, nomGrupoFF: $nomGrupoFF, setcodigo: $setcodigo, nombreComercial: $nombreComercial, grupo: $grupo, totalPA: $totalPA, concent: $concent, nombreFormaFarmaceutica: $nombreFormaFarmaceutica, fracciones: $fracciones, totalRegistros: $totalRegistros, nombreLaboratorio: $nombreLaboratorio, nombreTitular: $nombreTitular, catCodigo: $catCodigo, nombreSustancia: $nombreSustancia, fabricante: $fabricante, departamento: $departamento, provincia: $provincia, distrito: $distrito)';
}


}

/// @nodoc
abstract mixin class _$AddFavoriteRequestCopyWith<$Res> implements $AddFavoriteRequestCopyWith<$Res> {
  factory _$AddFavoriteRequestCopyWith(_AddFavoriteRequest value, $Res Function(_AddFavoriteRequest) _then) = __$AddFavoriteRequestCopyWithImpl;
@override @useResult
$Res call({
 String codEstab, int codProdE, String fecha, String nombreProducto, double precio1, double precio2, double? precio3, String codGrupoFF, String ubicodigo, String direccion, String telefono, String nomGrupoFF, String setcodigo, String nombreComercial, String grupo, String totalPA, String concent, String nombreFormaFarmaceutica, int fracciones, String? totalRegistros, String nombreLaboratorio, String nombreTitular, String catCodigo, String nombreSustancia, String? fabricante, String departamento, String provincia, String distrito
});




}
/// @nodoc
class __$AddFavoriteRequestCopyWithImpl<$Res>
    implements _$AddFavoriteRequestCopyWith<$Res> {
  __$AddFavoriteRequestCopyWithImpl(this._self, this._then);

  final _AddFavoriteRequest _self;
  final $Res Function(_AddFavoriteRequest) _then;

/// Create a copy of AddFavoriteRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? codEstab = null,Object? codProdE = null,Object? fecha = null,Object? nombreProducto = null,Object? precio1 = null,Object? precio2 = null,Object? precio3 = freezed,Object? codGrupoFF = null,Object? ubicodigo = null,Object? direccion = null,Object? telefono = null,Object? nomGrupoFF = null,Object? setcodigo = null,Object? nombreComercial = null,Object? grupo = null,Object? totalPA = null,Object? concent = null,Object? nombreFormaFarmaceutica = null,Object? fracciones = null,Object? totalRegistros = freezed,Object? nombreLaboratorio = null,Object? nombreTitular = null,Object? catCodigo = null,Object? nombreSustancia = null,Object? fabricante = freezed,Object? departamento = null,Object? provincia = null,Object? distrito = null,}) {
  return _then(_AddFavoriteRequest(
codEstab: null == codEstab ? _self.codEstab : codEstab // ignore: cast_nullable_to_non_nullable
as String,codProdE: null == codProdE ? _self.codProdE : codProdE // ignore: cast_nullable_to_non_nullable
as int,fecha: null == fecha ? _self.fecha : fecha // ignore: cast_nullable_to_non_nullable
as String,nombreProducto: null == nombreProducto ? _self.nombreProducto : nombreProducto // ignore: cast_nullable_to_non_nullable
as String,precio1: null == precio1 ? _self.precio1 : precio1 // ignore: cast_nullable_to_non_nullable
as double,precio2: null == precio2 ? _self.precio2 : precio2 // ignore: cast_nullable_to_non_nullable
as double,precio3: freezed == precio3 ? _self.precio3 : precio3 // ignore: cast_nullable_to_non_nullable
as double?,codGrupoFF: null == codGrupoFF ? _self.codGrupoFF : codGrupoFF // ignore: cast_nullable_to_non_nullable
as String,ubicodigo: null == ubicodigo ? _self.ubicodigo : ubicodigo // ignore: cast_nullable_to_non_nullable
as String,direccion: null == direccion ? _self.direccion : direccion // ignore: cast_nullable_to_non_nullable
as String,telefono: null == telefono ? _self.telefono : telefono // ignore: cast_nullable_to_non_nullable
as String,nomGrupoFF: null == nomGrupoFF ? _self.nomGrupoFF : nomGrupoFF // ignore: cast_nullable_to_non_nullable
as String,setcodigo: null == setcodigo ? _self.setcodigo : setcodigo // ignore: cast_nullable_to_non_nullable
as String,nombreComercial: null == nombreComercial ? _self.nombreComercial : nombreComercial // ignore: cast_nullable_to_non_nullable
as String,grupo: null == grupo ? _self.grupo : grupo // ignore: cast_nullable_to_non_nullable
as String,totalPA: null == totalPA ? _self.totalPA : totalPA // ignore: cast_nullable_to_non_nullable
as String,concent: null == concent ? _self.concent : concent // ignore: cast_nullable_to_non_nullable
as String,nombreFormaFarmaceutica: null == nombreFormaFarmaceutica ? _self.nombreFormaFarmaceutica : nombreFormaFarmaceutica // ignore: cast_nullable_to_non_nullable
as String,fracciones: null == fracciones ? _self.fracciones : fracciones // ignore: cast_nullable_to_non_nullable
as int,totalRegistros: freezed == totalRegistros ? _self.totalRegistros : totalRegistros // ignore: cast_nullable_to_non_nullable
as String?,nombreLaboratorio: null == nombreLaboratorio ? _self.nombreLaboratorio : nombreLaboratorio // ignore: cast_nullable_to_non_nullable
as String,nombreTitular: null == nombreTitular ? _self.nombreTitular : nombreTitular // ignore: cast_nullable_to_non_nullable
as String,catCodigo: null == catCodigo ? _self.catCodigo : catCodigo // ignore: cast_nullable_to_non_nullable
as String,nombreSustancia: null == nombreSustancia ? _self.nombreSustancia : nombreSustancia // ignore: cast_nullable_to_non_nullable
as String,fabricante: freezed == fabricante ? _self.fabricante : fabricante // ignore: cast_nullable_to_non_nullable
as String?,departamento: null == departamento ? _self.departamento : departamento // ignore: cast_nullable_to_non_nullable
as String,provincia: null == provincia ? _self.provincia : provincia // ignore: cast_nullable_to_non_nullable
as String,distrito: null == distrito ? _self.distrito : distrito // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$RemoveFavoriteRequest {

 String get codEstab; int get codProdE;
/// Create a copy of RemoveFavoriteRequest
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$RemoveFavoriteRequestCopyWith<RemoveFavoriteRequest> get copyWith => _$RemoveFavoriteRequestCopyWithImpl<RemoveFavoriteRequest>(this as RemoveFavoriteRequest, _$identity);

  /// Serializes this RemoveFavoriteRequest to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is RemoveFavoriteRequest&&(identical(other.codEstab, codEstab) || other.codEstab == codEstab)&&(identical(other.codProdE, codProdE) || other.codProdE == codProdE));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codEstab,codProdE);

@override
String toString() {
  return 'RemoveFavoriteRequest(codEstab: $codEstab, codProdE: $codProdE)';
}


}

/// @nodoc
abstract mixin class $RemoveFavoriteRequestCopyWith<$Res>  {
  factory $RemoveFavoriteRequestCopyWith(RemoveFavoriteRequest value, $Res Function(RemoveFavoriteRequest) _then) = _$RemoveFavoriteRequestCopyWithImpl;
@useResult
$Res call({
 String codEstab, int codProdE
});




}
/// @nodoc
class _$RemoveFavoriteRequestCopyWithImpl<$Res>
    implements $RemoveFavoriteRequestCopyWith<$Res> {
  _$RemoveFavoriteRequestCopyWithImpl(this._self, this._then);

  final RemoveFavoriteRequest _self;
  final $Res Function(RemoveFavoriteRequest) _then;

/// Create a copy of RemoveFavoriteRequest
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? codEstab = null,Object? codProdE = null,}) {
  return _then(_self.copyWith(
codEstab: null == codEstab ? _self.codEstab : codEstab // ignore: cast_nullable_to_non_nullable
as String,codProdE: null == codProdE ? _self.codProdE : codProdE // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _RemoveFavoriteRequest implements RemoveFavoriteRequest {
  const _RemoveFavoriteRequest({required this.codEstab, required this.codProdE});
  factory _RemoveFavoriteRequest.fromJson(Map<String, dynamic> json) => _$RemoveFavoriteRequestFromJson(json);

@override final  String codEstab;
@override final  int codProdE;

/// Create a copy of RemoveFavoriteRequest
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$RemoveFavoriteRequestCopyWith<_RemoveFavoriteRequest> get copyWith => __$RemoveFavoriteRequestCopyWithImpl<_RemoveFavoriteRequest>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$RemoveFavoriteRequestToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _RemoveFavoriteRequest&&(identical(other.codEstab, codEstab) || other.codEstab == codEstab)&&(identical(other.codProdE, codProdE) || other.codProdE == codProdE));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codEstab,codProdE);

@override
String toString() {
  return 'RemoveFavoriteRequest(codEstab: $codEstab, codProdE: $codProdE)';
}


}

/// @nodoc
abstract mixin class _$RemoveFavoriteRequestCopyWith<$Res> implements $RemoveFavoriteRequestCopyWith<$Res> {
  factory _$RemoveFavoriteRequestCopyWith(_RemoveFavoriteRequest value, $Res Function(_RemoveFavoriteRequest) _then) = __$RemoveFavoriteRequestCopyWithImpl;
@override @useResult
$Res call({
 String codEstab, int codProdE
});




}
/// @nodoc
class __$RemoveFavoriteRequestCopyWithImpl<$Res>
    implements _$RemoveFavoriteRequestCopyWith<$Res> {
  __$RemoveFavoriteRequestCopyWithImpl(this._self, this._then);

  final _RemoveFavoriteRequest _self;
  final $Res Function(_RemoveFavoriteRequest) _then;

/// Create a copy of RemoveFavoriteRequest
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? codEstab = null,Object? codProdE = null,}) {
  return _then(_RemoveFavoriteRequest(
codEstab: null == codEstab ? _self.codEstab : codEstab // ignore: cast_nullable_to_non_nullable
as String,codProdE: null == codProdE ? _self.codProdE : codProdE // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}

// dart format on

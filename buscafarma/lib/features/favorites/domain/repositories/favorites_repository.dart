import 'package:buscafarma/features/favorites/domain/entities/medication_favorite.dart';
import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';

abstract class FavoritesRepository {
  /// Add a medication to favorites
  Future<MedicationFavorite> addToFavorites(ProductPrice medication);

  /// Remove a medication from favorites by medication identifiers
  Future<bool> removeFromFavorites(String codEstab, int codProdE);

  /// Remove a medication from favorites by favorite ID
  Future<bool> removeFromFavoritesById(String favoriteId);

  /// Get user's favorite medications with pagination
  Future<PaginatedFavorites> getFavorites({int page = 1, int limit = 20});

  /// Search within user's favorite medications
  Future<PaginatedFavorites> searchFavorites({required String query, int page = 1, int limit = 20});

  /// Check if a medication is in favorites
  Future<bool> isFavorite(String codEstab, int codProdE);

  /// Get total count of user's favorites
  Future<int> getFavoritesCount();
}

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:buscafarma/core/utils/api_config.dart';
import 'package:buscafarma/features/auth/data/providers/auth_local_provider.dart';
import 'package:buscafarma/features/favorites/domain/entities/medication_favorite.dart';
import 'package:buscafarma/features/favorites/domain/repositories/favorites_repository.dart';
import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';

class FavoritesRepositoryImpl implements FavoritesRepository {
  final Dio _dio;
  final AuthLocalProvider _authLocal;

  FavoritesRepositoryImpl({required Dio dio, required AuthLocalProvider authLocal}) : _dio = dio, _authLocal = authLocal;

  Future<Map<String, String>> _getAuthHeaders() async {
    final accessToken = await _authLocal.getAccessToken();
    if (accessToken == null) {
      throw Exception('User not authenticated');
    }
    return {'Authorization': 'Bearer $accessToken'};
  }

  @override
  Future<MedicationFavorite> addToFavorites(ProductPrice medication) async {
    try {
      final headers = await _getAuthHeaders();
      final request = AddFavoriteRequest.fromProductPrice(medication);

      final response = await _dio.post('${ApiConfig.baseUrl}/favorites/medications', data: request.toJson(), options: Options(headers: headers));

      if (response.statusCode != 201 && response.statusCode != 200) {
        throw Exception('Failed to add to favorites: ${response.statusCode}');
      }

      if (response.data == null || response.data['data'] == null) {
        throw Exception('Invalid response format');
      }

      return MedicationFavorite.fromJson(response.data['data']);
    } catch (e) {
      if (e is DioException) {
        final dioError = e;
        if (dioError.response?.statusCode == 409) {
          throw Exception('Medication is already in favorites');
        } else if (dioError.response?.statusCode == 401) {
          throw Exception('Authentication failed');
        } else if (dioError.response?.statusCode == 400) {
          throw Exception('Invalid medication data');
        }

        // Try to extract error message from response
        if (dioError.response?.data != null && dioError.response?.data is Map) {
          final data = dioError.response?.data as Map;
          if (data.containsKey('message')) {
            throw Exception(data['message']);
          }
        }
      }
      throw Exception('Error adding to favorites: ${e.toString()}');
    }
  }

  @override
  Future<bool> removeFromFavorites(String codEstab, int codProdE) async {
    try {
      final headers = await _getAuthHeaders();
      final request = RemoveFavoriteRequest(codEstab: codEstab, codProdE: codProdE);

      final response = await _dio.delete('${ApiConfig.baseUrl}/favorites/medications', data: request.toJson(), options: Options(headers: headers));

      if (response.statusCode != 200) {
        throw Exception('Failed to remove from favorites: ${response.statusCode}');
      }

      return true;
    } catch (e) {
      if (e is DioException) {
        final dioError = e;
        if (dioError.response?.statusCode == 404) {
          throw Exception('Medication not found in favorites');
        } else if (dioError.response?.statusCode == 401) {
          throw Exception('Authentication failed');
        }
      }
      throw Exception('Error removing from favorites: ${e.toString()}');
    }
  }

  @override
  Future<bool> removeFromFavoritesById(String favoriteId) async {
    try {
      final headers = await _getAuthHeaders();

      final response = await _dio.delete('${ApiConfig.baseUrl}/favorites/medications/$favoriteId', options: Options(headers: headers));

      if (response.statusCode != 200) {
        throw Exception('Failed to remove from favorites: ${response.statusCode}');
      }

      return true;
    } catch (e) {
      if (e is DioException) {
        final dioError = e;

        // Try to extract specific error message from response
        String errorMessage = 'Error removing from favorites';
        if (dioError.response?.data != null) {
          if (dioError.response?.data is Map) {
            final data = dioError.response?.data as Map;
            if (data.containsKey('message')) {
              errorMessage = data['message'];
            }
          } else if (dioError.response?.data is String) {
            errorMessage = dioError.response?.data;
          }
        }

        if (dioError.response?.statusCode == 404) {
          throw Exception('Favorite not found: $errorMessage');
        } else if (dioError.response?.statusCode == 401) {
          throw Exception('Authentication failed: $errorMessage');
        } else if (dioError.response?.statusCode == 400) {
          throw Exception('Invalid request: $errorMessage');
        }

        throw Exception('$errorMessage (Status: ${dioError.response?.statusCode})');
      }
      throw Exception('Error removing from favorites: ${e.toString()}');
    }
  }

  @override
  Future<PaginatedFavorites> getFavorites({int page = 1, int limit = 20}) async {
    try {
      final headers = await _getAuthHeaders();

      final response = await _dio.get('${ApiConfig.baseUrl}/favorites/medications', queryParameters: {'page': page.toString(), 'limit': limit.toString()}, options: Options(headers: headers));

      if (response.statusCode != 200) {
        throw Exception('Failed to get favorites: ${response.statusCode}');
      }

      if (response.data == null || response.data['data'] == null) {
        throw Exception('Invalid response format');
      }

      final data = response.data['data'];
      return PaginatedFavorites.fromJson(data);
    } catch (e) {
      if (e is DioException) {
        final dioError = e;
        if (dioError.response?.statusCode == 401) {
          throw Exception('Authentication failed');
        }
      }
      throw Exception('Error getting favorites: ${e.toString()}');
    }
  }

  @override
  Future<PaginatedFavorites> searchFavorites({required String query, int page = 1, int limit = 20}) async {
    try {
      final headers = await _getAuthHeaders();

      final response = await _dio.get('${ApiConfig.baseUrl}/favorites/medications/search', queryParameters: {'q': query, 'page': page.toString(), 'limit': limit.toString()}, options: Options(headers: headers));

      if (response.statusCode != 200) {
        throw Exception('Failed to search favorites: ${response.statusCode}');
      }

      if (response.data == null || response.data['data'] == null) {
        throw Exception('Invalid response format');
      }

      final data = response.data['data'];
      return PaginatedFavorites.fromJson(data);
    } catch (e) {
      if (e is DioException) {
        final dioError = e;
        if (dioError.response?.statusCode == 401) {
          throw Exception('Authentication failed');
        } else if (dioError.response?.statusCode == 400) {
          throw Exception('Invalid search query');
        }
      }
      throw Exception('Error searching favorites: ${e.toString()}');
    }
  }

  @override
  Future<bool> isFavorite(String codEstab, int codProdE) async {
    try {
      // For now, we'll implement this by checking if the medication exists in the first page
      // In a real implementation, you might want a dedicated endpoint for this
      final favorites = await getFavorites(page: 1, limit: 100);
      return favorites.favorites.any((fav) => fav.codEstab == codEstab && fav.codProdE == codProdE);
    } catch (e) {
      debugPrint('Error checking if medication is favorite: $e');
      return false;
    }
  }

  @override
  Future<int> getFavoritesCount() async {
    try {
      final favorites = await getFavorites(page: 1, limit: 1);
      return favorites.pagination.total;
    } catch (e) {
      debugPrint('Error getting favorites count: $e');
      return 0;
    }
  }
}

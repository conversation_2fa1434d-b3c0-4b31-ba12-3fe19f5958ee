import 'package:buscafarma/features/favorites/favorites.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'package:buscafarma/core/widgets/app_logo_header.dart';
import 'package:buscafarma/features/map/presentation/widgets/establishment_details_sheet.dart';

class FavoritesPage extends ConsumerStatefulWidget {
  const FavoritesPage({super.key});

  @override
  ConsumerState<FavoritesPage> createState() => _FavoritesPageState();
}

class _FavoritesPageState extends ConsumerState<FavoritesPage> {
  final TextEditingController _searchController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  bool _isSearching = false;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);

    // Load favorites when page is first opened and clear any errors
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final favoritesNotifier = ref.read(favoritesProvider.notifier);
      favoritesNotifier.clearError();
      favoritesNotifier.loadFavorites(refresh: true);
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >= _scrollController.position.maxScrollExtent - 200) {
      // Load more when near bottom
      final state = ref.read(favoritesProvider);
      if (!state.isLoadingMore && state.pagination?.hasNext == true) {
        if (_isSearching && _searchController.text.isNotEmpty) {
          ref.read(favoritesProvider.notifier).searchFavorites(_searchController.text);
        } else {
          ref.read(favoritesProvider.notifier).loadFavorites();
        }
      }
    }
  }

  void _onSearchChanged(String query) {
    // Cancel the previous timer
    if (_debounceTimer?.isActive ?? false) {
      _debounceTimer!.cancel();
    }

    // Create a new timer with 500ms delay
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      final trimmedQuery = query.trim();
      if (trimmedQuery.isEmpty) {
        setState(() => _isSearching = false);
        ref.read(favoritesProvider.notifier).clearSearch();
      } else if (trimmedQuery.length >= 2) {
        // Only search if query has at least 2 characters
        setState(() => _isSearching = true);
        ref.read(favoritesProvider.notifier).searchFavorites(trimmedQuery, refresh: true);
      } else {
        // If less than 2 characters, clear search but don't set searching state
        setState(() => _isSearching = false);
        ref.read(favoritesProvider.notifier).clearSearch();
      }
    });
  }

  void _onRefresh() {
    final favoritesNotifier = ref.read(favoritesProvider.notifier);
    // Clear errors before refreshing
    favoritesNotifier.clearError();

    if (_isSearching && _searchController.text.isNotEmpty) {
      favoritesNotifier.searchFavorites(_searchController.text, refresh: true);
    } else {
      favoritesNotifier.loadFavorites(refresh: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final favoritesState = ref.watch(favoritesProvider);

    return Scaffold(
      appBar: AppBar(
        leading: const AppLogoHeader.compact(),
        title: const Text('Mis Favoritos'),
        backgroundColor: theme.colorScheme.surface,
        elevation: 0,
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(60),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: SearchBarTheme(
              data: SearchBarThemeData(elevation: WidgetStateProperty.all(1.0)),
              child: SearchBar(
                controller: _searchController,
                hintText: 'Buscar en favoritos...',
                leading: const Icon(Icons.search),
                trailing:
                    _searchController.text.isNotEmpty
                        ? [
                          IconButton(
                            onPressed: () {
                              _searchController.clear();
                              _onSearchChanged(''); // Manually trigger search clear
                            },
                            icon: const Icon(Icons.clear),
                          ),
                        ]
                        : null,
                onChanged: _onSearchChanged,
              ),
            ),
          ),
        ),
      ),
      body: RefreshIndicator(onRefresh: () async => _onRefresh(), child: _buildBody(context, favoritesState)),
    );
  }

  Widget _buildBody(BuildContext context, FavoritesState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.errorMessage != null) {
      return _buildErrorState(context, state.errorMessage!);
    }

    if (state.favorites.isEmpty) {
      return _buildEmptyState(context);
    }

    return _buildFavoritesList(context, state);
  }

  Widget _buildErrorState(BuildContext context, String error) {
    final theme = Theme.of(context);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 64, color: theme.colorScheme.error),
            const SizedBox(height: 16),
            Text('Error al cargar favoritos', style: theme.textTheme.headlineSmall, textAlign: TextAlign.center),
            const SizedBox(height: 8),
            Text(error, style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant), textAlign: TextAlign.center),
            const SizedBox(height: 24),
            ElevatedButton(onPressed: _onRefresh, child: const Text('Reintentar')),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    final theme = Theme.of(context);
    final isSearching = _isSearching && _searchController.text.isNotEmpty;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(isSearching ? Icons.search_off : Icons.favorite_border, size: 64, color: theme.colorScheme.onSurfaceVariant),
            const SizedBox(height: 16),
            Text(isSearching ? 'No se encontraron resultados' : 'No tienes medicamentos favoritos', style: theme.textTheme.headlineSmall, textAlign: TextAlign.center),
            const SizedBox(height: 8),
            Text(
              isSearching ? 'Intenta con otros términos de búsqueda' : 'Agrega medicamentos a favoritos desde el mapa para verlos aquí',
              style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant),
              textAlign: TextAlign.center,
            ),
            if (isSearching) ...[
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () {
                  _searchController.clear();
                  _onSearchChanged(''); // Manually trigger search clear
                },
                child: const Text('Limpiar búsqueda'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildFavoritesList(BuildContext context, FavoritesState state) {
    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.only(top: 8, bottom: 100), // Back to original padding since banner is now in MainNavigationPage
      itemCount: state.favorites.length + (state.isLoadingMore ? 1 : 0),
      itemBuilder: (context, index) {
        if (index >= state.favorites.length) {
          // Loading indicator at bottom
          return const Padding(padding: EdgeInsets.all(16), child: Center(child: CircularProgressIndicator()));
        }

        final favorite = state.favorites[index];
        return MedicationCard(favorite: favorite, onTap: () => _showMedicationDetails(context, favorite));
      },
    );
  }

  void _showMedicationDetails(BuildContext context, MedicationFavorite favorite) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => Container(
            constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.9),
            margin: EdgeInsets.only(top: MediaQuery.of(context).padding.top + 20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Flexible(
                  child: Container(
                    decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface, borderRadius: const BorderRadius.vertical(top: Radius.circular(20))),
                    child: ClipRRect(
                      borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          // Drag handle
                          Padding(
                            padding: const EdgeInsets.only(top: 12.0, bottom: 8.0),
                            child: Container(height: 5, width: 40, decoration: BoxDecoration(color: Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(102), borderRadius: BorderRadius.circular(2.5))),
                          ),
                          // Content
                          Flexible(
                            child: SingleChildScrollView(
                              physics: const AlwaysScrollableScrollPhysics(),
                              padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom + 16),
                              child: EstablishmentDetailsSheet(productPrice: favorite.medicationData, isFavoriteContext: true, address: favorite.medicationData.direccion),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
    );
  }
}

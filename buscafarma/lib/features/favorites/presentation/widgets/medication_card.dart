import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/favorites/domain/entities/medication_favorite.dart';
import 'package:buscafarma/features/favorites/presentation/providers/favorites_provider.dart';

class MedicationCard extends ConsumerWidget {
  final MedicationFavorite favorite;
  final VoidCallback? onTap;
  final bool showRemoveButton;

  const MedicationCard({super.key, required this.favorite, this.onTap, this.showRemoveButton = true});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final medication = favorite.medicationData;

    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with medication name and remove button
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(medication.nombreProducto ?? 'Medicamento', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold), maxLines: 2, overflow: TextOverflow.ellipsis),
                        const SizedBox(height: 4),
                        if (medication.concent.isNotEmpty) Text(medication.concent, style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.primary, fontWeight: FontWeight.w500)),
                      ],
                    ),
                  ),
                  if (showRemoveButton) ...[const SizedBox(width: 8), IconButton(onPressed: () => _removeFromFavorites(context, ref), icon: Icon(Icons.favorite, color: theme.colorScheme.error), tooltip: 'Quitar de favoritos')],
                ],
              ),

              const SizedBox(height: 12),

              // Establishment info
              Row(
                children: [
                  Icon(Icons.store, size: 16, color: theme.colorScheme.onSurfaceVariant),
                  const SizedBox(width: 8),
                  Expanded(child: Text(medication.nombreComercial ?? 'Establecimiento', style: theme.textTheme.bodyMedium?.copyWith(color: theme.colorScheme.onSurfaceVariant), maxLines: 1, overflow: TextOverflow.ellipsis)),
                ],
              ),

              const SizedBox(height: 8),

              // Location info
              Row(
                children: [
                  Icon(Icons.location_on, size: 16, color: theme.colorScheme.onSurfaceVariant),
                  const SizedBox(width: 8),
                  Expanded(child: Text('${medication.distrito}, ${medication.provincia}', style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant), maxLines: 1, overflow: TextOverflow.ellipsis)),
                ],
              ),

              const SizedBox(height: 12),

              // Price and form info
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Price
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(color: theme.colorScheme.primaryContainer, borderRadius: BorderRadius.circular(16)),
                    child: Text('S/ ${medication.precio1.toStringAsFixed(2)}', style: theme.textTheme.titleSmall?.copyWith(color: theme.colorScheme.onPrimaryContainer, fontWeight: FontWeight.bold)),
                  ),

                  // Pharmaceutical form
                  if (medication.nombreFormaFarmaceutica.isNotEmpty)
                    Container(
                      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                      decoration: BoxDecoration(color: theme.colorScheme.surfaceContainerHighest, borderRadius: BorderRadius.circular(16)),
                      child: Text(medication.nombreFormaFarmaceutica, style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
                    ),
                ],
              ),

              // Laboratory info
              if (medication.nombreLaboratorio?.isNotEmpty == true) ...[
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.science, size: 14, color: theme.colorScheme.onSurfaceVariant),
                    const SizedBox(width: 6),
                    Expanded(child: Text(medication.nombreLaboratorio!, style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant), maxLines: 1, overflow: TextOverflow.ellipsis)),
                  ],
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _removeFromFavorites(BuildContext context, WidgetRef ref) async {
    final favoritesNotifier = ref.read(favoritesProvider.notifier);

    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Quitar de favoritos'),
            content: Text('¿Estás seguro de que quieres quitar "${favorite.medicationData.nombreProducto}" de tus favoritos?'),
            actions: [TextButton(onPressed: () => Navigator.of(context).pop(false), child: const Text('Cancelar')), TextButton(onPressed: () => Navigator.of(context).pop(true), child: const Text('Quitar'))],
          ),
    );

    if (confirmed == true) {
      final success = await favoritesNotifier.removeFromFavoritesById(favorite.id);

      if (context.mounted) {
        if (success) {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Medicamento quitado de favoritos'), backgroundColor: Colors.green));
        } else {
          ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Error al quitar de favoritos'), backgroundColor: Colors.red));
        }
      }
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/providers/dio_provider.dart';
import 'package:buscafarma/features/favorites/data/repositories/favorites_repository_impl.dart';
import 'package:buscafarma/features/favorites/domain/entities/medication_favorite.dart';
import 'package:buscafarma/features/favorites/domain/repositories/favorites_repository.dart';
import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';
import 'package:buscafarma/features/review/presentation/providers/review_manager_provider.dart';

// Repository provider
final favoritesRepositoryProvider = Provider<FavoritesRepository>((ref) {
  final dio = ref.watch(dioProvider);
  final authLocal = ref.watch(authLocalProvider);
  return FavoritesRepositoryImpl(dio: dio, authLocal: authLocal);
});

// Favorites state model
class FavoritesState {
  final List<MedicationFavorite> favorites;
  final FavoritesPagination? pagination;
  final bool isLoading;
  final bool isLoadingMore;
  final String? errorMessage;
  final String? searchQuery;
  final Set<String> favoriteKeys; // For quick lookup: "codEstab-codProdE"

  FavoritesState({this.favorites = const [], this.pagination, this.isLoading = false, this.isLoadingMore = false, this.errorMessage, this.searchQuery, this.favoriteKeys = const {}});

  FavoritesState copyWith({List<MedicationFavorite>? favorites, FavoritesPagination? pagination, bool? isLoading, bool? isLoadingMore, String? errorMessage, String? searchQuery, Set<String>? favoriteKeys}) {
    return FavoritesState(
      favorites: favorites ?? this.favorites,
      pagination: pagination ?? this.pagination,
      isLoading: isLoading ?? this.isLoading,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      errorMessage: errorMessage,
      searchQuery: searchQuery ?? this.searchQuery,
      favoriteKeys: favoriteKeys ?? this.favoriteKeys,
    );
  }

  // Helper method to generate favorite key
  static String _generateFavoriteKey(String codEstab, int codProdE) {
    return '$codEstab-$codProdE';
  }

  // Check if a medication is in favorites
  bool isFavorite(String codEstab, int codProdE) {
    return favoriteKeys.contains(_generateFavoriteKey(codEstab, codProdE));
  }

  // Update favorite keys from favorites list
  FavoritesState updateFavoriteKeys() {
    final keys = favorites.map((fav) => _generateFavoriteKey(fav.codEstab, fav.codProdE)).toSet();
    return copyWith(favoriteKeys: keys);
  }
}

// Favorites notifier
class FavoritesNotifier extends StateNotifier<FavoritesState> {
  final FavoritesRepository _repository;
  final Ref _ref;

  FavoritesNotifier(this._repository, this._ref) : super(FavoritesState());

  // Load favorites with pagination
  Future<void> loadFavorites({bool refresh = false}) async {
    if (refresh) {
      state = state.copyWith(isLoading: true, errorMessage: null, searchQuery: null);
    } else if (state.pagination?.hasNext != true) {
      return; // No more pages to load
    } else {
      state = state.copyWith(isLoadingMore: true, errorMessage: null);
    }

    try {
      final page = refresh ? 1 : (state.pagination?.page ?? 0) + 1;
      final result = await _repository.getFavorites(page: page, limit: 20);

      if (refresh) {
        state = state.copyWith(favorites: result.favorites, pagination: result.pagination, isLoading: false, searchQuery: null).updateFavoriteKeys();
      } else {
        state = state.copyWith(favorites: [...state.favorites, ...result.favorites], pagination: result.pagination, isLoadingMore: false).updateFavoriteKeys();
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, isLoadingMore: false, errorMessage: e.toString());
    }
  }

  // Search favorites
  Future<void> searchFavorites(String query, {bool refresh = false}) async {
    if (query.trim().isEmpty) {
      await loadFavorites(refresh: true);
      return;
    }

    if (refresh) {
      state = state.copyWith(isLoading: true, errorMessage: null, searchQuery: query);
    } else if (state.pagination?.hasNext != true) {
      return;
    } else {
      state = state.copyWith(isLoadingMore: true, errorMessage: null);
    }

    try {
      final page = refresh ? 1 : (state.pagination?.page ?? 0) + 1;
      final result = await _repository.searchFavorites(query: query, page: page, limit: 20);

      if (refresh) {
        state = state.copyWith(favorites: result.favorites, pagination: result.pagination, isLoading: false, searchQuery: query).updateFavoriteKeys();
      } else {
        state = state.copyWith(favorites: [...state.favorites, ...result.favorites], pagination: result.pagination, isLoadingMore: false, searchQuery: query).updateFavoriteKeys();
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, isLoadingMore: false, errorMessage: e.toString());
    }
  }

  // Add medication to favorites
  Future<bool> addToFavorites(ProductPrice medication) async {
    try {
      final favorite = await _repository.addToFavorites(medication);

      // Clear any existing errors on success
      // Add to current list if not searching
      if (state.searchQuery == null || state.searchQuery!.isEmpty) {
        state = state.copyWith(favorites: [favorite, ...state.favorites], errorMessage: null).updateFavoriteKeys();
      } else {
        // Just update the keys for quick lookup
        final newKeys = Set<String>.from(state.favoriteKeys);
        newKeys.add(FavoritesState._generateFavoriteKey(medication.codEstab, medication.codProdE));
        state = state.copyWith(favoriteKeys: newKeys, errorMessage: null);
      }

      // Record favorite addition for review tracking
      try {
        final reviewManager = _ref.read(reviewManagerProvider.notifier);
        await reviewManager.recordFavoriteAdded(metadata: {'medication_name': medication.nombreProducto, 'establishment_code': medication.codEstab, 'product_code': medication.codProdE.toString(), 'timestamp': DateTime.now().toIso8601String()});
      } catch (e) {
        // Don't fail the favorite addition if review tracking fails
        debugPrint('Failed to record favorite addition interaction for review: $e');
      }

      return true;
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
      return false;
    }
  }

  // Remove medication from favorites
  Future<bool> removeFromFavorites(String codEstab, int codProdE) async {
    try {
      await _repository.removeFromFavorites(codEstab, codProdE);

      // Remove from current list and clear errors on success
      final updatedFavorites = state.favorites.where((fav) => !(fav.codEstab == codEstab && fav.codProdE == codProdE)).toList();

      state = state.copyWith(favorites: updatedFavorites, errorMessage: null).updateFavoriteKeys();
      return true;
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
      return false;
    }
  }

  // Remove medication from favorites by ID
  Future<bool> removeFromFavoritesById(String favoriteId) async {
    try {
      await _repository.removeFromFavoritesById(favoriteId);

      // Remove from current list and clear errors on success
      final updatedFavorites = state.favorites.where((fav) => fav.id != favoriteId).toList();

      state = state.copyWith(favorites: updatedFavorites, errorMessage: null).updateFavoriteKeys();
      return true;
    } catch (e) {
      state = state.copyWith(errorMessage: e.toString());
      return false;
    }
  }

  // Check if medication is favorite (from state)
  bool isFavorite(String codEstab, int codProdE) {
    return state.isFavorite(codEstab, codProdE);
  }

  // Clear error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  // Clear search
  void clearSearch() {
    loadFavorites(refresh: true);
  }
}

// Favorites provider
final favoritesProvider = StateNotifierProvider<FavoritesNotifier, FavoritesState>((ref) {
  final repository = ref.watch(favoritesRepositoryProvider);
  return FavoritesNotifier(repository, ref);
});

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'user_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UserEntity _$UserEntityFromJson(Map<String, dynamic> json) => _UserEntity(
  id: json['id'] as String,
  name: json['name'] as String,
  email: json['email'] as String,
  profilePictureUrl: json['profilePictureUrl'] as String?,
  accessToken: json['accessToken'] as String?,
  refreshToken: json['refreshToken'] as String?,
  preferences:
      json['preferences'] == null
          ? null
          : UserPreferences.fromJson(
            json['preferences'] as Map<String, dynamic>,
          ),
  healthData:
      json['healthData'] == null
          ? null
          : HealthData.fromJson(json['healthData'] as Map<String, dynamic>),
);

Map<String, dynamic> _$UserEntityToJson(_UserEntity instance) =>
    <String, dynamic>{
      'id': instance.id,
      'name': instance.name,
      'email': instance.email,
      'profilePictureUrl': instance.profilePictureUrl,
      'accessToken': instance.accessToken,
      'refreshToken': instance.refreshToken,
      'preferences': instance.preferences,
      'healthData': instance.healthData,
    };

_UserPreferences _$UserPreferencesFromJson(Map<String, dynamic> json) =>
    _UserPreferences(
      theme:
          $enumDecodeNullable(_$AppThemeModeEnumMap, json['theme']) ??
          AppThemeMode.system,
      notifications: json['notifications'] as bool? ?? true,
      defaultLocation:
          json['defaultLocation'] == null
              ? null
              : LocationEntity.fromJson(
                json['defaultLocation'] as Map<String, dynamic>,
              ),
      language: json['language'] as String? ?? "es",
    );

Map<String, dynamic> _$UserPreferencesToJson(_UserPreferences instance) =>
    <String, dynamic>{
      'theme': _$AppThemeModeEnumMap[instance.theme]!,
      'notifications': instance.notifications,
      'defaultLocation': instance.defaultLocation,
      'language': instance.language,
    };

const _$AppThemeModeEnumMap = {
  AppThemeMode.system: 'system',
  AppThemeMode.light: 'light',
  AppThemeMode.dark: 'dark',
};

_HealthData _$HealthDataFromJson(Map<String, dynamic> json) => _HealthData(
  allergies:
      (json['allergies'] as List<dynamic>?)?.map((e) => e as String).toList() ??
      const [],
  conditions:
      (json['conditions'] as List<dynamic>?)
          ?.map((e) => e as String)
          .toList() ??
      const [],
  medications:
      (json['medications'] as List<dynamic>?)
          ?.map((e) => Medication.fromJson(e as Map<String, dynamic>))
          .toList() ??
      const [],
);

Map<String, dynamic> _$HealthDataToJson(_HealthData instance) =>
    <String, dynamic>{
      'allergies': instance.allergies,
      'conditions': instance.conditions,
      'medications': instance.medications,
    };

_Medication _$MedicationFromJson(Map<String, dynamic> json) => _Medication(
  name: json['name'] as String,
  dosage: json['dosage'] as String?,
  frequency: json['frequency'] as String?,
);

Map<String, dynamic> _$MedicationToJson(_Medication instance) =>
    <String, dynamic>{
      'name': instance.name,
      'dosage': instance.dosage,
      'frequency': instance.frequency,
    };

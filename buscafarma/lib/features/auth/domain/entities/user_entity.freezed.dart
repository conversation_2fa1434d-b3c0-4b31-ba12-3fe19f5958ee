// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'user_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UserEntity {

 String get id; String get name; String get email; String? get profilePictureUrl; String? get accessToken; String? get refreshToken; UserPreferences? get preferences; HealthData? get healthData;
/// Create a copy of UserEntity
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserEntityCopyWith<UserEntity> get copyWith => _$UserEntityCopyWithImpl<UserEntity>(this as UserEntity, _$identity);

  /// Serializes this UserEntity to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.preferences, preferences) || other.preferences == preferences)&&(identical(other.healthData, healthData) || other.healthData == healthData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,email,profilePictureUrl,accessToken,refreshToken,preferences,healthData);

@override
String toString() {
  return 'UserEntity(id: $id, name: $name, email: $email, profilePictureUrl: $profilePictureUrl, accessToken: $accessToken, refreshToken: $refreshToken, preferences: $preferences, healthData: $healthData)';
}


}

/// @nodoc
abstract mixin class $UserEntityCopyWith<$Res>  {
  factory $UserEntityCopyWith(UserEntity value, $Res Function(UserEntity) _then) = _$UserEntityCopyWithImpl;
@useResult
$Res call({
 String id, String name, String email, String? profilePictureUrl, String? accessToken, String? refreshToken, UserPreferences? preferences, HealthData? healthData
});


$UserPreferencesCopyWith<$Res>? get preferences;$HealthDataCopyWith<$Res>? get healthData;

}
/// @nodoc
class _$UserEntityCopyWithImpl<$Res>
    implements $UserEntityCopyWith<$Res> {
  _$UserEntityCopyWithImpl(this._self, this._then);

  final UserEntity _self;
  final $Res Function(UserEntity) _then;

/// Create a copy of UserEntity
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? id = null,Object? name = null,Object? email = null,Object? profilePictureUrl = freezed,Object? accessToken = freezed,Object? refreshToken = freezed,Object? preferences = freezed,Object? healthData = freezed,}) {
  return _then(_self.copyWith(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,profilePictureUrl: freezed == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String?,accessToken: freezed == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String?,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,preferences: freezed == preferences ? _self.preferences : preferences // ignore: cast_nullable_to_non_nullable
as UserPreferences?,healthData: freezed == healthData ? _self.healthData : healthData // ignore: cast_nullable_to_non_nullable
as HealthData?,
  ));
}
/// Create a copy of UserEntity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserPreferencesCopyWith<$Res>? get preferences {
    if (_self.preferences == null) {
    return null;
  }

  return $UserPreferencesCopyWith<$Res>(_self.preferences!, (value) {
    return _then(_self.copyWith(preferences: value));
  });
}/// Create a copy of UserEntity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HealthDataCopyWith<$Res>? get healthData {
    if (_self.healthData == null) {
    return null;
  }

  return $HealthDataCopyWith<$Res>(_self.healthData!, (value) {
    return _then(_self.copyWith(healthData: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _UserEntity implements UserEntity {
  const _UserEntity({required this.id, required this.name, required this.email, this.profilePictureUrl, this.accessToken, this.refreshToken, this.preferences, this.healthData});
  factory _UserEntity.fromJson(Map<String, dynamic> json) => _$UserEntityFromJson(json);

@override final  String id;
@override final  String name;
@override final  String email;
@override final  String? profilePictureUrl;
@override final  String? accessToken;
@override final  String? refreshToken;
@override final  UserPreferences? preferences;
@override final  HealthData? healthData;

/// Create a copy of UserEntity
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserEntityCopyWith<_UserEntity> get copyWith => __$UserEntityCopyWithImpl<_UserEntity>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserEntityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserEntity&&(identical(other.id, id) || other.id == id)&&(identical(other.name, name) || other.name == name)&&(identical(other.email, email) || other.email == email)&&(identical(other.profilePictureUrl, profilePictureUrl) || other.profilePictureUrl == profilePictureUrl)&&(identical(other.accessToken, accessToken) || other.accessToken == accessToken)&&(identical(other.refreshToken, refreshToken) || other.refreshToken == refreshToken)&&(identical(other.preferences, preferences) || other.preferences == preferences)&&(identical(other.healthData, healthData) || other.healthData == healthData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,id,name,email,profilePictureUrl,accessToken,refreshToken,preferences,healthData);

@override
String toString() {
  return 'UserEntity(id: $id, name: $name, email: $email, profilePictureUrl: $profilePictureUrl, accessToken: $accessToken, refreshToken: $refreshToken, preferences: $preferences, healthData: $healthData)';
}


}

/// @nodoc
abstract mixin class _$UserEntityCopyWith<$Res> implements $UserEntityCopyWith<$Res> {
  factory _$UserEntityCopyWith(_UserEntity value, $Res Function(_UserEntity) _then) = __$UserEntityCopyWithImpl;
@override @useResult
$Res call({
 String id, String name, String email, String? profilePictureUrl, String? accessToken, String? refreshToken, UserPreferences? preferences, HealthData? healthData
});


@override $UserPreferencesCopyWith<$Res>? get preferences;@override $HealthDataCopyWith<$Res>? get healthData;

}
/// @nodoc
class __$UserEntityCopyWithImpl<$Res>
    implements _$UserEntityCopyWith<$Res> {
  __$UserEntityCopyWithImpl(this._self, this._then);

  final _UserEntity _self;
  final $Res Function(_UserEntity) _then;

/// Create a copy of UserEntity
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? id = null,Object? name = null,Object? email = null,Object? profilePictureUrl = freezed,Object? accessToken = freezed,Object? refreshToken = freezed,Object? preferences = freezed,Object? healthData = freezed,}) {
  return _then(_UserEntity(
id: null == id ? _self.id : id // ignore: cast_nullable_to_non_nullable
as String,name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,profilePictureUrl: freezed == profilePictureUrl ? _self.profilePictureUrl : profilePictureUrl // ignore: cast_nullable_to_non_nullable
as String?,accessToken: freezed == accessToken ? _self.accessToken : accessToken // ignore: cast_nullable_to_non_nullable
as String?,refreshToken: freezed == refreshToken ? _self.refreshToken : refreshToken // ignore: cast_nullable_to_non_nullable
as String?,preferences: freezed == preferences ? _self.preferences : preferences // ignore: cast_nullable_to_non_nullable
as UserPreferences?,healthData: freezed == healthData ? _self.healthData : healthData // ignore: cast_nullable_to_non_nullable
as HealthData?,
  ));
}

/// Create a copy of UserEntity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$UserPreferencesCopyWith<$Res>? get preferences {
    if (_self.preferences == null) {
    return null;
  }

  return $UserPreferencesCopyWith<$Res>(_self.preferences!, (value) {
    return _then(_self.copyWith(preferences: value));
  });
}/// Create a copy of UserEntity
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$HealthDataCopyWith<$Res>? get healthData {
    if (_self.healthData == null) {
    return null;
  }

  return $HealthDataCopyWith<$Res>(_self.healthData!, (value) {
    return _then(_self.copyWith(healthData: value));
  });
}
}


/// @nodoc
mixin _$UserPreferences {

 AppThemeMode get theme; bool get notifications; LocationEntity? get defaultLocation; String get language;
/// Create a copy of UserPreferences
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UserPreferencesCopyWith<UserPreferences> get copyWith => _$UserPreferencesCopyWithImpl<UserPreferences>(this as UserPreferences, _$identity);

  /// Serializes this UserPreferences to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UserPreferences&&(identical(other.theme, theme) || other.theme == theme)&&(identical(other.notifications, notifications) || other.notifications == notifications)&&(identical(other.defaultLocation, defaultLocation) || other.defaultLocation == defaultLocation)&&(identical(other.language, language) || other.language == language));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,theme,notifications,defaultLocation,language);

@override
String toString() {
  return 'UserPreferences(theme: $theme, notifications: $notifications, defaultLocation: $defaultLocation, language: $language)';
}


}

/// @nodoc
abstract mixin class $UserPreferencesCopyWith<$Res>  {
  factory $UserPreferencesCopyWith(UserPreferences value, $Res Function(UserPreferences) _then) = _$UserPreferencesCopyWithImpl;
@useResult
$Res call({
 AppThemeMode theme, bool notifications, LocationEntity? defaultLocation, String language
});


$LocationEntityCopyWith<$Res>? get defaultLocation;

}
/// @nodoc
class _$UserPreferencesCopyWithImpl<$Res>
    implements $UserPreferencesCopyWith<$Res> {
  _$UserPreferencesCopyWithImpl(this._self, this._then);

  final UserPreferences _self;
  final $Res Function(UserPreferences) _then;

/// Create a copy of UserPreferences
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? theme = null,Object? notifications = null,Object? defaultLocation = freezed,Object? language = null,}) {
  return _then(_self.copyWith(
theme: null == theme ? _self.theme : theme // ignore: cast_nullable_to_non_nullable
as AppThemeMode,notifications: null == notifications ? _self.notifications : notifications // ignore: cast_nullable_to_non_nullable
as bool,defaultLocation: freezed == defaultLocation ? _self.defaultLocation : defaultLocation // ignore: cast_nullable_to_non_nullable
as LocationEntity?,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,
  ));
}
/// Create a copy of UserPreferences
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LocationEntityCopyWith<$Res>? get defaultLocation {
    if (_self.defaultLocation == null) {
    return null;
  }

  return $LocationEntityCopyWith<$Res>(_self.defaultLocation!, (value) {
    return _then(_self.copyWith(defaultLocation: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _UserPreferences implements UserPreferences {
  const _UserPreferences({this.theme = AppThemeMode.system, this.notifications = true, this.defaultLocation, this.language = "es"});
  factory _UserPreferences.fromJson(Map<String, dynamic> json) => _$UserPreferencesFromJson(json);

@override@JsonKey() final  AppThemeMode theme;
@override@JsonKey() final  bool notifications;
@override final  LocationEntity? defaultLocation;
@override@JsonKey() final  String language;

/// Create a copy of UserPreferences
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UserPreferencesCopyWith<_UserPreferences> get copyWith => __$UserPreferencesCopyWithImpl<_UserPreferences>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UserPreferencesToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UserPreferences&&(identical(other.theme, theme) || other.theme == theme)&&(identical(other.notifications, notifications) || other.notifications == notifications)&&(identical(other.defaultLocation, defaultLocation) || other.defaultLocation == defaultLocation)&&(identical(other.language, language) || other.language == language));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,theme,notifications,defaultLocation,language);

@override
String toString() {
  return 'UserPreferences(theme: $theme, notifications: $notifications, defaultLocation: $defaultLocation, language: $language)';
}


}

/// @nodoc
abstract mixin class _$UserPreferencesCopyWith<$Res> implements $UserPreferencesCopyWith<$Res> {
  factory _$UserPreferencesCopyWith(_UserPreferences value, $Res Function(_UserPreferences) _then) = __$UserPreferencesCopyWithImpl;
@override @useResult
$Res call({
 AppThemeMode theme, bool notifications, LocationEntity? defaultLocation, String language
});


@override $LocationEntityCopyWith<$Res>? get defaultLocation;

}
/// @nodoc
class __$UserPreferencesCopyWithImpl<$Res>
    implements _$UserPreferencesCopyWith<$Res> {
  __$UserPreferencesCopyWithImpl(this._self, this._then);

  final _UserPreferences _self;
  final $Res Function(_UserPreferences) _then;

/// Create a copy of UserPreferences
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? theme = null,Object? notifications = null,Object? defaultLocation = freezed,Object? language = null,}) {
  return _then(_UserPreferences(
theme: null == theme ? _self.theme : theme // ignore: cast_nullable_to_non_nullable
as AppThemeMode,notifications: null == notifications ? _self.notifications : notifications // ignore: cast_nullable_to_non_nullable
as bool,defaultLocation: freezed == defaultLocation ? _self.defaultLocation : defaultLocation // ignore: cast_nullable_to_non_nullable
as LocationEntity?,language: null == language ? _self.language : language // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

/// Create a copy of UserPreferences
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$LocationEntityCopyWith<$Res>? get defaultLocation {
    if (_self.defaultLocation == null) {
    return null;
  }

  return $LocationEntityCopyWith<$Res>(_self.defaultLocation!, (value) {
    return _then(_self.copyWith(defaultLocation: value));
  });
}
}


/// @nodoc
mixin _$HealthData {

 List<String> get allergies; List<String> get conditions; List<Medication> get medications;
/// Create a copy of HealthData
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$HealthDataCopyWith<HealthData> get copyWith => _$HealthDataCopyWithImpl<HealthData>(this as HealthData, _$identity);

  /// Serializes this HealthData to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is HealthData&&const DeepCollectionEquality().equals(other.allergies, allergies)&&const DeepCollectionEquality().equals(other.conditions, conditions)&&const DeepCollectionEquality().equals(other.medications, medications));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(allergies),const DeepCollectionEquality().hash(conditions),const DeepCollectionEquality().hash(medications));

@override
String toString() {
  return 'HealthData(allergies: $allergies, conditions: $conditions, medications: $medications)';
}


}

/// @nodoc
abstract mixin class $HealthDataCopyWith<$Res>  {
  factory $HealthDataCopyWith(HealthData value, $Res Function(HealthData) _then) = _$HealthDataCopyWithImpl;
@useResult
$Res call({
 List<String> allergies, List<String> conditions, List<Medication> medications
});




}
/// @nodoc
class _$HealthDataCopyWithImpl<$Res>
    implements $HealthDataCopyWith<$Res> {
  _$HealthDataCopyWithImpl(this._self, this._then);

  final HealthData _self;
  final $Res Function(HealthData) _then;

/// Create a copy of HealthData
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? allergies = null,Object? conditions = null,Object? medications = null,}) {
  return _then(_self.copyWith(
allergies: null == allergies ? _self.allergies : allergies // ignore: cast_nullable_to_non_nullable
as List<String>,conditions: null == conditions ? _self.conditions : conditions // ignore: cast_nullable_to_non_nullable
as List<String>,medications: null == medications ? _self.medications : medications // ignore: cast_nullable_to_non_nullable
as List<Medication>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _HealthData implements HealthData {
  const _HealthData({final  List<String> allergies = const [], final  List<String> conditions = const [], final  List<Medication> medications = const []}): _allergies = allergies,_conditions = conditions,_medications = medications;
  factory _HealthData.fromJson(Map<String, dynamic> json) => _$HealthDataFromJson(json);

 final  List<String> _allergies;
@override@JsonKey() List<String> get allergies {
  if (_allergies is EqualUnmodifiableListView) return _allergies;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_allergies);
}

 final  List<String> _conditions;
@override@JsonKey() List<String> get conditions {
  if (_conditions is EqualUnmodifiableListView) return _conditions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_conditions);
}

 final  List<Medication> _medications;
@override@JsonKey() List<Medication> get medications {
  if (_medications is EqualUnmodifiableListView) return _medications;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_medications);
}


/// Create a copy of HealthData
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$HealthDataCopyWith<_HealthData> get copyWith => __$HealthDataCopyWithImpl<_HealthData>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$HealthDataToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _HealthData&&const DeepCollectionEquality().equals(other._allergies, _allergies)&&const DeepCollectionEquality().equals(other._conditions, _conditions)&&const DeepCollectionEquality().equals(other._medications, _medications));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,const DeepCollectionEquality().hash(_allergies),const DeepCollectionEquality().hash(_conditions),const DeepCollectionEquality().hash(_medications));

@override
String toString() {
  return 'HealthData(allergies: $allergies, conditions: $conditions, medications: $medications)';
}


}

/// @nodoc
abstract mixin class _$HealthDataCopyWith<$Res> implements $HealthDataCopyWith<$Res> {
  factory _$HealthDataCopyWith(_HealthData value, $Res Function(_HealthData) _then) = __$HealthDataCopyWithImpl;
@override @useResult
$Res call({
 List<String> allergies, List<String> conditions, List<Medication> medications
});




}
/// @nodoc
class __$HealthDataCopyWithImpl<$Res>
    implements _$HealthDataCopyWith<$Res> {
  __$HealthDataCopyWithImpl(this._self, this._then);

  final _HealthData _self;
  final $Res Function(_HealthData) _then;

/// Create a copy of HealthData
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? allergies = null,Object? conditions = null,Object? medications = null,}) {
  return _then(_HealthData(
allergies: null == allergies ? _self._allergies : allergies // ignore: cast_nullable_to_non_nullable
as List<String>,conditions: null == conditions ? _self._conditions : conditions // ignore: cast_nullable_to_non_nullable
as List<String>,medications: null == medications ? _self._medications : medications // ignore: cast_nullable_to_non_nullable
as List<Medication>,
  ));
}


}


/// @nodoc
mixin _$Medication {

 String get name; String? get dosage; String? get frequency;
/// Create a copy of Medication
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MedicationCopyWith<Medication> get copyWith => _$MedicationCopyWithImpl<Medication>(this as Medication, _$identity);

  /// Serializes this Medication to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Medication&&(identical(other.name, name) || other.name == name)&&(identical(other.dosage, dosage) || other.dosage == dosage)&&(identical(other.frequency, frequency) || other.frequency == frequency));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,dosage,frequency);

@override
String toString() {
  return 'Medication(name: $name, dosage: $dosage, frequency: $frequency)';
}


}

/// @nodoc
abstract mixin class $MedicationCopyWith<$Res>  {
  factory $MedicationCopyWith(Medication value, $Res Function(Medication) _then) = _$MedicationCopyWithImpl;
@useResult
$Res call({
 String name, String? dosage, String? frequency
});




}
/// @nodoc
class _$MedicationCopyWithImpl<$Res>
    implements $MedicationCopyWith<$Res> {
  _$MedicationCopyWithImpl(this._self, this._then);

  final Medication _self;
  final $Res Function(Medication) _then;

/// Create a copy of Medication
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? name = null,Object? dosage = freezed,Object? frequency = freezed,}) {
  return _then(_self.copyWith(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,dosage: freezed == dosage ? _self.dosage : dosage // ignore: cast_nullable_to_non_nullable
as String?,frequency: freezed == frequency ? _self.frequency : frequency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Medication implements Medication {
  const _Medication({required this.name, this.dosage, this.frequency});
  factory _Medication.fromJson(Map<String, dynamic> json) => _$MedicationFromJson(json);

@override final  String name;
@override final  String? dosage;
@override final  String? frequency;

/// Create a copy of Medication
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MedicationCopyWith<_Medication> get copyWith => __$MedicationCopyWithImpl<_Medication>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$MedicationToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Medication&&(identical(other.name, name) || other.name == name)&&(identical(other.dosage, dosage) || other.dosage == dosage)&&(identical(other.frequency, frequency) || other.frequency == frequency));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,name,dosage,frequency);

@override
String toString() {
  return 'Medication(name: $name, dosage: $dosage, frequency: $frequency)';
}


}

/// @nodoc
abstract mixin class _$MedicationCopyWith<$Res> implements $MedicationCopyWith<$Res> {
  factory _$MedicationCopyWith(_Medication value, $Res Function(_Medication) _then) = __$MedicationCopyWithImpl;
@override @useResult
$Res call({
 String name, String? dosage, String? frequency
});




}
/// @nodoc
class __$MedicationCopyWithImpl<$Res>
    implements _$MedicationCopyWith<$Res> {
  __$MedicationCopyWithImpl(this._self, this._then);

  final _Medication _self;
  final $Res Function(_Medication) _then;

/// Create a copy of Medication
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? name = null,Object? dosage = freezed,Object? frequency = freezed,}) {
  return _then(_Medication(
name: null == name ? _self.name : name // ignore: cast_nullable_to_non_nullable
as String,dosage: freezed == dosage ? _self.dosage : dosage // ignore: cast_nullable_to_non_nullable
as String?,frequency: freezed == frequency ? _self.frequency : frequency // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on

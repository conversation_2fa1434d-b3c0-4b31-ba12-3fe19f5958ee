import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:buscafarma/core/utils/app_constants.dart';
import 'package:buscafarma/features/auth/domain/entities/location_entity.dart';

part 'user_entity.freezed.dart';
part 'user_entity.g.dart';

@freezed
abstract class UserEntity with _$UserEntity {
  const factory UserEntity({required String id, required String name, required String email, String? profilePictureUrl, String? accessToken, String? refreshToken, UserPreferences? preferences, HealthData? healthData}) = _UserEntity;

  factory UserEntity.fromJson(Map<String, dynamic> json) => _$UserEntityFromJson(json);
}

@freezed
abstract class UserPreferences with _$UserPreferences {
  const factory UserPreferences({@Default(AppThemeMode.system) AppThemeMode theme, @Default(true) bool notifications, LocationEntity? defaultLocation, @Default("es") String language}) = _UserPreferences;

  factory UserPreferences.fromJson(Map<String, dynamic> json) => _$UserPreferencesFromJson(json);
}

@freezed
abstract class HealthData with _$HealthData {
  const factory HealthData({@Default([]) List<String> allergies, @Default([]) List<String> conditions, @Default([]) List<Medication> medications}) = _HealthData;

  factory HealthData.fromJson(Map<String, dynamic> json) => _$HealthDataFromJson(json);
}

@freezed
abstract class Medication with _$Medication {
  const factory Medication({required String name, String? dosage, String? frequency}) = _Medication;

  factory Medication.fromJson(Map<String, dynamic> json) => _$MedicationFromJson(json);
}

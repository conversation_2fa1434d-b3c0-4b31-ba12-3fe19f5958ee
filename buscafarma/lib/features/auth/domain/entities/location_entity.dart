import 'package:freezed_annotation/freezed_annotation.dart';

part 'location_entity.freezed.dart';
part 'location_entity.g.dart';

/// Entity class representing a location with department, province and district codes
@freezed
abstract class LocationEntity with _$LocationEntity {
  const factory LocationEntity({required String departmentCode, required String provinceCode, required String districtCode}) = _LocationEntity;

  factory LocationEntity.fromJson(Map<String, dynamic> json) => _$LocationEntityFromJson(json);
}

// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'location_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$LocationEntity {

 String get departmentCode; String get provinceCode; String get districtCode;
/// Create a copy of LocationEntity
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LocationEntityCopyWith<LocationEntity> get copyWith => _$LocationEntityCopyWithImpl<LocationEntity>(this as LocationEntity, _$identity);

  /// Serializes this LocationEntity to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LocationEntity&&(identical(other.departmentCode, departmentCode) || other.departmentCode == departmentCode)&&(identical(other.provinceCode, provinceCode) || other.provinceCode == provinceCode)&&(identical(other.districtCode, districtCode) || other.districtCode == districtCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,departmentCode,provinceCode,districtCode);

@override
String toString() {
  return 'LocationEntity(departmentCode: $departmentCode, provinceCode: $provinceCode, districtCode: $districtCode)';
}


}

/// @nodoc
abstract mixin class $LocationEntityCopyWith<$Res>  {
  factory $LocationEntityCopyWith(LocationEntity value, $Res Function(LocationEntity) _then) = _$LocationEntityCopyWithImpl;
@useResult
$Res call({
 String departmentCode, String provinceCode, String districtCode
});




}
/// @nodoc
class _$LocationEntityCopyWithImpl<$Res>
    implements $LocationEntityCopyWith<$Res> {
  _$LocationEntityCopyWithImpl(this._self, this._then);

  final LocationEntity _self;
  final $Res Function(LocationEntity) _then;

/// Create a copy of LocationEntity
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? departmentCode = null,Object? provinceCode = null,Object? districtCode = null,}) {
  return _then(_self.copyWith(
departmentCode: null == departmentCode ? _self.departmentCode : departmentCode // ignore: cast_nullable_to_non_nullable
as String,provinceCode: null == provinceCode ? _self.provinceCode : provinceCode // ignore: cast_nullable_to_non_nullable
as String,districtCode: null == districtCode ? _self.districtCode : districtCode // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _LocationEntity implements LocationEntity {
  const _LocationEntity({required this.departmentCode, required this.provinceCode, required this.districtCode});
  factory _LocationEntity.fromJson(Map<String, dynamic> json) => _$LocationEntityFromJson(json);

@override final  String departmentCode;
@override final  String provinceCode;
@override final  String districtCode;

/// Create a copy of LocationEntity
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$LocationEntityCopyWith<_LocationEntity> get copyWith => __$LocationEntityCopyWithImpl<_LocationEntity>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$LocationEntityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _LocationEntity&&(identical(other.departmentCode, departmentCode) || other.departmentCode == departmentCode)&&(identical(other.provinceCode, provinceCode) || other.provinceCode == provinceCode)&&(identical(other.districtCode, districtCode) || other.districtCode == districtCode));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,departmentCode,provinceCode,districtCode);

@override
String toString() {
  return 'LocationEntity(departmentCode: $departmentCode, provinceCode: $provinceCode, districtCode: $districtCode)';
}


}

/// @nodoc
abstract mixin class _$LocationEntityCopyWith<$Res> implements $LocationEntityCopyWith<$Res> {
  factory _$LocationEntityCopyWith(_LocationEntity value, $Res Function(_LocationEntity) _then) = __$LocationEntityCopyWithImpl;
@override @useResult
$Res call({
 String departmentCode, String provinceCode, String districtCode
});




}
/// @nodoc
class __$LocationEntityCopyWithImpl<$Res>
    implements _$LocationEntityCopyWith<$Res> {
  __$LocationEntityCopyWithImpl(this._self, this._then);

  final _LocationEntity _self;
  final $Res Function(_LocationEntity) _then;

/// Create a copy of LocationEntity
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? departmentCode = null,Object? provinceCode = null,Object? districtCode = null,}) {
  return _then(_LocationEntity(
departmentCode: null == departmentCode ? _self.departmentCode : departmentCode // ignore: cast_nullable_to_non_nullable
as String,provinceCode: null == provinceCode ? _self.provinceCode : provinceCode // ignore: cast_nullable_to_non_nullable
as String,districtCode: null == districtCode ? _self.districtCode : districtCode // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on

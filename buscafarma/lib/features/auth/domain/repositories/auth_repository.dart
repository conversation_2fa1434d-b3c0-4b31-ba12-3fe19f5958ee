import 'package:buscafarma/features/auth/domain/entities/user_entity.dart';

/// Repository interface for authentication operations
abstract class AuthRepository {
  /// Signs in user with Google and communicates with backend
  /// Returns a UserEntity with tokens if successful
  Future<UserEntity> signInWithGoogle();

  /// Signs out the current user
  Future<void> signOut();

  /// Checks if the user is currently authenticated
  Future<bool> isAuthenticated();

  /// Gets the current user if authenticated
  Future<UserEntity?> getCurrentUser();

  /// Updates user preferences
  Future<UserEntity> updatePreferences(UserPreferences preferences);

  /// Updates user health data
  Future<UserEntity> updateHealthData(HealthData healthData);

  /// Gets the latest user profile from backend
  Future<UserEntity> getProfileFromBackend();
}

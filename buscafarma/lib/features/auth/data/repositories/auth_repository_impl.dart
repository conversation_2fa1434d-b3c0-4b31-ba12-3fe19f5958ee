import 'dart:async';

import 'package:dio/dio.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:google_sign_in/google_sign_in.dart';
import 'package:buscafarma/core/utils/api_config.dart';
import 'package:buscafarma/features/auth/data/providers/auth_local_provider.dart';
import 'package:buscafarma/features/auth/domain/entities/user_entity.dart';
import 'package:buscafarma/features/auth/domain/repositories/auth_repository.dart';
import 'package:flutter/foundation.dart';

/// Implementation of the AuthRepository interface
class AuthRepositoryImpl implements AuthRepository {
  final _authLocal = AuthLocalProvider();
  Dio _dio;

  AuthRepositoryImpl({Dio? dio}) : _dio = dio ?? Dio(BaseOptions(baseUrl: ApiConfig.baseUrl, connectTimeout: Duration(seconds: ApiConfig.timeoutSeconds), receiveTimeout: Duration(seconds: ApiConfig.timeoutSeconds), contentType: 'application/json'));

  void injectDependencies(Dio dio, AuthLocalProvider localProvider) {
    _dio = dio;
  }

  @override
  Future<UserEntity?> getCurrentUser() async {
    try {
      // Check if we have data in local storage
      final userId = await _authLocal.getUserId();
      final userName = await _authLocal.getUserName();
      final userEmail = await _authLocal.getUserEmail();
      final profilePictureUrl = await _authLocal.getUserProfilePictureUrl();
      final accessToken = await _authLocal.getAccessToken();
      final refreshToken = await _authLocal.getRefreshToken();
      final preferences = await _authLocal.getUserPreferences();
      final healthData = await _authLocal.getUserHealthData();

      if (userId == null || userName == null || userEmail == null || accessToken == null || refreshToken == null) {
        return null;
      }

      return UserEntity(id: userId, name: userName, email: userEmail, profilePictureUrl: profilePictureUrl, accessToken: accessToken, refreshToken: refreshToken, preferences: preferences, healthData: healthData);
    } catch (e) {
      return null;
    }
  }

  @override
  Future<UserEntity> signInWithGoogle() async {
    try {
      // Sign in with Google
      final GoogleSignInAccount? googleUser = await GoogleSignIn().signIn();
      if (googleUser == null) {
        throw Exception('User cancelled sign in process');
      }

      // Authenticate with Firebase
      final GoogleSignInAuthentication googleAuth = await googleUser.authentication;
      final credential = GoogleAuthProvider.credential(accessToken: googleAuth.accessToken, idToken: googleAuth.idToken);
      final userCredential = await FirebaseAuth.instance.signInWithCredential(credential);

      // Get Firebase user
      final firebaseUser = userCredential.user;
      if (firebaseUser == null) {
        throw Exception('Firebase authentication failed');
      }

      // Prepare data for backend
      final loginData = {'firebaseId': firebaseUser.uid, 'email': firebaseUser.email, 'name': firebaseUser.displayName ?? 'Usuario', 'profilePictureUrl': firebaseUser.photoURL ?? '', 'firebaseToken': await firebaseUser.getIdToken()};

      // Send to backend API
      final response = await _dio.post('${ApiConfig.baseUrl}/users/firebase/login', data: loginData);

      if (response.statusCode != 200) {
        throw Exception('Failed to login: ${response.statusCode}');
      }

      if (response.data == null || response.data['data'] == null) {
        throw Exception('Invalid response format');
      }

      final userData = response.data['data'] as Map<String, dynamic>;

      // Create user entity
      final user = UserEntity(
        id: userData['user']['id'],
        name: userData['user']['name'],
        email: userData['user']['email'],
        profilePictureUrl: userData['user']['profilePictureUrl'],
        accessToken: userData['accessToken'],
        refreshToken: userData['refreshToken'],
        preferences: UserPreferences.fromJson(userData['user']['preferences']),
        healthData: HealthData.fromJson(userData['user']['healthData']),
      );

      // Store user data locally
      await _authLocal.saveAuthData(
        accessToken: userData['accessToken'],
        refreshToken: userData['refreshToken'],
        userId: userData['user']['id'],
        userName: userData['user']['name'],
        userEmail: userData['user']['email'],
        userProfilePictureUrl: userData['user']['profilePictureUrl'],
      );

      return user;
    } catch (e) {
      // Handle specific error cases
      if (e is DioException) {
        final dioError = e;
        if (dioError.type == DioExceptionType.connectionTimeout || dioError.type == DioExceptionType.receiveTimeout || dioError.type == DioExceptionType.sendTimeout) {
          throw Exception('Connection timeout. Please check your internet connection.');
        } else if (dioError.response != null) {
          // Handle specific status codes
          if (dioError.response?.statusCode == 422) {
            debugPrint('Validation error details: ${dioError.response?.data}');
            throw Exception('Validation error in sign in request');
          }

          // Try to extract error message from response
          if (dioError.response?.data != null && dioError.response?.data is Map) {
            final data = dioError.response?.data as Map;
            if (data.containsKey('message')) {
              throw Exception(data['message']);
            }
          }

          throw Exception('Error del servidor: ${dioError.response?.statusCode} - ${dioError.response?.data}');
        }
      }
      // Rethrow other errors
      throw Exception('Error during sign in: ${e.toString()}');
    }
  }

  @override
  Future<bool> signOut() async {
    try {
      await FirebaseAuth.instance.signOut();
      await GoogleSignIn().signOut();
      await _authLocal.clearAuthData();
      return true;
    } catch (e) {
      return false;
    }
  }

  @override
  Future<UserEntity> updatePreferences(UserPreferences preferences) async {
    try {
      final accessToken = await _authLocal.getAccessToken();

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      // Prepare headers with token
      final headers = {'Authorization': 'Bearer $accessToken'};

      // Create a sanitized version of the preferences JSON
      final preferencesJson = preferences.toJson();
      if (preferences.defaultLocation == null) {
        // If defaultLocation is null, remove it from the JSON
        preferencesJson.remove('defaultLocation');
      }

      // Call backend API to update preferences
      final response = await _dio.put('${ApiConfig.baseUrl}/users/preferences', data: preferencesJson, options: Options(headers: headers));

      if (response.statusCode != 200) {
        throw Exception('Failed to update preferences: ${response.statusCode}');
      }

      if (response.data == null || response.data['data'] == null) {
        throw Exception('Invalid response format');
      }

      // Update local storage with new preferences
      await _authLocal.saveUserPreferences(preferences);

      // Get the updated user data from response
      final userData = response.data['data'] as Map<String, dynamic>;

      // Create and return updated user entity
      return UserEntity(
        id: userData['id'],
        name: userData['name'],
        email: userData['email'],
        profilePictureUrl: userData['profilePictureUrl'],
        accessToken: accessToken,
        refreshToken: await _authLocal.getRefreshToken(),
        preferences: preferences,
        healthData: await _authLocal.getUserHealthData(),
      );
    } catch (e) {
      // Handle specific error cases
      if (e is DioException) {
        final dioError = e;
        if (dioError.type == DioExceptionType.connectionTimeout || dioError.type == DioExceptionType.receiveTimeout || dioError.type == DioExceptionType.sendTimeout) {
          throw Exception('Connection timeout. Please check your internet connection.');
        } else if (dioError.response != null) {
          // Handle specific status codes
          if (dioError.response?.statusCode == 422) {
            debugPrint('Validation error details: ${dioError.response?.data}');
            throw Exception('Validation error in preferences update request');
          }

          // Try to extract error message from response
          if (dioError.response?.data != null && dioError.response?.data is Map) {
            final data = dioError.response?.data as Map;
            if (data.containsKey('message')) {
              throw Exception(data['message']);
            }
          }

          throw Exception('Error del servidor: ${dioError.response?.statusCode} - ${dioError.response?.data}');
        }
      }

      // Rethrow other errors
      throw Exception('Error updating preferences: ${e.toString()}');
    }
  }

  @override
  Future<UserEntity> getProfileFromBackend() async {
    final accessToken = await _authLocal.getAccessToken();

    if (accessToken == null) {
      throw Exception('User not authenticated');
    }

    try {
      // Prepare headers with token
      final headers = {'Authorization': 'Bearer $accessToken'};

      // Call backend API to get user profile
      final response = await _dio.get('${ApiConfig.baseUrl}/users/profile', options: Options(headers: headers));

      if (response.statusCode != 200) {
        throw Exception('Failed to get profile: ${response.statusCode}');
      }

      if (response.data == null || response.data['data'] == null) {
        throw Exception('Invalid response format: missing data');
      }

      final userData = response.data['data'] as Map<String, dynamic>;

      // Parse preferences if provided
      UserPreferences? preferences;
      if (userData['preferences'] != null && userData['preferences'] is Map<String, dynamic>) {
        preferences = UserPreferences.fromJson(userData['preferences'] as Map<String, dynamic>);
      }

      // Parse health data if provided
      HealthData? healthData;
      if (userData['healthData'] != null && userData['healthData'] is Map<String, dynamic>) {
        healthData = HealthData.fromJson(userData['healthData'] as Map<String, dynamic>);
      }

      // Save the preferences and health data to local storage
      if (preferences != null) {
        await _authLocal.saveUserPreferences(preferences);
      }

      if (healthData != null) {
        await _authLocal.saveUserHealthData(healthData);
      }

      // Create and return user entity
      final user = UserEntity(
        id: userData['id'],
        name: userData['name'],
        email: userData['email'],
        profilePictureUrl: userData['profilePictureUrl'],
        accessToken: accessToken,
        refreshToken: userData['refreshToken'] ?? await _authLocal.getRefreshToken() ?? '',
        preferences: preferences,
        healthData: healthData,
      );

      return user;
    } catch (e) {
      if (e is DioException && e.response != null) {
        throw Exception('Error getting profile: ${e.response?.statusCode} - ${e.response?.data}');
      }
      throw Exception('Error getting profile: ${e.toString()}');
    }
  }

  @override
  Future<UserEntity> updateHealthData(HealthData healthData) async {
    try {
      final accessToken = await _authLocal.getAccessToken();

      if (accessToken == null) {
        throw Exception('User not authenticated');
      }

      // Prepare headers with token
      final headers = {'Authorization': 'Bearer $accessToken'};

      // Call backend API to update health data
      final response = await _dio.put('${ApiConfig.baseUrl}/users/health-data', data: healthData.toJson(), options: Options(headers: headers));

      if (response.statusCode != 200) {
        throw Exception('Failed to update health data: ${response.statusCode}');
      }

      if (response.data == null || response.data['data'] == null) {
        throw Exception('Invalid response format');
      }

      // Update local storage with new health data
      await _authLocal.saveUserHealthData(healthData);

      // Get the updated user data from response
      final userData = response.data['data'] as Map<String, dynamic>;

      // Create and return updated user entity
      return UserEntity(
        id: userData['id'],
        name: userData['name'],
        email: userData['email'],
        profilePictureUrl: userData['profilePictureUrl'],
        accessToken: accessToken,
        refreshToken: await _authLocal.getRefreshToken(),
        preferences: await _authLocal.getUserPreferences(),
        healthData: healthData,
      );
    } catch (e) {
      throw Exception('Error updating health data: ${e.toString()}');
    }
  }

  @override
  Future<bool> isAuthenticated() async {
    return await _authLocal.hasAuthData();
  }
}

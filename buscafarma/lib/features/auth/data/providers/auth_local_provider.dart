import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:buscafarma/features/auth/domain/entities/user_entity.dart';

/// Provider for handling authentication-related local storage operations
class AuthLocalProvider {
  static const String _accessTokenKey = 'accessToken';
  static const String _refreshTokenKey = 'refreshToken';
  static const String _userIdKey = 'userId';
  static const String _userNameKey = 'userName';
  static const String _userEmailKey = 'userEmail';
  static const String _userProfilePictureUrlKey = 'userProfilePictureUrl';
  static const String _userPreferencesKey = 'userPreferences';
  static const String _userHealthDataKey = 'userHealthData';

  /// Saves user authentication data to local storage
  Future<void> saveAuthData({
    required String accessToken,
    required String refreshToken,
    required String userId,
    required String userName,
    required String userEmail,
    String? userProfilePictureUrl,
    UserPreferences? userPreferences,
    HealthData? userHealthData,
  }) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_accessTokenKey, accessToken);
    await prefs.setString(_refreshTokenKey, refreshToken);
    await prefs.setString(_userIdKey, userId);
    await prefs.setString(_userNameKey, userName);
    await prefs.setString(_userEmailKey, userEmail);

    if (userProfilePictureUrl != null) {
      await prefs.setString(_userProfilePictureUrlKey, userProfilePictureUrl);
    }

    if (userPreferences != null) {
      await prefs.setString(_userPreferencesKey, jsonEncode(userPreferences.toJson()));
    }

    if (userHealthData != null) {
      await prefs.setString(_userHealthDataKey, jsonEncode(userHealthData.toJson()));
    }
  }

  /// Saves just the access token without changing other data
  Future<void> saveAccessToken(String accessToken) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_accessTokenKey, accessToken);
  }

  /// Updates user preferences in local storage
  Future<void> saveUserPreferences(UserPreferences preferences) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userPreferencesKey, jsonEncode(preferences.toJson()));
  }

  /// Updates user health data in local storage
  Future<void> saveUserHealthData(HealthData healthData) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userHealthDataKey, jsonEncode(healthData.toJson()));
  }

  /// Clears all authentication data from local storage
  Future<void> clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_accessTokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_userIdKey);
    await prefs.remove(_userNameKey);
    await prefs.remove(_userEmailKey);
    await prefs.remove(_userProfilePictureUrlKey);
    await prefs.remove(_userPreferencesKey);
    await prefs.remove(_userHealthDataKey);
  }

  /// Gets the stored access token
  Future<String?> getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_accessTokenKey);
  }

  /// Gets the stored refresh token
  Future<String?> getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }

  /// Gets the stored user ID
  Future<String?> getUserId() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userIdKey);
  }

  /// Gets the stored user name
  Future<String?> getUserName() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userNameKey);
  }

  /// Gets the stored user email
  Future<String?> getUserEmail() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userEmailKey);
  }

  /// Gets the stored user profile picture URL
  Future<String?> getUserProfilePictureUrl() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_userProfilePictureUrlKey);
  }

  /// Gets the stored user preferences
  Future<UserPreferences?> getUserPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    final prefsString = prefs.getString(_userPreferencesKey);

    if (prefsString == null) {
      return null;
    }

    try {
      final prefsJson = jsonDecode(prefsString) as Map<String, dynamic>;
      return UserPreferences.fromJson(prefsJson);
    } catch (e) {
      return null;
    }
  }

  /// Gets the stored user health data
  Future<HealthData?> getUserHealthData() async {
    final prefs = await SharedPreferences.getInstance();
    final healthDataString = prefs.getString(_userHealthDataKey);

    if (healthDataString == null) {
      return null;
    }

    try {
      final healthDataJson = jsonDecode(healthDataString) as Map<String, dynamic>;
      return HealthData.fromJson(healthDataJson);
    } catch (e) {
      return null;
    }
  }

  /// Checks if the user has authentication data stored
  Future<bool> hasAuthData() async {
    final accessToken = await getAccessToken();
    return accessToken != null && accessToken.isNotEmpty;
  }
}

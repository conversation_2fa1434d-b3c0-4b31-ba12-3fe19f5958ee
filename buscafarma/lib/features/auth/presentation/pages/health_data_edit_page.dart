import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/auth/domain/entities/user_entity.dart';
import 'package:buscafarma/features/auth/presentation/providers/auth_provider.dart';

/// Page for editing user health data
class HealthDataEditPage extends ConsumerStatefulWidget {
  const HealthDataEditPage({super.key});

  @override
  ConsumerState<HealthDataEditPage> createState() => _HealthDataEditPageState();
}

class _HealthDataEditPageState extends ConsumerState<HealthDataEditPage> {
  late List<String> _allergies;
  late List<String> _conditions;
  late List<Medication> _medications;

  // Text controllers for adding new items
  final TextEditingController _newAllergyController = TextEditingController();
  final TextEditingController _newConditionController = TextEditingController();
  final TextEditingController _newMedNameController = TextEditingController();
  final TextEditingController _newMedDosageController = TextEditingController();
  final TextEditingController _newMedFrequencyController = TextEditingController();

  bool _isSaving = false;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();

    // Delay initialization until after the widget is built
    Future.microtask(() {
      _initializeData();
    });
  }

  void _initializeData() {
    // Initialize lists from user data
    final healthData = ref.read(authProvider).user?.healthData ?? const HealthData();

    if (!mounted) return;

    setState(() {
      _allergies = List<String>.from(healthData.allergies);
      _conditions = List<String>.from(healthData.conditions);
      _medications = List<Medication>.from(healthData.medications);
      _isInitialized = true;
    });
  }

  @override
  void dispose() {
    _newAllergyController.dispose();
    _newConditionController.dispose();
    _newMedNameController.dispose();
    _newMedDosageController.dispose();
    _newMedFrequencyController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // If not initialized yet, show a loading indicator
    if (!_isInitialized) {
      return Scaffold(appBar: AppBar(title: const Text('Cargando datos de salud')), body: const Center(child: CircularProgressIndicator()));
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Editar Datos de Salud'),
        actions: [
          _isSaving ? const Padding(padding: EdgeInsets.all(16.0), child: SizedBox(width: 24, height: 24, child: CircularProgressIndicator())) : IconButton(icon: const Icon(Icons.save), onPressed: _saveHealthData, tooltip: 'Guardar cambios'),
        ],
      ),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          // Allergies section
          _buildSectionHeader('Alergias', Icons.warning_amber),
          ..._buildAllergiesSection(),

          const Divider(height: 32),

          // Medical conditions section
          _buildSectionHeader('Condiciones Médicas', Icons.medical_services),
          ..._buildConditionsSection(),

          const Divider(height: 32),

          // Medications section
          _buildSectionHeader('Medicamentos', Icons.medication),
          ..._buildMedicationsSection(),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(padding: const EdgeInsets.only(bottom: 16.0), child: Row(children: [Icon(icon), const SizedBox(width: 8), Text(title, style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold))]));
  }

  List<Widget> _buildAllergiesSection() {
    return [
      // List of current allergies
      ..._allergies.asMap().entries.map(
        (entry) => Card(
          child: ListTile(
            title: Text(entry.value),
            trailing: IconButton(
              icon: const Icon(Icons.delete),
              onPressed:
                  () => setState(() {
                    _allergies.removeAt(entry.key);
                  }),
              tooltip: 'Eliminar',
            ),
          ),
        ),
      ),

      // Add new allergy
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            Expanded(child: TextField(controller: _newAllergyController, decoration: const InputDecoration(hintText: 'Nueva alergia', border: OutlineInputBorder()))),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                if (_newAllergyController.text.trim().isNotEmpty) {
                  setState(() {
                    _allergies.add(_newAllergyController.text.trim());
                    _newAllergyController.clear();
                  });
                }
              },
              child: const Icon(Icons.add),
            ),
          ],
        ),
      ),
    ];
  }

  List<Widget> _buildConditionsSection() {
    return [
      // List of current conditions
      ..._conditions.asMap().entries.map(
        (entry) => Card(
          child: ListTile(
            title: Text(entry.value),
            trailing: IconButton(
              icon: const Icon(Icons.delete),
              onPressed:
                  () => setState(() {
                    _conditions.removeAt(entry.key);
                  }),
              tooltip: 'Eliminar',
            ),
          ),
        ),
      ),

      // Add new condition
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Row(
          children: [
            Expanded(child: TextField(controller: _newConditionController, decoration: const InputDecoration(hintText: 'Nueva condición médica', border: OutlineInputBorder()))),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                if (_newConditionController.text.trim().isNotEmpty) {
                  setState(() {
                    _conditions.add(_newConditionController.text.trim());
                    _newConditionController.clear();
                  });
                }
              },
              child: const Icon(Icons.add),
            ),
          ],
        ),
      ),
    ];
  }

  List<Widget> _buildMedicationsSection() {
    return [
      // List of current medications
      ..._medications.asMap().entries.map(
        (entry) => Card(
          child: ListTile(
            title: Text(entry.value.name),
            subtitle: Text('${entry.value.dosage ?? "Sin dosis"} ${entry.value.frequency != null ? '(${entry.value.frequency})' : ''}'),
            trailing: IconButton(
              icon: const Icon(Icons.delete),
              onPressed:
                  () => setState(() {
                    _medications.removeAt(entry.key);
                  }),
              tooltip: 'Eliminar',
            ),
          ),
        ),
      ),

      // Add new medication form
      Padding(
        padding: const EdgeInsets.symmetric(vertical: 8.0),
        child: Card(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Agregar medicamento', style: Theme.of(context).textTheme.titleSmall),
                const SizedBox(height: 16),
                TextField(controller: _newMedNameController, decoration: const InputDecoration(labelText: 'Nombre del medicamento', hintText: 'Ej: Paracetamol', border: OutlineInputBorder())),
                const SizedBox(height: 8),
                TextField(controller: _newMedDosageController, decoration: const InputDecoration(labelText: 'Dosis', hintText: 'Ej: 500mg', border: OutlineInputBorder())),
                const SizedBox(height: 8),
                TextField(controller: _newMedFrequencyController, decoration: const InputDecoration(labelText: 'Frecuencia', hintText: 'Ej: Cada 8 horas', border: OutlineInputBorder())),
                const SizedBox(height: 16),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      if (_newMedNameController.text.trim().isNotEmpty) {
                        setState(() {
                          _medications.add(
                            Medication(
                              name: _newMedNameController.text.trim(),
                              dosage: _newMedDosageController.text.trim().isEmpty ? null : _newMedDosageController.text.trim(),
                              frequency: _newMedFrequencyController.text.trim().isEmpty ? null : _newMedFrequencyController.text.trim(),
                            ),
                          );
                          _newMedNameController.clear();
                          _newMedDosageController.clear();
                          _newMedFrequencyController.clear();
                        });
                      } else {
                        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('El nombre del medicamento es obligatorio')));
                      }
                    },
                    child: const Text('Agregar Medicamento'),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ];
  }

  Future<void> _saveHealthData() async {
    setState(() {
      _isSaving = true;
    });

    try {
      // Create updated health data object
      final updatedHealthData = HealthData(allergies: _allergies, conditions: _conditions, medications: _medications);

      // Save to backend
      final success = await ref.read(authProvider.notifier).updateHealthData(updatedHealthData);

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Datos actualizados correctamente')));
        Navigator.of(context).pop();
      } else if (mounted) {
        // Mostrar mensaje de error detallado desde el provider
        final errorMessage = ref.read(authProvider).errorMessage ?? 'Error al guardar los datos';
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(errorMessage), backgroundColor: Theme.of(context).colorScheme.error));
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error al guardar los datos: ${e.toString()}'), backgroundColor: Theme.of(context).colorScheme.error));
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }
}

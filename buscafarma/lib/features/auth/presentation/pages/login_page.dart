import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/router/app_router.dart';
import 'package:buscafarma/core/router/app_routes.dart';
import 'package:buscafarma/core/widgets/app_logo.dart';
import 'package:buscafarma/features/auth/presentation/providers/auth_provider.dart';
import 'package:buscafarma/features/auth/presentation/widgets/google_sign_in_button.dart';
import 'package:buscafarma/core/animations/animation_constants.dart';
import 'package:buscafarma/core/animations/staggered_animation_controller.dart';

class LoginPage extends ConsumerStatefulWidget {
  const LoginPage({super.key});

  @override
  ConsumerState<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends ConsumerState<LoginPage> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late StaggeredAnimationController _staggeredController;

  // Individual animations for each element
  late Animation<double> _logoFadeAnimation;
  late Animation<Offset> _logoSlideAnimation;
  late Animation<double> _titleFadeAnimation;
  late Animation<Offset> _titleSlideAnimation;
  late Animation<double> _taglineFadeAnimation;
  late Animation<Offset> _taglineSlideAnimation;
  late Animation<double> _buttonFadeAnimation;
  late Animation<Offset> _buttonSlideAnimation;

  // Error animation controller
  late AnimationController _errorController;
  late Animation<Offset> _errorShakeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize main animation controller
    _animationController = AnimationController(duration: AnimationConstants.loginEntranceDuration, vsync: this);

    // Initialize error animation controller
    _errorController = AnimationController(duration: AnimationConstants.errorAnimationDuration, vsync: this);

    // Initialize staggered animation controller
    _staggeredController = StaggeredAnimationController(controller: _animationController);

    _setupAnimations();

    // Start entrance animations
    _animationController.forward();
  }

  void _setupAnimations() {
    // Logo animations - slide down from top with bounce
    _logoFadeAnimation = _staggeredController.createFadeAnimation(key: 'logo_fade', delay: AnimationTimings.login['logo']!, duration: const Duration(milliseconds: 500));

    _logoSlideAnimation = _staggeredController.createSlideAnimation(
      key: 'logo_slide',
      delay: AnimationTimings.login['logo']!,
      fromOffset: AnimationConstants.slideFromTop,
      duration: const Duration(milliseconds: 600),
      curve: AnimationConstants.bounceCurve,
    );

    // Title animations - slide from left
    _titleFadeAnimation = _staggeredController.createFadeAnimation(key: 'title_fade', delay: AnimationTimings.login['title']!, duration: const Duration(milliseconds: 400));

    _titleSlideAnimation = _staggeredController.createSlideAnimation(key: 'title_slide', delay: AnimationTimings.login['title']!, fromOffset: AnimationConstants.slideFromLeft, duration: const Duration(milliseconds: 500));

    // Tagline animations - slide from right
    _taglineFadeAnimation = _staggeredController.createFadeAnimation(key: 'tagline_fade', delay: AnimationTimings.login['tagline']!, duration: const Duration(milliseconds: 400));

    _taglineSlideAnimation = _staggeredController.createSlideAnimation(key: 'tagline_slide', delay: AnimationTimings.login['tagline']!, fromOffset: AnimationConstants.slideFromRight, duration: const Duration(milliseconds: 500));

    // Button animations - slide up from bottom
    _buttonFadeAnimation = _staggeredController.createFadeAnimation(key: 'button_fade', delay: AnimationTimings.login['button']!, duration: const Duration(milliseconds: 400));

    _buttonSlideAnimation = _staggeredController.createSlideAnimation(
      key: 'button_slide',
      delay: AnimationTimings.login['button']!,
      fromOffset: AnimationConstants.slideFromBottom,
      duration: const Duration(milliseconds: 500),
      curve: AnimationConstants.bounceCurve,
    );

    // Error shake animation
    _errorShakeAnimation = Tween<Offset>(begin: Offset.zero, end: const Offset(0.1, 0.0)).animate(CurvedAnimation(parent: _errorController, curve: Curves.elasticIn));
  }

  void _triggerErrorAnimation() {
    _errorController.reset();
    _errorController.forward().then((_) {
      _errorController.reverse();
    });
  }

  @override
  void dispose() {
    _animationController.dispose();
    _errorController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);

    // Listen for authentication state changes and navigate if authenticated
    ref.listen<AuthState>(authProvider, (previous, current) {
      // If user just got authenticated and we have user data
      if (!previous!.isAuthenticated && current.isAuthenticated && current.user != null) {
        // Navigate to map page and clear navigation stack
        AppRouter.navigateTo(context, AppRoutes.map, clearStack: true);
      }

      // Show error if there is one and trigger error animation
      if (current.errorMessage != null && current.errorMessage != previous.errorMessage) {
        _triggerErrorAnimation();
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(current.errorMessage!), backgroundColor: Colors.red, duration: const Duration(seconds: 3)));
        // Clear error after showing it
        ref.read(authProvider.notifier).clearError();
      }
    });

    return Scaffold(
      body: SafeArea(
        child: AnimatedBuilder(
          animation: Listenable.merge([_animationController, _errorController]),
          builder: (context, child) {
            return SlideTransition(
              position: _errorShakeAnimation,
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    // App logo with slide down animation
                    SlideTransition(position: _logoSlideAnimation, child: FadeTransition(opacity: _logoFadeAnimation, child: const AppLogo.large())),

                    const SizedBox(height: 20),

                    // App name with slide from left animation
                    SlideTransition(position: _titleSlideAnimation, child: FadeTransition(opacity: _titleFadeAnimation, child: const Text('BuscaFarma', style: TextStyle(fontSize: 30, fontWeight: FontWeight.bold)))),

                    const SizedBox(height: 16),

                    // Tagline with slide from right animation
                    SlideTransition(position: _taglineSlideAnimation, child: FadeTransition(opacity: _taglineFadeAnimation, child: const Text('Encuentra farmacias cercanas a ti', style: TextStyle(fontSize: 16, color: Colors.grey)))),

                    const SizedBox(height: 60),

                    // Google Sign-In Button with slide up animation
                    SlideTransition(
                      position: _buttonSlideAnimation,
                      child: FadeTransition(
                        opacity: _buttonFadeAnimation,
                        child: GoogleSignInButton(
                          isLoading: authState.isLoading,
                          onPressed: () async {
                            // Start Google sign-in process
                            await ref.read(authProvider.notifier).signInWithGoogle();
                            // Navigation will be handled by the listener above
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}

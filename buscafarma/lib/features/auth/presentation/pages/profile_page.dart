import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/providers/theme_provider.dart';
import 'package:buscafarma/core/utils/app_constants.dart';
import 'package:buscafarma/core/router/app_router.dart';
import 'package:buscafarma/core/router/app_routes.dart';
import 'package:buscafarma/core/widgets/app_logo_header.dart';
import 'package:buscafarma/features/auth/domain/entities/user_entity.dart';
import 'package:buscafarma/features/auth/presentation/providers/auth_provider.dart';
import 'package:buscafarma/core/providers/announcement_provider.dart';

/// Profile page for displaying and editing user information
class ProfilePage extends ConsumerStatefulWidget {
  const ProfilePage({super.key});

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  @override
  void initState() {
    super.initState();

    // Use Future.microtask to delay the provider modification until after the widget is built
    Future.microtask(() {
      _refreshProfile();
    });
  }

  Future<void> _refreshProfile() async {
    await ref.read(authProvider.notifier).getProfileFromBackend();
  }

  @override
  Widget build(BuildContext context) {
    final authState = ref.watch(authProvider);
    final user = authState.user;
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    if (user == null) {
      return Scaffold(appBar: AppBar(title: const Text('Perfil')), body: const Center(child: Text('No se encontró información de usuario')));
    }

    return Scaffold(
      appBar: AppBar(leading: const AppLogoHeader.compact(), title: const Text('Mi Perfil'), actions: [IconButton(icon: const Icon(Icons.logout), onPressed: () => _showLogoutDialog(context), tooltip: 'Cerrar sesión')]),
      body: RefreshIndicator(
        onRefresh: _refreshProfile,
        child: ListView(
          padding: const EdgeInsets.all(16.0),
          children: [
            // User profile header
            _buildProfileHeader(user, colorScheme),
            const SizedBox(height: 24),

            // User preferences section
            _buildPreferencesSection(user),
            // const SizedBox(height: 16),

            // Health data section
            // _buildHealthDataSection(user),
          ],
        ),
      ),
    );
  }

  Widget _buildProfileHeader(UserEntity user, ColorScheme colorScheme) {
    return Column(
      children: [
        // Profile picture
        Hero(
          tag: 'profile_avatar_hero',
          child: Material(
            color: Colors.transparent,
            child: Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(shape: BoxShape.circle, border: Border.all(color: colorScheme.primary, width: 3), color: colorScheme.primaryContainer),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(60),
                child:
                    user.profilePictureUrl != null
                        ? Image.network(user.profilePictureUrl!, fit: BoxFit.cover, errorBuilder: (context, error, stackTrace) => Icon(Icons.person, color: colorScheme.primary, size: 60))
                        : Icon(Icons.person, color: colorScheme.primary, size: 60),
              ),
            ),
          ),
        ),
        const SizedBox(height: 16),
        // Name
        Text(user.name, style: Theme.of(context).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
        const SizedBox(height: 4),
        // Email
        Text(user.email, style: Theme.of(context).textTheme.bodyMedium?.copyWith(color: Theme.of(context).colorScheme.onSurfaceVariant)),
      ],
    );
  }

  Widget _buildPreferencesSection(UserEntity user) {
    // final preferences = user.preferences ?? const UserPreferences();
    final currentTheme = ref.watch(themeProvider);

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [Text('Preferencias', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold))]),
            const Divider(),

            // Theme selection
            ListTile(title: const Text('Tema'), subtitle: Text(_getThemeName(currentTheme)), leading: const Icon(Icons.brightness_4), onTap: () => _showThemeSelectionDialog(context)),

            // Notifications toggle
            // SwitchListTile(title: const Text('Notificaciones'), subtitle: const Text('Recibir alertas y notificaciones'), value: preferences.notifications, onChanged: (value) => _updateNotificationPreference(value)),

            // Language (read-only for now)
            ListTile(title: const Text('Idioma'), subtitle: const Text('Español'), leading: const Icon(Icons.language)),

            // Information announcement
            ListTile(title: const Text('Información'), subtitle: const Text('Mensaje informativo de la aplicación'), leading: const Icon(Icons.info_outline), onTap: () => _showAnnouncementDialog(context)),
          ],
        ),
      ),
    );
  }

  // Widget _buildHealthDataSection(UserEntity user) {
  //   final healthData = user.healthData ?? const HealthData();

  //   return Card(
  //     elevation: 2,
  //     child: Padding(
  //       padding: const EdgeInsets.all(16.0),
  //       child: Column(
  //         crossAxisAlignment: CrossAxisAlignment.start,
  //         children: [
  //           Row(
  //             mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //             children: [
  //               Text('Datos de Salud', style: Theme.of(context).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
  //               IconButton(
  //                 icon: const Icon(Icons.edit),
  //                 onPressed: () {
  //                   AppRouter.navigateTo(context, AppRoutes.editHealthData);
  //                 },
  //                 tooltip: 'Editar datos de salud',
  //               ),
  //             ],
  //           ),
  //           const Divider(),

  //           // Allergies
  //           ListTile(
  //             title: const Text('Alergias'),
  //             subtitle: healthData.allergies.isEmpty ? const Text('Ninguna alergia registrada') : Wrap(spacing: 4, children: healthData.allergies.map((allergy) => Chip(label: Text(allergy))).toList()),
  //             leading: const Icon(Icons.warning_amber),
  //           ),

  //           // Medical conditions
  //           ListTile(
  //             title: const Text('Condiciones médicas'),
  //             subtitle: healthData.conditions.isEmpty ? const Text('Ninguna condición registrada') : Wrap(spacing: 4, children: healthData.conditions.map((condition) => Chip(label: Text(condition))).toList()),
  //             leading: const Icon(Icons.medical_services),
  //           ),

  //           // Medications
  //           ListTile(
  //             title: const Text('Medicamentos'),
  //             subtitle:
  //                 healthData.medications.isEmpty
  //                     ? const Text('Ningún medicamento registrado')
  //                     : Column(
  //                       crossAxisAlignment: CrossAxisAlignment.start,
  //                       children:
  //                           healthData.medications
  //                               .map(
  //                                 (med) => Padding(
  //                                   padding: const EdgeInsets.only(bottom: 4.0),
  //                                   child: Text(
  //                                     '${med.name}'
  //                                     '${med.dosage != null ? ' - ${med.dosage}' : ''}'
  //                                     '${med.frequency != null ? ' (${med.frequency})' : ''}',
  //                                   ),
  //                                 ),
  //                               )
  //                               .toList(),
  //                     ),
  //             leading: const Icon(Icons.medication),
  //           ),
  //         ],
  //       ),
  //     ),
  //   );
  // }

  String _getThemeName(AppThemeMode themeMode) {
    switch (themeMode) {
      case AppThemeMode.light:
        return 'Claro';
      case AppThemeMode.dark:
        return 'Oscuro';
      case AppThemeMode.system:
        return 'Sistema';
    }
  }

  Future<void> _showThemeSelectionDialog(BuildContext context) async {
    final currentTheme = ref.read(themeProvider);

    return showDialog<void>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Seleccionar tema'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              RadioListTile<AppThemeMode>(
                title: const Text('Sistema'),
                value: AppThemeMode.system,
                groupValue: currentTheme,
                onChanged: (value) {
                  Navigator.of(context).pop();
                  ref.read(themeProvider.notifier).setTheme(AppThemeMode.system);
                },
              ),
              RadioListTile<AppThemeMode>(
                title: const Text('Claro'),
                value: AppThemeMode.light,
                groupValue: currentTheme,
                onChanged: (value) {
                  Navigator.of(context).pop();
                  ref.read(themeProvider.notifier).setTheme(AppThemeMode.light);
                },
              ),
              RadioListTile<AppThemeMode>(
                title: const Text('Oscuro'),
                value: AppThemeMode.dark,
                groupValue: currentTheme,
                onChanged: (value) {
                  Navigator.of(context).pop();
                  ref.read(themeProvider.notifier).setTheme(AppThemeMode.dark);
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              child: const Text('Cancelar'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  // Future<void> _updateNotificationPreference(bool enabled) async {
  //   final authState = ref.read(authProvider);
  //   final preferences = authState.user?.preferences ?? const UserPreferences();
  //   final updatedPreferences = preferences.copyWith(notifications: enabled);

  //   try {
  //     final success = await ref.read(authProvider.notifier).updatePreferences(updatedPreferences);

  //     if (!success && mounted) {
  //       // Muestra mensaje de error si falló
  //       final errorMessage = ref.read(authProvider).errorMessage ?? 'Error al actualizar preferencias';
  //       ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(errorMessage), backgroundColor: Theme.of(context).colorScheme.error));
  //     }
  //   } catch (e) {
  //     if (mounted) {
  //       ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}'), backgroundColor: Theme.of(context).colorScheme.error));
  //     }
  //   }
  // }

  /// Show the informational announcement dialog
  Future<void> _showAnnouncementDialog(BuildContext context) async {
    final announcementService = ref.read(announcementServiceProvider);
    await announcementService.showAnnouncement(context);
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: const Text('Cerrar sesión'),
            content: const Text('¿Estás seguro que deseas cerrar sesión?'),
            actions: [
              TextButton(onPressed: () => Navigator.of(context).pop(), child: const Text('Cancelar')),
              TextButton(
                onPressed: () async {
                  Navigator.of(context).pop();
                  await ref.read(authProvider.notifier).signOut();
                  if (context.mounted) {
                    AppRouter.navigateTo(context, AppRoutes.login, clearStack: true);
                  }
                },
                child: const Text('Cerrar sesión'),
              ),
            ],
          ),
    );
  }
}

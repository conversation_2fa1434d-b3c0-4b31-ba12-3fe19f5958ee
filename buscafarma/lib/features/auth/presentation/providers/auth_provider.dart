import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/providers/dio_provider.dart';
import 'package:buscafarma/features/auth/data/repositories/auth_repository_impl.dart';
import 'package:buscafarma/features/auth/domain/entities/user_entity.dart';
import 'package:buscafarma/features/auth/domain/repositories/auth_repository.dart';
import 'package:buscafarma/features/review/presentation/providers/review_manager_provider.dart';

// Repository provider
final authRepositoryProvider = Provider<AuthRepository>((ref) {
  final dio = ref.watch(dioProvider);
  final authLocal = ref.watch(authLocalProvider);
  final repository = AuthRepositoryImpl(dio: dio);
  repository.injectDependencies(dio, authLocal);
  return repository;
});

// Auth state model
class AuthState {
  final bool isAuthenticated;
  final bool isLoading;
  final UserEntity? user;
  final String? errorMessage;

  AuthState({this.isAuthenticated = false, this.isLoading = false, this.user, this.errorMessage});

  AuthState copyWith({bool? isAuthenticated, bool? isLoading, UserEntity? user, String? errorMessage}) {
    return AuthState(isAuthenticated: isAuthenticated ?? this.isAuthenticated, isLoading: isLoading ?? this.isLoading, user: user ?? this.user, errorMessage: errorMessage);
  }
}

// Auth notifier
class AuthNotifier extends StateNotifier<AuthState> {
  final AuthRepository _authRepository;
  final Ref _ref;

  AuthNotifier(this._authRepository, this._ref) : super(AuthState());

  // Check authentication state on startup
  Future<void> checkAuthState() async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final isAuthenticated = await _authRepository.isAuthenticated();

      if (isAuthenticated) {
        final user = await _authRepository.getCurrentUser();
        state = state.copyWith(isAuthenticated: true, isLoading: false, user: user);
      } else {
        state = state.copyWith(isAuthenticated: false, isLoading: false, user: null);
      }
    } catch (e) {
      state = state.copyWith(isAuthenticated: false, isLoading: false, user: null, errorMessage: 'Error checking authentication: ${e.toString()}');
    }
  }

  // Sign in with Google
  Future<bool> signInWithGoogle() async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final user = await _authRepository.signInWithGoogle();

      // Update state with basic user data from initial sign-in
      state = state.copyWith(isAuthenticated: true, user: user);

      // Now explicitly load the complete profile including health_data
      try {
        // Maintain the loading state while fetching profile
        state = state.copyWith(isLoading: true);

        // Get the complete profile from backend
        await getProfileFromBackend();

        // Record successful authentication for review tracking
        try {
          final reviewManager = _ref.read(reviewManagerProvider.notifier);
          await reviewManager.recordSuccessfulAuth(
            metadata: {
              'user_id': user.id,
              'is_new_user': false, // Could be enhanced to detect new users
              'timestamp': DateTime.now().toIso8601String(),
            },
          );
        } catch (e) {
          // Don't fail authentication if review tracking fails
          debugPrint('Failed to record auth interaction for review: $e');
        }

        // Sign-in complete and profile loaded successfully
        return true;
      } catch (profileError) {
        // If profile loading fails, log the error but don't fail the whole sign-in
        state = state.copyWith(isLoading: false, errorMessage: 'Autenticación exitosa, pero hubo un problema cargando el perfil completo: ${profileError.toString()}');
        // We still consider the sign-in successful even if profile loading fails
        return true;
      }
    } catch (e) {
      // If the core sign-in fails, update state and return failure
      state = state.copyWith(isAuthenticated: false, isLoading: false, errorMessage: 'Error al iniciar sesión con Google: ${e.toString()}');
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      await _authRepository.signOut();
      state = state.copyWith(isAuthenticated: false, isLoading: false, user: null);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: 'Error signing out: ${e.toString()}');
    }
  }

  // Update user preferences
  Future<bool> updatePreferences(UserPreferences preferences) async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final updatedUser = await _authRepository.updatePreferences(preferences);
      state = state.copyWith(isLoading: false, user: updatedUser);

      // Record profile update for review tracking
      try {
        final reviewManager = _ref.read(reviewManagerProvider.notifier);
        await reviewManager.recordProfileUpdated(metadata: {'update_type': 'preferences', 'user_id': updatedUser.id, 'timestamp': DateTime.now().toIso8601String()});
      } catch (e) {
        // Don't fail the update if review tracking fails
        debugPrint('Failed to record profile update interaction for review: $e');
      }

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: 'Error updating preferences: ${e.toString()}');
      return false;
    }
  }

  // Update user health data
  Future<bool> updateHealthData(HealthData healthData) async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final updatedUser = await _authRepository.updateHealthData(healthData);
      state = state.copyWith(isLoading: false, user: updatedUser);

      // Record profile update for review tracking
      try {
        final reviewManager = _ref.read(reviewManagerProvider.notifier);
        await reviewManager.recordProfileUpdated(metadata: {'update_type': 'health_data', 'user_id': updatedUser.id, 'timestamp': DateTime.now().toIso8601String()});
      } catch (e) {
        // Don't fail the update if review tracking fails
        debugPrint('Failed to record health data update interaction for review: $e');
      }

      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: 'Error updating health data: ${e.toString()}');
      return false;
    }
  }

  // Fetch latest profile from backend
  Future<bool> getProfileFromBackend() async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final updatedUser = await _authRepository.getProfileFromBackend();
      state = state.copyWith(isAuthenticated: true, isLoading: false, user: updatedUser);
      return true;
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: 'Error fetching profile: ${e.toString()}');
      return false;
    }
  }

  // Helper method to update preferences locally without API call
  void updateUserPreferencesLocally(UserPreferences preferences) {
    if (state.user != null) {
      state = state.copyWith(user: state.user!.copyWith(preferences: preferences));
    }
  }

  // Helper method to update health data locally without API call
  void updateUserHealthDataLocally(HealthData healthData) {
    if (state.user != null) {
      state = state.copyWith(user: state.user!.copyWith(healthData: healthData));
    }
  }

  // Clear error message
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }
}

// Auth provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  final repository = ref.watch(authRepositoryProvider);
  return AuthNotifier(repository, ref);
});

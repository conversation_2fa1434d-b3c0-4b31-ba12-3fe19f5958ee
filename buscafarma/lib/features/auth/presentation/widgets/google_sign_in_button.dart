import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:buscafarma/core/animations/animation_constants.dart';

class GoogleSignInButton extends StatefulWidget {
  final VoidCallback onPressed;
  final bool isLoading;

  const GoogleSignInButton({super.key, required this.onPressed, this.isLoading = false});

  @override
  State<GoogleSignInButton> createState() => _GoogleSignInButtonState();
}

class _GoogleSignInButtonState extends State<GoogleSignInButton> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  bool _isPressed = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(duration: AnimationConstants.microInteractionDuration, vsync: this);

    _scaleAnimation = Tween<double>(begin: 1.0, end: AnimationConstants.buttonPressScale).animate(CurvedAnimation(parent: _animationController, curve: AnimationConstants.microInteractionCurve));

    _fadeAnimation = Tween<double>(begin: 1.0, end: 0.8).animate(CurvedAnimation(parent: _animationController, curve: AnimationConstants.microInteractionCurve));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _handleTapDown(TapDownDetails details) {
    if (!widget.isLoading) {
      setState(() => _isPressed = true);
      _animationController.forward();
    }
  }

  void _handleTapUp(TapUpDetails details) {
    if (!widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  void _handleTapCancel() {
    if (!widget.isLoading) {
      setState(() => _isPressed = false);
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: AnimatedOpacity(
            opacity: _fadeAnimation.value,
            duration: AnimationConstants.microInteractionDuration,
            child: GestureDetector(
              onTapDown: _handleTapDown,
              onTapUp: _handleTapUp,
              onTapCancel: _handleTapCancel,
              child: AnimatedContainer(
                duration: AnimationConstants.microInteractionDuration,
                curve: AnimationConstants.microInteractionCurve,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20), // Material 3 corner radius
                  boxShadow:
                      _isPressed ? [BoxShadow(color: colorScheme.primary.withValues(alpha: 0.2), blurRadius: 8, offset: const Offset(0, 2))] : [BoxShadow(color: colorScheme.shadow.withValues(alpha: 0.1), blurRadius: 4, offset: const Offset(0, 2))],
                ),
                child: OutlinedButton.icon(
                  onPressed: widget.isLoading ? null : widget.onPressed,
                  style: OutlinedButton.styleFrom(
                    backgroundColor: widget.isLoading ? colorScheme.surfaceContainerHighest : colorScheme.surface,
                    foregroundColor: widget.isLoading ? colorScheme.onSurface.withValues(alpha: 0.6) : colorScheme.onSurface,
                    side: BorderSide(
                      color:
                          widget.isLoading
                              ? colorScheme.outline.withValues(alpha: 0.6)
                              : _isPressed
                              ? colorScheme.primary
                              : colorScheme.outline,
                      width: _isPressed ? 2.0 : 1.0,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(20), // Material 3 corner radius
                    ),
                    padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                    minimumSize: const Size(280, 48), // Material 3 minimum touch target
                    textStyle: theme.textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w500, letterSpacing: 0.1),
                    // Material 3 state layer effects
                    splashFactory: InkRipple.splashFactory,
                    enableFeedback: true,
                  ),
                  icon: AnimatedSwitcher(
                    duration: AnimationConstants.stateTransitionDuration,
                    transitionBuilder: (Widget child, Animation<double> animation) {
                      return FadeTransition(opacity: animation, child: child);
                    },
                    child:
                        widget.isLoading
                            ? SizedBox(key: const ValueKey('loading'), width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2.5, valueColor: AlwaysStoppedAnimation<Color>(colorScheme.onSurface.withValues(alpha: 0.6))))
                            : FaIcon(
                              key: const ValueKey('google'),
                              FontAwesomeIcons.google,
                              color: Colors.red, // Google brand color
                              size: 20,
                            ),
                  ),
                  label: AnimatedDefaultTextStyle(
                    duration: AnimationConstants.stateTransitionDuration,
                    style: theme.textTheme.labelLarge?.copyWith(color: widget.isLoading ? colorScheme.onSurface.withValues(alpha: 0.6) : colorScheme.onSurface, fontWeight: FontWeight.w500, letterSpacing: 0.1) ?? const TextStyle(),
                    child: Text(widget.isLoading ? 'Iniciando sesión...' : 'Iniciar sesión con Google', semanticsLabel: widget.isLoading ? 'Iniciando sesión con Google, por favor espere' : 'Botón para iniciar sesión con Google'),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

import 'package:flutter/foundation.dart';
import 'package:buscafarma/core/providers/dio_provider.dart';
import 'package:buscafarma/features/map/data/repositories/minsa_repository_impl.dart';
import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';
import 'package:buscafarma/features/map/domain/repositories/minsa_repository.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/review/presentation/providers/review_manager_provider.dart';

// Provider para el repositorio MINSA
final minsaRepositoryProvider = Provider<MinsaRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return MinsaRepositoryImpl(dio: dio);
});

// Provider para el estado de búsqueda de medicamentos
class MedicineSearchState {
  final String query;
  final AsyncValue<List<Product>> searchResults;
  final bool isSearching;

  MedicineSearchState({this.query = '', this.searchResults = const AsyncValue.data([]), this.isSearching = false});

  MedicineSearchState copyWith({String? query, AsyncValue<List<Product>>? searchResults, bool? isSearching}) {
    return MedicineSearchState(query: query ?? this.query, searchResults: searchResults ?? this.searchResults, isSearching: isSearching ?? this.isSearching);
  }
}

class MedicineSearchNotifier extends StateNotifier<MedicineSearchState> {
  final MinsaRepository _repository;
  final Ref _ref;

  MedicineSearchNotifier(this._repository, this._ref) : super(MedicineSearchState());

  Future<void> searchMedicines(String query) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(query: query, searchResults: const AsyncValue.data([]), isSearching: false);
      return;
    }

    state = state.copyWith(query: query, searchResults: const AsyncValue.loading(), isSearching: true);

    try {
      final results = await _repository.searchProducts(query);
      state = state.copyWith(searchResults: AsyncValue.data(results), isSearching: false);

      // Record successful search for review tracking (only if results found)
      if (results.isNotEmpty) {
        try {
          final reviewManager = _ref.read(reviewManagerProvider.notifier);
          await reviewManager.recordSuccessfulSearch(metadata: {'search_query': query, 'results_count': results.length.toString(), 'timestamp': DateTime.now().toIso8601String()});
        } catch (e) {
          // Don't fail the search if review tracking fails
          debugPrint('Failed to record successful search interaction for review: $e');
        }
      }
    } catch (e) {
      state = state.copyWith(searchResults: AsyncValue.error(e, StackTrace.current), isSearching: false);
    }
  }

  void clearSearch() {
    state = MedicineSearchState();
  }
}

// Provider para el estado de búsqueda de medicamentos
final medicineSearchProvider = StateNotifierProvider<MedicineSearchNotifier, MedicineSearchState>((ref) {
  final repository = ref.watch(minsaRepositoryProvider);
  return MedicineSearchNotifier(repository, ref);
});

// Provider para el medicamento seleccionado
final selectedMedicineProvider = StateProvider<Product?>((ref) => null);

// Provider para obtener detalles de un medicamento específico
final medicineDetailProvider = FutureProvider.family<ProductDetail?, Map<String, dynamic>>((ref, params) async {
  final productCode = params['productCode'] as int;
  final establishmentCode = params['establishmentCode'] as String;

  final repository = ref.watch(minsaRepositoryProvider);
  return repository.getProductDetail(productCode, establishmentCode);
});

// Provider para obtener precios de un medicamento
final medicinePricesProvider = FutureProvider.family<List<ProductPrice>, Map<String, dynamic>>((ref, params) async {
  final repository = ref.watch(minsaRepositoryProvider);

  return repository.findProductPrices(
    productCode: params['productCode'] as int,
    productName: params['productName'] as String?,
    departmentCode: params['departmentCode'] as String,
    provinceCode: params['provinceCode'] as String,
    districtCode: params['districtCode'] as String,
    groupCode: params['groupCode'] as String,
    concentration: params['concentration'] as String,
    establishmentName: params['establishmentName'] as String?,
    laboratoryName: params['laboratoryName'] as String?,
    catEstablecimiento: params['establishmentCategory'] as String?,
    codTipoEstablecimiento: params['establishmentType'] as String?,
    page: params['page'] as int?,
    pageSize: params['pageSize'] as int?,
  );
});

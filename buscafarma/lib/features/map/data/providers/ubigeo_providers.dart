import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/map/data/repositories/ubigeo_repository_impl.dart';
import 'package:buscafarma/features/map/domain/entities/ubigeo_models.dart';
import 'package:buscafarma/features/map/domain/repositories/ubigeo_repository.dart';

// Repository provider
final ubigeoRepositoryProvider = Provider<UbigeoRepository>((ref) {
  return UbigeoRepositoryImpl();
});

// Provider for data initialization
final ubigeoInitProvider = FutureProvider<void>((ref) async {
  final repository = ref.watch(ubigeoRepositoryProvider);
  await repository.loadData();
  return;
});

// Providers for location data
final departmentsProvider = Provider<List<Department>>((ref) {
  // Make sure data is initialized first
  ref.watch(ubigeoInitProvider);

  final repository = ref.watch(ubigeoRepositoryProvider);
  try {
    return repository.getDepartments();
  } catch (e) {
    print('Error in departmentsProvider: $e');
    return [];
  }
});

final provincesProvider = Provider.family<List<Province>, String>((ref, departmentCode) {
  // Make sure data is initialized first
  ref.watch(ubigeoInitProvider);

  final repository = ref.watch(ubigeoRepositoryProvider);
  try {
    return repository.getProvincesByDepartment(departmentCode);
  } catch (e) {
    print('Error in provincesProvider: $e');
    return [];
  }
});

final districtsProvider = Provider.family<List<District>, String>((ref, provinceCode) {
  // Make sure data is initialized first
  ref.watch(ubigeoInitProvider);

  final repository = ref.watch(ubigeoRepositoryProvider);
  try {
    return repository.getDistrictsByProvince(provinceCode);
  } catch (e) {
    print('Error in districtsProvider: $e');
    return [];
  }
});

// Search providers
class LocationSearchState {
  final String query;
  final List<Department> departments;
  final List<Province> provinces;
  final List<District> districts;

  LocationSearchState({this.query = '', this.departments = const [], this.provinces = const [], this.districts = const []});

  LocationSearchState copyWith({String? query, List<Department>? departments, List<Province>? provinces, List<District>? districts}) {
    return LocationSearchState(query: query ?? this.query, departments: departments ?? this.departments, provinces: provinces ?? this.provinces, districts: districts ?? this.districts);
  }
}

class LocationSearchNotifier extends StateNotifier<LocationSearchState> {
  final UbigeoRepository _repository;

  LocationSearchNotifier(this._repository) : super(LocationSearchState());

  void searchLocations(String query) {
    if (query.isEmpty) {
      state = LocationSearchState();
      return;
    }

    try {
      final departments = _repository.searchDepartments(query);
      final provinces = _repository.searchProvinces(query);
      final districts = _repository.searchDistricts(query);

      state = state.copyWith(query: query, departments: departments, provinces: provinces, districts: districts);
    } catch (e) {
      print('Error searching locations: $e');
      state = state.copyWith(query: query);
    }
  }

  void clearSearch() {
    state = LocationSearchState();
  }
}

final locationSearchProvider = StateNotifierProvider<LocationSearchNotifier, LocationSearchState>((ref) {
  // Make sure data is initialized first
  ref.watch(ubigeoInitProvider);

  final repository = ref.watch(ubigeoRepositoryProvider);
  return LocationSearchNotifier(repository);
});

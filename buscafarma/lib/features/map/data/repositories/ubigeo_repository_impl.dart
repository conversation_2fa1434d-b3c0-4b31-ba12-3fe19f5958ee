import 'dart:convert';
import 'package:flutter/services.dart';
import 'package:buscafarma/features/map/domain/entities/ubigeo_models.dart';
import 'package:buscafarma/features/map/domain/repositories/ubigeo_repository.dart';

class UbigeoRepositoryImpl implements UbigeoRepository {
  UbigeoResponse? _ubigeoData;
  List<Department> _departments = [];
  List<Province> _allProvinces = [];
  List<District> _allDistricts = [];
  String _selectedDepartmentCode = '';
  bool _isInitialized = false;

  @override
  Future<void> loadData() async {
    if (_isInitialized) return;

    try {
      // Load JSON file from assets
      final jsonString = await rootBundle.loadString('assets/json/ubigeo.json');
      final jsonData = json.decode(jsonString);

      // Parse JSON to UbigeoResponse
      _ubigeoData = UbigeoResponse.fromJson(jsonData);
      _departments = _ubigeoData?.data ?? [];

      // Extract all provinces and districts for easier searching
      _allProvinces = [];
      _allDistricts = [];

      for (final department in _departments) {
        _allProvinces.addAll(department.provincias);

        for (final province in department.provincias) {
          if (province.distritos != null) {
            _allDistricts.addAll(province.distritos!);
          }
        }
      }

      _isInitialized = true;
    } catch (e) {
      print('Error loading ubigeo data: $e');
      throw Exception('Failed to load ubigeo data');
    }
  }

  @override
  List<Department> getDepartments() {
    _checkInitialized();
    return List.from(_departments);
  }

  @override
  List<Province> getProvincesByDepartment(String departmentCode) {
    _checkInitialized();
    _selectedDepartmentCode = departmentCode;
    final department = _departments.firstWhere((dept) => dept.codigo == departmentCode, orElse: () => Department(codigo: '', descripcion: '', provincias: []));

    return List.from(department.provincias);
  }

  @override
  List<District> getDistrictsByProvince(String provinceCode) {
    _checkInitialized();
    for (final department in _departments) {
      for (final province in department.provincias) {
        if (province.codigo == provinceCode && province.codigoDepartamento == _selectedDepartmentCode && province.distritos != null) {
          return List.from(province.distritos!);
        }
      }
    }

    return [];
  }

  @override
  List<Department> searchDepartments(String query) {
    _checkInitialized();

    if (query.isEmpty) return List.from(_departments);

    final normalizedQuery = _normalizeString(query);
    return _departments.where((dept) {
      final normalizedName = _normalizeString(dept.descripcion);
      return normalizedName.contains(normalizedQuery);
    }).toList();
  }

  @override
  List<Province> searchProvinces(String query) {
    _checkInitialized();

    if (query.isEmpty) return List.from(_allProvinces);

    final normalizedQuery = _normalizeString(query);
    return _allProvinces.where((province) {
      final normalizedName = _normalizeString(province.descripcion);
      return normalizedName.contains(normalizedQuery);
    }).toList();
  }

  @override
  List<District> searchDistricts(String query) {
    _checkInitialized();

    if (query.isEmpty) return List.from(_allDistricts);

    final normalizedQuery = _normalizeString(query);
    return _allDistricts.where((district) {
      final normalizedName = _normalizeString(district.descripcion);
      return normalizedName.contains(normalizedQuery);
    }).toList();
  }

  // Utility methods
  void _checkInitialized() {
    if (!_isInitialized) {
      throw Exception('UbigeoRepository not initialized. Call loadData() first.');
    }
  }

  String _normalizeString(String input) {
    return input.toLowerCase().replaceAll('á', 'a').replaceAll('é', 'e').replaceAll('í', 'i').replaceAll('ó', 'o').replaceAll('ú', 'u').replaceAll('ü', 'u').replaceAll('ñ', 'n');
  }
}

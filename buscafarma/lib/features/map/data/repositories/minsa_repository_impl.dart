import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';
import 'package:buscafarma/features/map/domain/repositories/minsa_repository.dart';
import 'package:buscafarma/core/utils/api_config.dart';
import 'package:dio/dio.dart';

class MinsaRepositoryImpl implements MinsaRepository {
  final Dio _dio;
  final String baseUrl = "https://ms-opm.minsa.gob.pe/msopmcovid";

  MinsaRepositoryImpl({Dio? dio})
    : _dio =
          dio ?? Dio(BaseOptions(connectTimeout: Duration(seconds: ApiConfig.timeoutSeconds), receiveTimeout: Duration(seconds: ApiConfig.timeoutSeconds)))
            ..options.headers = {"Content-Type": "application/json", "Origin": "https://opm-digemid.minsa.gob.pe", "Referer": "https://opm-digemid.minsa.gob.pe/"};

  @override
  Future<List<Product>> searchProducts(String query, {int page = 1, int pageSize = 10}) async {
    try {
      final response = await _dio.post(
        "$baseUrl/producto/autocompleteciudadano",
        data: {
          "filtro": {"nombreProducto": query, "pagina": page, "tamanio": pageSize, "tokenGoogle": ""},
        },
      );

      final minsaResponse = MinsaResponse<Product>.fromJson(response.data as Map<String, dynamic>, (json) => Product.fromJson(json as Map<String, dynamic>));

      return minsaResponse.data;
    } on DioException catch (e) {
      print('Error searching products: ${e.message}');
      return [];
    } catch (e) {
      print('Unexpected error: $e');
      return [];
    }
  }

  @override
  Future<List<ProductPrice>> findProductPrices({
    required int productCode,
    required String departmentCode,
    required String provinceCode,
    required String districtCode,
    String? establishmentName,
    String? laboratoryName,
    String? catEstablecimiento,
    String? codTipoEstablecimiento,
    required String groupCode,
    required String concentration,
    int? page,
    int? pageSize,
    String? productName,
  }) async {
    try {
      final response = await _dio.post(
        "$baseUrl/preciovista/ciudadano",
        data: {
          "filtro": {
            "codigoProducto": productCode,
            "codigoDepartamento": departmentCode,
            "codigoProvincia": provinceCode,
            "codigoUbigeo": districtCode,
            "codTipoEstablecimiento": codTipoEstablecimiento,
            "catEstablecimiento": catEstablecimiento,
            "nombreEstablecimiento": establishmentName,
            "nombreLaboratorio": laboratoryName,
            "codGrupoFF": groupCode,
            "concent": concentration,
            "tamanio": pageSize ?? 100,
            "pagina": page ?? 1,
            "nombreProducto": productName,
          },
        },
      );

      final minsaResponse = MinsaResponse<ProductPrice>.fromJson(response.data as Map<String, dynamic>, (json) => ProductPrice.fromJson(json as Map<String, dynamic>));

      return minsaResponse.data;
    } on DioException catch (e) {
      print('Error finding product prices: ${e.message}');
      return [];
    } catch (e) {
      print('Unexpected error: $e');
      return [];
    }
  }

  @override
  Future<ProductDetail?> getProductDetail(int productCode, String establishmentCode) async {
    try {
      final response = await _dio.post(
        "$baseUrl/precioproducto/obtener",
        data: {
          "filtro": {"codigoProducto": productCode, "codEstablecimiento": establishmentCode},
        },
      );

      final minsaResponse = MinsaResponse<ProductDetail>.fromJson(response.data as Map<String, dynamic>, (json) => ProductDetail.fromJson(json as Map<String, dynamic>));

      return minsaResponse.entidad;
    } on DioException catch (e) {
      print('Error getting product detail: ${e.message}');
      return null;
    } catch (e) {
      print('Unexpected error: $e');
      return null;
    }
  }
}

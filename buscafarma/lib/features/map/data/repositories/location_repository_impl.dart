import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:buscafarma/core/error/failures.dart';
import 'package:buscafarma/features/map/domain/entities/location.dart';
import 'package:buscafarma/features/map/domain/repositories/location_repository.dart';

class LocationRepositoryImpl implements LocationRepository {
  @override
  Future<Either<Failure, Location>> getCurrentLocation() async {
    try {
      if (!await isLocationServiceEnabled()) {
        return left(const Failure.locationServiceDisabled());
      }

      if (!await requestLocationPermission()) {
        return left(const Failure.locationPermissionDenied());
      }

      final position = await Geolocator.getCurrentPosition(locationSettings: const LocationSettings(accuracy: LocationAccuracy.high));

      return right(Location(latitude: position.latitude, longitude: position.longitude));
    } catch (e) {
      return left(Failure.unexpectedError(e.toString()));
    }
  }

  @override
  Future<bool> isLocationServiceEnabled() async {
    return await Geolocator.isLocationServiceEnabled();
  }

  @override
  Future<bool> requestLocationPermission() async {
    // En macOS, usamos directamente Geolocator para los permisos
    if (defaultTargetPlatform == TargetPlatform.macOS) {
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
      }
      return permission == LocationPermission.always || permission == LocationPermission.whileInUse;
    }
    // En otras plataformas, seguimos usando permission_handler
    else {
      final permission = await Permission.location.request();
      return permission.isGranted;
    }
  }
}

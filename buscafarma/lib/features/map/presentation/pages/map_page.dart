import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/map/presentation/providers/map_provider.dart';
import 'package:buscafarma/features/map/presentation/widgets/floating_search_bar.dart';
import 'package:buscafarma/features/map/presentation/widgets/search_suggestions_panel.dart';
import 'package:buscafarma/features/map/presentation/providers/establishment_markers_provider.dart';
import 'package:buscafarma/features/map/presentation/widgets/establishment_details_sheet.dart';
import 'package:buscafarma/core/widgets/ads/interstitial_ad_manager.dart';
import 'package:latlong2/latlong.dart';
import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';
import 'package:buscafarma/features/map/data/providers/minsa_providers.dart';
import 'package:buscafarma/core/providers/announcement_provider.dart';
import 'package:buscafarma/features/auth/presentation/providers/auth_provider.dart';

// Provider to track loading state for markers
final markerLoadingProvider = StateProvider<bool>((ref) => false);

// Provider to track whether geocoding should be cancelled
final geocodingCancellationProvider = StateProvider<bool>((ref) => false);

// Provider to track geocoding progress (from 0.0 to 1.0)
final geocodingProgressProvider = StateProvider<double>((ref) => 0.0);

// Provider to track total and processed items
final geocodingCountsProvider = StateProvider<({int total, int processed})>((ref) => (total: 0, processed: 0));

class MapPage extends ConsumerStatefulWidget {
  const MapPage({super.key});

  @override
  ConsumerState<MapPage> createState() => _MapPageState();
}

class _MapPageState extends ConsumerState<MapPage> with SingleTickerProviderStateMixin {
  final MapController _mapController = MapController();
  bool _isSearchPanelVisible = false;
  bool _isMapControllerReady = false;

  // Static variable to prevent multiple simultaneous announcements
  static bool _isAnnouncementInProgress = false;

  @override
  void initState() {
    super.initState();

    // Check for first-launch announcement after the page is fully built
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _checkAndShowFirstLaunchAnnouncement();
    });
  }

  /// Check if this is the first launch and show announcement if needed
  /// Only shows for authenticated users and prevents multiple simultaneous announcements
  Future<void> _checkAndShowFirstLaunchAnnouncement() async {
    try {
      if (!mounted) return;

      // Prevent multiple simultaneous announcements
      if (_isAnnouncementInProgress) {
        debugPrint('Announcement already in progress, skipping...');
        return;
      }

      // Check if user is authenticated - only show for logged-in users
      final authState = ref.read(authProvider);
      if (!authState.isAuthenticated) {
        debugPrint('User not authenticated, skipping announcement');
        return;
      }

      final announcementService = ref.read(announcementServiceProvider);
      final isFirstLaunch = await announcementService.isFirstLaunch();

      if (isFirstLaunch && mounted && !_isAnnouncementInProgress) {
        // Mark announcement as in progress
        _isAnnouncementInProgress = true;

        try {
          // Small delay to ensure the page is fully loaded
          await Future.delayed(const Duration(milliseconds: 300));

          if (mounted) {
            await announcementService.showFirstLaunchAnnouncement(context);
          }
        } finally {
          // Always reset the flag when done
          _isAnnouncementInProgress = false;
        }
      }
    } catch (e) {
      // Graceful fallback - don't break the app if announcement fails
      _isAnnouncementInProgress = false;
      debugPrint('Failed to show first launch announcement: $e');
    }
  }

  // Execute map operations with retry mechanism
  void _retryMapOperation(void Function() operation, {int maxRetries = 5}) {
    if (!_isMapControllerReady || !mounted) return;

    int retryCount = 0;

    void attemptOperation() {
      // Stop if component is no longer mounted
      if (!mounted) return;

      try {
        operation();
      } catch (e) {
        print('Error performing map operation (attempt ${retryCount + 1}): $e');

        // Retry with increasing delay if we haven't reached max retries
        if (retryCount < maxRetries) {
          retryCount++;
          // Exponential backoff: 50ms, 100ms, 200ms, 400ms, 800ms
          final delay = Duration(milliseconds: 50 * (1 << retryCount));
          Future.delayed(delay, attemptOperation);
        } else {
          print('Failed to perform map operation after $maxRetries retries');
        }
      }
    }

    // Start with a small initial delay to allow the map to initialize
    Future.delayed(const Duration(milliseconds: 100), attemptOperation);
  }

  void _showSearchPanel() {
    if (!_isSearchPanelVisible) {
      setState(() {
        _isSearchPanelVisible = true;
      });
    }
  }

  void _hideSearchPanel() {
    if (_isSearchPanelVisible) {
      setState(() {
        _isSearchPanelVisible = false;
      });
    }
  }

  @override
  void dispose() {
    _mapController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final mapState = ref.watch(mapProvider);
    final topPadding = MediaQuery.of(context).padding.top;
    final establishmentMarkersState = ref.watch(establishmentMarkersProvider);
    final establishmentMarkers = establishmentMarkersState.markers;
    final isLoading = ref.watch(markerLoadingProvider);
    final progress = ref.watch(geocodingProgressProvider);
    final counts = ref.watch(geocodingCountsProvider);

    ref.listen(mapProvider, (previous, current) {
      if (previous?.currentLocation != current.currentLocation && _isMapControllerReady) {
        current.currentLocation.whenData((location) {
          _retryMapOperation(() {
            _mapController.move(location.toLatLng(), current.zoom);
          });
        });
      }
    });

    // Listen to establishment markers changes to center map on them
    ref.listen(establishmentMarkersProvider, (previous, current) {
      if (_isMapControllerReady && current.shouldCenterOnMarkers && current.markers.isNotEmpty) {
        _calculateAndSetBounds(current.markers);
      }
    });

    return Scaffold(
      body: Column(
        children: [
          // Main map area
          Expanded(
            child: Stack(
              children: [
                FlutterMap(
                  mapController: _mapController,
                  options: MapOptions(
                    initialCenter: mapState.mapCenter,
                    initialZoom: mapState.zoom,
                    minZoom: 2,
                    maxZoom: 18,
                    onMapReady: () {
                      setState(() {
                        _isMapControllerReady = true;

                        // Check if we already have markers to show using our retry operation method
                        if (establishmentMarkers.isNotEmpty && establishmentMarkersState.shouldCenterOnMarkers) {
                          _calculateAndSetBounds(establishmentMarkers);
                        }
                      });
                    },
                    onPositionChanged: (position, hasGesture) {
                      if (hasGesture) {
                        final center = position.center;
                        ref.read(mapProvider.notifier).setMapCenter(center);
                      }
                    },
                  ),
                  children: [
                    TileLayer(urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png'),
                    // Capa para la ubicación actual
                    mapState.currentLocation.when(
                      data: (location) => MarkerLayer(markers: [Marker(point: location.toLatLng(), width: 80, height: 80, child: const _CurrentLocationMarker())]),
                      loading: () => const SizedBox(),
                      error: (_, __) => const SizedBox(),
                    ),
                    // Capa para los marcadores de establecimientos
                    if (establishmentMarkers.isNotEmpty)
                      MarkerLayer(
                        markers:
                            establishmentMarkers.map((marker) {
                              return Marker(point: marker.location, width: 60.0, height: 60.0, child: _PharmacyMarker(price: marker.price, location: marker.location, onTap: () => _showEstablishmentDetails(context, marker.price, marker.location)));
                            }).toList(),
                      ),
                  ],
                ),
                Positioned(
                  top: topPadding + 16.0,
                  left: 16.0,
                  right: 16.0,
                  width: null,
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      FloatingSearchBar(
                        onTap: _showSearchPanel,
                        isPanelVisible: _isSearchPanelVisible,
                        onClearSelection: () {
                          ref.read(geocodingCancellationProvider.notifier).state = true;

                          // Redundant if FloatingSearchBar already does this, but safe:
                          ref.read(selectedMedicineProvider.notifier).state = null;
                          ref.read(establishmentMarkersProvider.notifier).clearMarkers();
                          ref.read(markerLoadingProvider.notifier).state = false;
                        },
                      ),
                      // Loading indicator below search bar - only visible when loading
                      if (isLoading)
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Progress info text
                            Container(
                              margin: const EdgeInsets.only(top: 2.0, left: 8.0),
                              child: Text('Cargando ${counts.processed}/${counts.total} ubicaciones...', style: TextStyle(fontSize: 10.0, color: Theme.of(context).colorScheme.onSurfaceVariant)),
                            ),
                            // Progress bar
                            Container(
                              margin: const EdgeInsets.only(top: 2.0, bottom: 3.0),
                              width: double.infinity,
                              height: 3.0,
                              decoration: BoxDecoration(borderRadius: BorderRadius.circular(1.5)),
                              child: LinearProgressIndicator(
                                backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest.withAlpha(128),
                                color: Theme.of(context).colorScheme.primary,
                                value: progress, // Use actual progress value
                                borderRadius: BorderRadius.circular(1.5),
                              ),
                            ),
                          ],
                        ),
                    ],
                  ),
                ),

                if (_isSearchPanelVisible) Positioned(top: 0, left: 0, right: 0, bottom: 0, width: null, child: SearchSuggestionsPanel(onClose: _hideSearchPanel, topPadding: topPadding)),

                // Botón para limpiar marcadores (solo visible si hay marcadores)
                if (establishmentMarkers.isNotEmpty)
                  Positioned(
                    bottom: 80.0, // Back to original position since banner is now in MainNavigationPage
                    right: 16.0,
                    child: FloatingActionButton.small(
                      heroTag: "clearMarkersBtn",
                      backgroundColor: Theme.of(context).colorScheme.errorContainer,
                      child: const Icon(Icons.clear_all),
                      onPressed: () {
                        // Signal cancellation before clearing state
                        ref.read(geocodingCancellationProvider.notifier).state = true;

                        // Clear markers and state
                        ref.read(establishmentMarkersProvider.notifier).clearMarkers();
                        ref.read(selectedMedicineProvider.notifier).state = null;
                        ref.read(markerLoadingProvider.notifier).state = false;

                        // Show feedback for cancellation
                        ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Búsqueda cancelada y marcadores limpiados'), duration: Duration(seconds: 1), behavior: SnackBarBehavior.floating));
                      },
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
      floatingActionButton:
          _isSearchPanelVisible
              ? null
              : FloatingActionButton(
                heroTag: "myLocationBtn",
                onPressed: () {
                  ref.read(mapProvider.notifier).refreshLocation();
                  if (_isMapControllerReady) {
                    mapState.currentLocation.whenData((location) {
                      _retryMapOperation(() {
                        _mapController.move(location.toLatLng(), mapState.zoom);
                      });
                    });
                  }
                },
                child: const Icon(Icons.my_location),
              ),
    );
  }

  // Helper function to calculate a zoom level that can show a given span
  double _calculateZoomLevel(double span) {
    // Nueva fórmula para calcular el zoom
    // Valor base más alto y factor multiplicador menor para un zoom más cercano
    // Valor base 16.0 (en vez de 14.0) y multiplicador 50 (en vez de 100)
    double zoom = 16.0 - (span * 50);

    // Asegurar que el zoom nunca sea menor que 13.0 (para evitar zoom muy lejano)
    return zoom < 13.0 ? 13.0 : zoom;
  }

  // Helper method to safely calculate bounds when markers change
  void _calculateAndSetBounds(List<EstablishmentMarker> markers) {
    if (markers.isEmpty || !_isMapControllerReady || !mounted) return;

    // First mark that we've centered on these markers regardless of success
    ref.read(establishmentMarkersProvider.notifier).markCentered();

    _retryMapOperation(() {
      if (markers.length == 1) {
        // Si hay un solo marcador, centrar en u00e9l con un nivel de zoom mayor (mu00e1s cercano)
        // Cambiar 15.0 a 16.5 para un zoom mu00e1s cercano
        _mapController.move(markers.first.location, 16.5);
      } else {
        // If there are multiple markers, calculate the bounds to fit them all
        double minLat = 90.0;
        double maxLat = -90.0;
        double minLng = 180.0;
        double maxLng = -180.0;

        for (final marker in markers) {
          if (marker.location.latitude < minLat) minLat = marker.location.latitude;
          if (marker.location.latitude > maxLat) maxLat = marker.location.latitude;
          if (marker.location.longitude < minLng) minLng = marker.location.longitude;
          if (marker.location.longitude > maxLng) maxLng = marker.location.longitude;
        }

        // Add padding
        minLat -= 0.01;
        maxLat += 0.01;
        minLng -= 0.01;
        maxLng += 0.01;

        // Create a center point that is the average of all markers
        final centerLat = (minLat + maxLat) / 2;
        final centerLng = (minLng + maxLng) / 2;

        // Calculate zoom level to fit all markers
        final latZoom = _calculateZoomLevel(maxLat - minLat);
        final lngZoom = _calculateZoomLevel(maxLng - minLng);

        // Use the smaller zoom level to ensure all markers are visible
        final zoomLevel = latZoom < lngZoom ? latZoom : lngZoom;

        // Move to the center with the calculated zoom
        _mapController.move(LatLng(centerLat, centerLng), zoomLevel);
      }
    });
  }

  void _showEstablishmentDetails(BuildContext context, ProductPrice price, LatLng location) {
    // Obtenemos la altura de la pantalla para calcular tamaños relativos
    final screenHeight = MediaQuery.of(context).size.height;
    final topPadding = MediaQuery.of(context).padding.top;
    final bottomPadding = MediaQuery.of(context).padding.bottom;
    final availableHeight = screenHeight - topPadding - bottomPadding;

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      isDismissible: true,
      enableDrag: true,
      backgroundColor: Colors.transparent,
      barrierColor: Colors.black54, // Barrera visible y clickeable
      builder: (context) {
        return StatefulBuilder(
          builder: (BuildContext context, StateSetter setState) {
            return GestureDetector(
              onVerticalDragEnd: (details) {
                // Si el gesto fue hacia abajo con suficiente velocidad, cerramos el modal
                if (details.velocity.pixelsPerSecond.dy > 200) {
                  Navigator.of(context).pop();
                }
              },
              child: Container(
                // Máximo 90% de la altura disponible de la pantalla
                constraints: BoxConstraints(maxHeight: availableHeight * 0.9),
                margin: EdgeInsets.only(
                  top: topPadding + 20, // Espacio en la parte superior
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Contenedor principal con el contenido
                    Flexible(
                      child: Container(
                        decoration: BoxDecoration(color: Theme.of(context).colorScheme.surface, borderRadius: const BorderRadius.vertical(top: Radius.circular(20))),
                        child: ClipRRect(
                          borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              // Barra de arrastre dentro del contenedor
                              Padding(
                                padding: const EdgeInsets.only(top: 12.0, bottom: 8.0),
                                child: Container(height: 5, width: 40, decoration: BoxDecoration(color: Theme.of(context).colorScheme.onSurfaceVariant.withAlpha(102), borderRadius: BorderRadius.circular(2.5))),
                              ),
                              // Contenido principal con scroll
                              Flexible(
                                child: SingleChildScrollView(
                                  // Aquí no hay controlador para no interferir con el arrastre
                                  physics: const AlwaysScrollableScrollPhysics(),
                                  padding: EdgeInsets.only(bottom: MediaQuery.of(context).viewInsets.bottom + 16),
                                  child: EstablishmentDetailsSheet(establishment: price, location: location),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            );
          },
        );
      },
    ).then((_) {
      // Show interstitial ad when establishment details are closed
      InterstitialAdManager.showAfterEstablishmentDetailsClosure(ref);
    });
  }
}

class _CurrentLocationMarker extends StatelessWidget {
  const _CurrentLocationMarker();

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Stack(
      children: [
        Center(child: Container(width: 24, height: 24, decoration: BoxDecoration(color: colorScheme.primary.withAlpha(76), shape: BoxShape.circle))),
        Center(child: Container(width: 12, height: 12, decoration: BoxDecoration(color: colorScheme.primary, shape: BoxShape.circle, border: Border.all(color: colorScheme.onPrimary, width: 2)))),
      ],
    );
  }
}

class _PharmacyMarker extends StatelessWidget {
  final ProductPrice price;
  final LatLng location;
  final VoidCallback onTap;

  const _PharmacyMarker({required this.price, required this.location, required this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Format price for display, using a helper method to handle edge cases
    final displayPrice = _formatPrice(price.precio1);

    return GestureDetector(
      onTap: onTap,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Burbuja con el precio - with width that adapts to content
          Container(
            // Remove width constraints to allow container to size to content
            constraints: const BoxConstraints(
              maxHeight: 22, // Keep only the height constraint
            ),
            padding: const EdgeInsets.symmetric(horizontal: 6.0, vertical: 2.0),
            decoration: BoxDecoration(color: colorScheme.secondaryContainer, borderRadius: BorderRadius.circular(12.0), boxShadow: [BoxShadow(color: Colors.black.withAlpha(40), blurRadius: 2, offset: const Offset(0, 1))]),
            child: Text('S/ $displayPrice', style: theme.textTheme.labelSmall?.copyWith(fontWeight: FontWeight.bold, color: colorScheme.onSecondaryContainer, fontSize: 10.0), textAlign: TextAlign.center, softWrap: false),
          ),
          const SizedBox(height: 1), // Reduced spacing between elements
          // Icono de la farmacia
          Container(
            padding: const EdgeInsets.all(4.0),
            decoration: BoxDecoration(color: colorScheme.primary, shape: BoxShape.circle, boxShadow: [BoxShadow(color: Colors.black.withAlpha(60), blurRadius: 3, offset: const Offset(0, 2))]),
            child: Icon(Icons.local_pharmacy, color: colorScheme.onPrimary, size: 16.0),
          ),
        ],
      ),
    );
  }

  // Helper method to format prices consistently
  String _formatPrice(double price) {
    // For prices with cents that are 0, show as whole number
    if (price == price.roundToDouble()) {
      return price.toStringAsFixed(0);
    }

    // Otherwise show up to 2 decimal places
    return price.toStringAsFixed(2);
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/utils/string_utils.dart';
import 'package:buscafarma/features/geocoding/domain/entities/geocoding_detail_models.dart';
import 'package:buscafarma/features/geocoding/presentation/providers/geocoding_providers.dart';
import 'package:buscafarma/features/map/data/providers/ubigeo_providers.dart';
import 'package:buscafarma/features/map/domain/entities/ubigeo_models.dart';
import 'package:buscafarma/features/map/presentation/providers/map_provider.dart';
import 'package:buscafarma/features/map/presentation/providers/selected_ubigeo_state_provider.dart';

/// Estado que contiene la información de ubicación automática.
class AutoLocationState {
  final bool isLoading;
  final String? errorMessage;
  final GeocodingLocationDetails? locationDetails;
  final Department? matchedDepartment;
  final Province? matchedProvince;
  final District? matchedDistrict;
  final bool hasMatched;

  AutoLocationState({this.isLoading = false, this.errorMessage, this.locationDetails, this.matchedDepartment, this.matchedProvince, this.matchedDistrict, this.hasMatched = false});

  AutoLocationState copyWith({bool? isLoading, String? errorMessage, GeocodingLocationDetails? locationDetails, Department? matchedDepartment, Province? matchedProvince, District? matchedDistrict, bool? hasMatched}) {
    return AutoLocationState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage ?? this.errorMessage,
      locationDetails: locationDetails ?? this.locationDetails,
      matchedDepartment: matchedDepartment ?? this.matchedDepartment,
      matchedProvince: matchedProvince ?? this.matchedProvince,
      matchedDistrict: matchedDistrict ?? this.matchedDistrict,
      hasMatched: hasMatched ?? this.hasMatched,
    );
  }

  AutoLocationState clearError() {
    return copyWith(errorMessage: null);
  }
}

/// Controlador para gestionar la ubicación automática.
class AutoLocationNotifier extends StateNotifier<AutoLocationState> {
  final Ref _ref;

  AutoLocationNotifier(this._ref) : super(AutoLocationState()) {
    _initializeAutoLocation();
  }

  /// Encuentra la mejor coincidencia entre district y suburb usando la distancia de Levenshtein.
  ///
  /// Retorna un objeto con el distrito más cercano y la distancia mínima encontrada.
  ({District district, int distance, String? matchedField}) _findBestDistrictMatch(GeocodingLocationDetails details, List<District> districts) {
    District? bestMatch;
    int minDistance = double.maxFinite.toInt();
    String? matchedField;

    // Función auxiliar para probar un campo contra todos los distritos
    void checkField(String? field, String fieldName) {
      if (field == null) return;

      for (final district in districts) {
        final distance = levenshteinDistance(field, district.descripcion);
        if (distance < minDistance) {
          minDistance = distance;
          bestMatch = district;
          matchedField = fieldName;
        }
      }
    }

    // Probar con district y suburb
    checkField(details.district, 'district');
    checkField(details.suburb, 'suburb');

    // Si no encontramos coincidencia, usar el primer distrito
    if (bestMatch == null && districts.isNotEmpty) {
      bestMatch = districts.first;
      minDistance = double.maxFinite.toInt();
      matchedField = 'default';
    }

    return (district: bestMatch!, distance: minDistance, matchedField: matchedField);
  }

  /// Inicializa la detección automática de ubicación.
  Future<void> _initializeAutoLocation() async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      // Esperar a que los datos de ubigeo estén cargados
      await _ref.read(ubigeoInitProvider.future);

      // Obtener la ubicación actual
      final mapState = _ref.read(mapProvider);

      // Verificar si hay una ubicación disponible
      final locationOrError = await mapState.currentLocation.when(
        data: (location) async => location,
        loading: () async {
          // Esperar a que la ubicación termine de cargarse
          await Future.delayed(const Duration(seconds: 1));
          final newMapState = _ref.read(mapProvider);
          return newMapState.currentLocation.maybeWhen(data: (loc) => loc, orElse: () => null);
        },
        error: (error, _) async => null,
      );

      if (locationOrError == null) {
        state = state.copyWith(isLoading: false, errorMessage: 'No se pudo obtener la ubicación actual.');
        return;
      }

      // Geocodificar las coordenadas para obtener información de ubicación
      final coords = (latitude: locationOrError.latitude, longitude: locationOrError.longitude);
      final geocodingDetails = await _ref.read(reverseGeocodeProvider(coords).future);

      if (geocodingDetails == null) {
        state = state.copyWith(isLoading: false, errorMessage: 'No se pudo geocodificar la ubicación actual.');
        return;
      }

      // Buscar coincidencias en el ubigeo
      await _findMatches(geocodingDetails);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: 'Error al procesar la ubicación: $e');
    }
  }

  /// Busca coincidencias entre los datos de geocoding y el ubigeo.
  Future<void> _findMatches(GeocodingLocationDetails details) async {
    state = state.copyWith(locationDetails: details);

    try {
      // Obtener todos los departamentos, provincias y distritos
      final allDepartments = _ref.read(departmentsProvider);

      if (allDepartments.isEmpty) {
        state = state.copyWith(isLoading: false, errorMessage: 'No hay datos de ubigeo disponibles.');
        return;
      }

      // Buscar coincidencia de departamento usando distancia de Levenshtein
      Department? matchedDepartment;
      if (details.department != null) {
        matchedDepartment = findClosestMatch(details.department!, allDepartments, (dept) => dept.descripcion);
      }

      // Si no se encontró por department, intentar con province
      if (matchedDepartment == null && details.province != null) {
        matchedDepartment = findClosestMatch(details.province!, allDepartments, (dept) => dept.descripcion);
      }

      if (matchedDepartment == null) {
        state = state.copyWith(isLoading: false, errorMessage: 'No se pudo identificar el departamento.');
        return;
      }

      // Obtener provincias del departamento encontrado
      final provinces = _ref.read(provincesProvider(matchedDepartment.codigo));

      // Buscar coincidencia de provincia
      Province? matchedProvince;
      if (details.province != null) {
        matchedProvince = findClosestMatch(details.province!, provinces, (prov) => prov.descripcion);
      }

      if (matchedProvince == null && (details.district != null || details.suburb != null)) {
        // Intentar buscar por distrito o suburb en cada provincia
        Province? bestProvince;
        int bestProvinceDistance = double.maxFinite.toInt();

        for (final province in provinces) {
          if (province.distritos != null && province.distritos!.isNotEmpty) {
            // Buscar la mejor coincidencia entre district y suburb
            final bestMatch = _findBestDistrictMatch(details, province.distritos!);

            if (bestMatch.distance < bestProvinceDistance) {
              bestProvinceDistance = bestMatch.distance;
              bestProvince = province;
            }
          }
        }

        if (bestProvince != null) {
          matchedProvince = bestProvince;
        }
      }

      // Si no encontramos provincia, usar la primera del departamento
      if (matchedProvince == null && provinces.isNotEmpty) {
        matchedProvince = provinces.first;
      }

      // Buscar el distrito más cercano dentro de la provincia seleccionada
      District? matchedDistrict;
      if (matchedProvince != null && matchedProvince.distritos != null && matchedProvince.distritos!.isNotEmpty && (details.district != null || details.suburb != null)) {
        // Encontrar la mejor coincidencia entre district y suburb
        final bestMatch = _findBestDistrictMatch(details, matchedProvince.distritos!);
        matchedDistrict = bestMatch.district;
      }

      // Actualizar el estado con las coincidencias encontradas
      state = state.copyWith(isLoading: false, matchedDepartment: matchedDepartment, matchedProvince: matchedProvince, matchedDistrict: matchedDistrict, hasMatched: true);

      // Aplicar la selección automática al proveedor de Ubigeo
      final selectedUbigeoNotifier = _ref.read(selectedUbigeoProvider.notifier);

      await selectedUbigeoNotifier.selectDepartment(matchedDepartment);

      if (matchedProvince != null) {
        await selectedUbigeoNotifier.selectProvince(matchedProvince);

        if (matchedDistrict != null) {
          selectedUbigeoNotifier.selectDistrict(matchedDistrict);
        }
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: 'Error al buscar coincidencias: $e');
    }
  }

  /// Reinicia el proceso de detección automática de ubicación.
  Future<void> refreshAutoLocation() async {
    state = AutoLocationState(isLoading: true);
    await _initializeAutoLocation();
  }
}

/// Proveedor para la ubicación automática.
final autoLocationProvider = StateNotifierProvider<AutoLocationNotifier, AutoLocationState>((ref) {
  return AutoLocationNotifier(ref);
});

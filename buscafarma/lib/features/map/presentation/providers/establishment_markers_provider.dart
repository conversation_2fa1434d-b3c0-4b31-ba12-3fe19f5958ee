import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart';
import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';

// Marker for a pharmacy or establishment with medicine
class EstablishmentMarker {
  final ProductPrice price;
  final LatLng location;

  EstablishmentMarker({required this.price, required this.location});
}

class EstablishmentMarkersState {
  final List<EstablishmentMarker> markers;
  final bool shouldCenterOnMarkers;

  EstablishmentMarkersState({this.markers = const [], this.shouldCenterOnMarkers = false});
}

// Notifier to manage establishment markers
class EstablishmentMarkersNotifier extends StateNotifier<EstablishmentMarkersState> {
  EstablishmentMarkersNotifier() : super(EstablishmentMarkersState());

  void setMarkers(List<EstablishmentMarker> markers) {
    state = EstablishmentMarkersState(markers: markers, shouldCenterOnMarkers: true);
  }

  void addMarker(EstablishmentMarker marker) {
    // Create a new list with the new marker added to the existing ones
    final updatedMarkers = [...state.markers, marker];
    // Update state with the new list of markers
    // Only set shouldCenterOnMarkers to true if this is the first marker
    state = EstablishmentMarkersState(markers: updatedMarkers, shouldCenterOnMarkers: state.markers.isEmpty || state.shouldCenterOnMarkers);
  }

  void markCentered() {
    if (state.shouldCenterOnMarkers) {
      state = EstablishmentMarkersState(markers: state.markers, shouldCenterOnMarkers: false);
    }
  }

  void clearMarkers() {
    state = EstablishmentMarkersState();
  }
}

// Provider to store product prices and locations
final establishmentMarkersProvider = StateNotifierProvider<EstablishmentMarkersNotifier, EstablishmentMarkersState>((ref) {
  return EstablishmentMarkersNotifier();
});

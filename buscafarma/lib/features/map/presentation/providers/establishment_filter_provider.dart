import 'package:flutter_riverpod/flutter_riverpod.dart';

/// Possible establishment category types
class EstablishmentCategory {
  final String code;
  final String name;

  const EstablishmentCategory(this.code, this.name);

  static const EstablishmentCategory fares = EstablishmentCategory('06', 'Fares');
  static const EstablishmentCategory farmacias = EstablishmentCategory('03', 'Farmacias');
  static const EstablishmentCategory boticas = EstablishmentCategory('04', 'Boticas');

  static const List<EstablishmentCategory> values = [fares, farmacias, boticas];
}

/// Possible establishment types
class EstablishmentType {
  final String code;
  final String name;

  const EstablishmentType(this.code, this.name);

  static const EstablishmentType public = EstablishmentType('1', 'Públicos');
  static const EstablishmentType private = EstablishmentType('2', 'Privados');

  static const List<EstablishmentType> values = [public, private];
}

/// State for establishment filters
class EstablishmentFilterState {
  final EstablishmentCategory? selectedCategory;
  final EstablishmentType? selectedType;
  final String? establishmentName;
  final String? laboratoryName;

  const EstablishmentFilterState({this.selectedCategory, this.selectedType, this.establishmentName, this.laboratoryName});

  EstablishmentFilterState copyWith({
    EstablishmentCategory? selectedCategory,
    bool clearCategory = false,
    EstablishmentType? selectedType,
    bool clearType = false,
    String? establishmentName,
    bool clearEstablishmentName = false,
    String? laboratoryName,
    bool clearLaboratoryName = false,
  }) {
    return EstablishmentFilterState(
      selectedCategory: clearCategory ? null : selectedCategory ?? this.selectedCategory,
      selectedType: clearType ? null : selectedType ?? this.selectedType,
      establishmentName: clearEstablishmentName ? null : establishmentName ?? this.establishmentName,
      laboratoryName: clearLaboratoryName ? null : laboratoryName ?? this.laboratoryName,
    );
  }

  /// Check if any filter is applied
  bool get hasFilters => selectedCategory != null || selectedType != null || (establishmentName != null && establishmentName!.isNotEmpty) || (laboratoryName != null && laboratoryName!.isNotEmpty);

  /// Get filter parameters for API calls
  Map<String, dynamic> get apiParameters {
    return {'catEstablecimiento': selectedCategory?.code, 'codTipoEstablecimiento': selectedType?.code, 'nombreEstablecimiento': establishmentName, 'nombreLaboratorio': laboratoryName};
  }
}

/// Notifier to manage establishment filter state
class EstablishmentFilterNotifier extends StateNotifier<EstablishmentFilterState> {
  EstablishmentFilterNotifier() : super(const EstablishmentFilterState());

  void selectCategory(EstablishmentCategory? category) {
    state = state.copyWith(selectedCategory: category);
  }

  void clearCategory() {
    state = state.copyWith(clearCategory: true);
  }

  void selectType(EstablishmentType? type) {
    state = state.copyWith(selectedType: type);
  }

  void clearType() {
    state = state.copyWith(clearType: true);
  }

  void setEstablishmentName(String name) {
    if (name.isEmpty) {
      state = state.copyWith(clearEstablishmentName: true);
    } else {
      state = state.copyWith(establishmentName: name);
    }
  }

  void setLaboratoryName(String name) {
    if (name.isEmpty) {
      state = state.copyWith(clearLaboratoryName: true);
    } else {
      state = state.copyWith(laboratoryName: name);
    }
  }

  void clearAllFilters() {
    state = const EstablishmentFilterState();
  }
}

/// Provider for establishment filters
final establishmentFilterProvider = StateNotifierProvider<EstablishmentFilterNotifier, EstablishmentFilterState>((ref) {
  return EstablishmentFilterNotifier();
});

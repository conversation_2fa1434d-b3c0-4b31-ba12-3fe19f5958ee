import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:latlong2/latlong.dart';
import 'package:buscafarma/features/map/data/providers/location_providers.dart';
import 'package:buscafarma/features/map/domain/entities/location.dart';
import 'package:buscafarma/features/map/domain/repositories/location_repository.dart';

class MapState {
  final AsyncValue<Location> currentLocation;
  final LatLng mapCenter;
  final double zoom;
  final String? searchQuery;
  final bool isSearching;

  MapState({this.currentLocation = const AsyncValue.loading(), LatLng? mapCenter, this.zoom = 20.0, this.searchQuery, this.isSearching = false}) : mapCenter = mapCenter ?? const LatLng(0, 0);

  MapState copyWith({AsyncValue<Location>? currentLocation, LatLng? mapCenter, double? zoom, String? searchQuery, bool? isSearching}) {
    return MapState(currentLocation: currentLocation ?? this.currentLocation, mapCenter: mapCenter ?? this.mapCenter, zoom: zoom ?? this.zoom, searchQuery: searchQuery ?? this.searchQuery, isSearching: isSearching ?? this.isSearching);
  }
}

class MapNotifier extends StateNotifier<MapState> {
  final LocationRepository _locationRepository;

  MapNotifier(this._locationRepository) : super(MapState()) {
    _initializeLocation();
  }

  Future<void> _initializeLocation() async {
    state = state.copyWith(currentLocation: const AsyncValue.loading());

    final isEnabled = await _locationRepository.isLocationServiceEnabled();
    if (!isEnabled) {
      state = state.copyWith(currentLocation: const AsyncValue.error("Location services are disabled. Please enable them in settings.", StackTrace.empty));
      return;
    }

    final hasPermission = await _locationRepository.requestLocationPermission();
    if (!hasPermission) {
      state = state.copyWith(currentLocation: const AsyncValue.error("Location permission denied. Please grant permission in settings.", StackTrace.empty));
      return;
    }

    final locationResult = await _locationRepository.getCurrentLocation();
    locationResult.fold(
      (failure) {
        state = state.copyWith(currentLocation: AsyncValue.error(failure.toString(), StackTrace.empty));
      },
      (location) {
        state = state.copyWith(currentLocation: AsyncValue.data(location), mapCenter: location.toLatLng());
      },
    );
  }

  void setMapCenter(LatLng center) {
    state = state.copyWith(mapCenter: center);
  }

  void setZoom(double zoom) {
    state = state.copyWith(zoom: zoom);
  }

  void setSearchQuery(String query) {
    state = state.copyWith(searchQuery: query, isSearching: query.isNotEmpty);
  }

  void refreshLocation() {
    _initializeLocation();
  }
}

final mapProvider = StateNotifierProvider<MapNotifier, MapState>((ref) {
  final locationRepository = ref.watch(locationRepositoryProvider);
  return MapNotifier(locationRepository);
});

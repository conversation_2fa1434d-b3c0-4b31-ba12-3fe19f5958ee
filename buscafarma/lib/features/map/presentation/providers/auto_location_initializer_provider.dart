import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/map/presentation/providers/map_provider.dart';
import 'package:buscafarma/features/map/presentation/providers/auto_location_provider.dart';

/// Provider que inicializa automáticamente la ubicación cuando el GPS está disponible.
///
/// Este provider observa el estado de [mapProvider.currentLocation] y cuando
/// cambia a un estado de datos (no cargando ni error), activa el [autoLocationProvider]
/// para cargar automáticamente la ubicación del usuario.
final autoLocationInitializerProvider = Provider<void>((ref) {
  // Observar los cambios en el estado de la ubicación del mapa
  ref.listen<MapState>(mapProvider, (previous, current) {
    // Verificar si el estado anterior era cargando o error y el actual tiene datos
    final wasLoading = previous?.currentLocation.isLoading ?? true;
    final hasData = current.currentLocation.hasValue;
    
    // Si ahora tenemos datos y antes estaba cargando, inicializar la ubicación automática
    if (hasData && wasLoading) {
      // Activar la carga automática de ubicación
      ref.read(autoLocationProvider.notifier).refreshAutoLocation();
    }
  });
  
  // Este provider no devuelve ningún valor, solo realiza una acción
  return;
});

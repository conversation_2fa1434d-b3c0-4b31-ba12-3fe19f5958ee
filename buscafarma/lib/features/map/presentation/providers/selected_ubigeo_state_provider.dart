import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/map/domain/entities/ubigeo_models.dart';
import 'package:buscafarma/features/map/data/providers/ubigeo_providers.dart';

class SelectedUbigeoState {
  final Department? selectedDepartment;
  final Province? selectedProvince;
  final District? selectedDistrict;

  final List<Province> availableProvinces;
  final List<District> availableDistricts;

  const SelectedUbigeoState({this.selectedDepartment, this.selectedProvince, this.selectedDistrict, this.availableProvinces = const [], this.availableDistricts = const []});

  SelectedUbigeoState copyWith({Department? selectedDepartment, Province? selectedProvince, District? selectedDistrict, List<Province>? availableProvinces, List<District>? availableDistricts, bool clearProvince = false, bool clearDistrict = false}) {
    return SelectedUbigeoState(
      selectedDepartment: selectedDepartment ?? this.selectedDepartment,
      selectedProvince: clearProvince ? null : selectedProvince ?? this.selectedProvince,
      selectedDistrict: clearDistrict ? null : selectedDistrict ?? this.selectedDistrict,
      availableProvinces: availableProvinces ?? this.availableProvinces,
      availableDistricts: availableDistricts ?? this.availableDistricts,
    );
  }
}

class SelectedUbigeoNotifier extends StateNotifier<SelectedUbigeoState> {
  final Ref _ref;

  SelectedUbigeoNotifier(this._ref) : super(const SelectedUbigeoState());

  Future<void> selectDepartment(Department department) async {
    await _ref.read(ubigeoInitProvider.future); // Ensure data is loaded
    final provinces = _ref.read(provincesProvider(department.codigo));
    state = state.copyWith(selectedDepartment: department, availableProvinces: provinces, clearProvince: true, clearDistrict: true);
  }

  Future<void> selectProvince(Province province) async {
    await _ref.read(ubigeoInitProvider.future); // Ensure data is loaded
    final districts = _ref.read(districtsProvider(province.codigo));
    state = state.copyWith(selectedProvince: province, availableDistricts: districts, clearDistrict: true);
  }

  void selectDistrict(District district) {
    state = state.copyWith(selectedDistrict: district);
  }

  void clearSelections() {
    state = const SelectedUbigeoState();
  }
}

final selectedUbigeoProvider = StateNotifierProvider<SelectedUbigeoNotifier, SelectedUbigeoState>((ref) {
  return SelectedUbigeoNotifier(ref);
});

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/map/presentation/providers/auto_location_provider.dart';
import 'package:buscafarma/features/map/presentation/providers/selected_ubigeo_state_provider.dart';
import 'package:buscafarma/features/map/data/providers/ubigeo_providers.dart';
import 'package:buscafarma/features/map/domain/entities/ubigeo_models.dart';
import 'package:buscafarma/features/map/data/providers/minsa_providers.dart';
import 'package:buscafarma/features/map/presentation/providers/establishment_markers_provider.dart';
import 'package:buscafarma/features/map/presentation/providers/establishment_filter_provider.dart';
import 'package:buscafarma/core/widgets/search_input_field.dart';

// Renamed and made public
class FilterItemWidget extends StatelessWidget {
  final String label;
  final VoidCallback? onTap;
  final bool isEnabled;

  const FilterItemWidget({required this.label, this.onTap, this.isEnabled = true, super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return InkWell(
      onTap: isEnabled ? onTap : null,
      child: Opacity(
        opacity: isEnabled ? 1.0 : 0.5,
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 16.0),
          decoration: BoxDecoration(border: Border.all(color: theme.dividerColor.withAlpha(100)), borderRadius: BorderRadius.circular(8.0), color: theme.colorScheme.surfaceContainerHighest.withAlpha(50)),
          child: Row(children: [Expanded(child: Text(label, style: theme.textTheme.bodyLarge)), const Icon(Icons.arrow_drop_down)]),
        ),
      ),
    );
  }
}

Future<T?> _showUbigeoSelectionDialog<T>(BuildContext context, WidgetRef ref, String title, List<T> items, String Function(T) getItemName) async {
  String searchQuery = '';
  List<T> filteredItems = List.from(items);

  return showDialog<T>(
    context: context,
    builder: (BuildContext dialogContext) {
      return StatefulBuilder(
        builder: (stfContext, stfSetState) {
          return AlertDialog(
            title: Text(title),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  TextField(
                    onChanged: (value) {
                      stfSetState(() {
                        searchQuery = value.toLowerCase();
                        filteredItems = items.where((item) => getItemName(item).toLowerCase().contains(searchQuery)).toList();
                      });
                    },
                    decoration: const InputDecoration(labelText: 'Buscar', suffixIcon: Icon(Icons.search)),
                  ),
                  Expanded(
                    child: ListView.builder(
                      shrinkWrap: true,
                      itemCount: filteredItems.length,
                      itemBuilder: (BuildContext context, int index) {
                        final item = filteredItems[index];
                        return ListTile(
                          title: Text(getItemName(item)),
                          onTap: () {
                            Navigator.of(dialogContext).pop(item);
                          },
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: <Widget>[
              TextButton(
                child: const Text('Cancelar'),
                onPressed: () {
                  Navigator.of(dialogContext).pop();
                },
              ),
            ],
          );
        },
      );
    },
  );
}

Future<T?> _showSelectionDialog<T>({required BuildContext context, required WidgetRef ref, required String title, required List<T> items, required String Function(T) getItemName, required String Function(T) getItemValue}) async {
  return showDialog<T>(
    context: context,
    builder: (BuildContext dialogContext) {
      return AlertDialog(
        title: Text(title),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: items.length,
            itemBuilder: (BuildContext context, int index) {
              final item = items[index];
              return ListTile(
                title: Text(getItemName(item)),
                onTap: () {
                  Navigator.of(dialogContext).pop(item);
                },
              );
            },
          ),
        ),
        actions: <Widget>[
          TextButton(
            child: const Text('Cancelar'),
            onPressed: () {
              Navigator.of(dialogContext).pop();
            },
          ),
        ],
      );
    },
  );
}

class TextInputWidget extends StatelessWidget {
  final String label;
  final String? value;
  final Function(String)? onChanged;
  final Function()? onClear;
  final bool isEnabled;
  final String hintText;

  const TextInputWidget({required this.label, this.value, this.onChanged, this.onClear, this.isEnabled = true, this.hintText = "Ingrese texto...", super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
      decoration: BoxDecoration(border: Border.all(color: theme.dividerColor.withAlpha(100)), borderRadius: BorderRadius.circular(8.0), color: theme.colorScheme.surfaceContainerHighest.withAlpha(50)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(label, style: theme.textTheme.bodyMedium),
          const SizedBox(height: 4.0),
          Row(
            children: [
              Expanded(
                child: TextField(
                  enabled: isEnabled,
                  controller: value != null ? TextEditingController(text: value) : null,
                  onChanged: onChanged,
                  decoration: InputDecoration(hintText: hintText, isDense: true, contentPadding: const EdgeInsets.symmetric(vertical: 8.0), border: InputBorder.none),
                ),
              ),
              if (value != null && value!.isNotEmpty && onClear != null) IconButton(icon: const Icon(Icons.clear, size: 18.0), padding: EdgeInsets.zero, constraints: const BoxConstraints(minWidth: 32, minHeight: 32), onPressed: onClear),
            ],
          ),
        ],
      ),
    );
  }
}

class SharedPanelContentBody extends ConsumerWidget {
  const SharedPanelContentBody({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    // Observar el estado de ubicación automática
    final autoLocationState = ref.watch(autoLocationProvider);

    // Observar el estado de Ubigeo
    final ubigeoAsyncValue = ref.watch(ubigeoInitProvider);
    final selectedUbigeo = ref.watch(selectedUbigeoProvider);
    final selectedUbigeoNotifier = ref.read(selectedUbigeoProvider.notifier);
    final allDepartments = ref.watch(departmentsProvider);

    // Observar el medicamento seleccionado
    final selectedMedicine = ref.watch(selectedMedicineProvider);

    final List<Widget> contentItems = [
      if (selectedMedicine != null)
        Card(
          color: colorScheme.primary,
          child: Padding(
            padding: const EdgeInsets.all(12.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text("Medicamento seleccionado", style: theme.textTheme.titleSmall?.copyWith(color: colorScheme.onPrimary)),
                const SizedBox(height: 4),
                Row(
                  children: [
                    Expanded(child: Text(selectedMedicine.nombreProducto, style: theme.textTheme.titleMedium?.copyWith(color: colorScheme.onPrimary, fontWeight: FontWeight.bold))),
                    IconButton(
                      icon: Icon(Icons.clear, color: colorScheme.onPrimary),
                      onPressed: () {
                        ref.read(selectedMedicineProvider.notifier).state = null;
                        ref.read(establishmentMarkersProvider.notifier).clearMarkers();
                      },
                    ),
                  ],
                ),
                Text("Concentración: ${selectedMedicine.concent}", style: theme.textTheme.bodySmall?.copyWith(color: colorScheme.onPrimary.withAlpha(200))),
                Text("Forma: ${selectedMedicine.nombreFormaFarmaceutica}", style: theme.textTheme.bodySmall?.copyWith(color: colorScheme.onPrimary.withAlpha(200))),
                Text("Grupo: ${selectedMedicine.grupo}", style: theme.textTheme.bodySmall?.copyWith(color: colorScheme.onPrimary.withAlpha(200))),
              ],
            ),
          ),
        ),
      if (selectedMedicine != null) const SizedBox(height: 24.0),
      Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text("Ubicación", style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
          // Botón para usar la ubicación actual
          TextButton.icon(
            icon: const Icon(Icons.my_location),
            label: const Text("Usar mi ubicación"),
            onPressed: () {
              ref.read(autoLocationProvider.notifier).refreshAutoLocation();
            },
          ),
        ],
      ),
      if (autoLocationState.isLoading) const Padding(padding: EdgeInsets.symmetric(vertical: 16.0), child: Center(child: CircularProgressIndicator())),
      if (autoLocationState.errorMessage != null)
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Card(
            color: colorScheme.errorContainer,
            child: Padding(
              padding: const EdgeInsets.all(8.0),
              child: Row(
                children: [Icon(Icons.error_outline, color: colorScheme.error), const SizedBox(width: 8), Expanded(child: Text(autoLocationState.errorMessage!, style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.onErrorContainer)))],
              ),
            ),
          ),
        ),
      // Mostrar información de geocodificación si está disponible
      if (autoLocationState.locationDetails != null && !autoLocationState.isLoading)
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 8.0),
          child: Card(
            color: colorScheme.surfaceContainerHighest.withAlpha(50),
            child: Padding(
              padding: const EdgeInsets.all(12.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text("Ubicación", style: theme.textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold)),
                  const SizedBox(height: 4),
                  if (autoLocationState.locationDetails!.fullAddress != null) Text(autoLocationState.locationDetails!.fullAddress!, style: theme.textTheme.bodyMedium),
                ],
              ),
            ),
          ),
        ),
      const SizedBox(height: 12.0),
      ubigeoAsyncValue.when(
        data:
            (_) => Column(
              children: [
                FilterItemWidget(
                  label: selectedUbigeo.selectedDepartment?.descripcion ?? "Seleccionar Departamento",
                  onTap: () async {
                    final selectedDept = await _showUbigeoSelectionDialog<Department>(context, ref, "Seleccionar Departamento", allDepartments, (dept) => dept.descripcion);
                    if (selectedDept != null) {
                      selectedUbigeoNotifier.selectDepartment(selectedDept);
                    }
                  },
                ),
                const SizedBox(height: 8.0),
                FilterItemWidget(
                  label: selectedUbigeo.selectedProvince?.descripcion ?? "Seleccionar Provincia",
                  isEnabled: selectedUbigeo.selectedDepartment != null && selectedUbigeo.availableProvinces.isNotEmpty,
                  onTap: () async {
                    if (selectedUbigeo.selectedDepartment == null) return;
                    final selectedProv = await _showUbigeoSelectionDialog<Province>(
                      context,
                      ref,
                      "Seleccionar Provincia",
                      selectedUbigeo.availableProvinces, // Use availableProvinces from state
                      (prov) => prov.descripcion,
                    );
                    if (selectedProv != null) {
                      selectedUbigeoNotifier.selectProvince(selectedProv);
                    }
                  },
                ),
                const SizedBox(height: 8.0),
                FilterItemWidget(
                  label: selectedUbigeo.selectedDistrict?.descripcion ?? "Seleccionar Distrito",
                  isEnabled: selectedUbigeo.selectedProvince != null && selectedUbigeo.availableDistricts.isNotEmpty,
                  onTap: () async {
                    if (selectedUbigeo.selectedProvince == null) return;
                    final selectedDist = await _showUbigeoSelectionDialog<District>(
                      context,
                      ref,
                      "Seleccionar Distrito",
                      selectedUbigeo.availableDistricts, // Use availableDistricts from state
                      (dist) => dist.descripcion,
                    );
                    if (selectedDist != null) {
                      selectedUbigeoNotifier.selectDistrict(selectedDist);
                    }
                  },
                ),
                if (autoLocationState.hasMatched)
                  Padding(
                    padding: const EdgeInsets.only(top: 8.0),
                    child: Row(children: [const Icon(Icons.info_outline, size: 16), const SizedBox(width: 8), Expanded(child: Text("Ubicación seleccionada automáticamente según tu ubicación actual", style: theme.textTheme.bodySmall))]),
                  ),
              ],
            ),
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (err, stack) => Center(child: Text("Error al cargar datos de ubicación: $err")),
      ),
      const SizedBox(height: 24.0),
      Text("Filtros adicionales", style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold)),
      const SizedBox(height: 12.0),

      // Watch establishment filter state
      Consumer(
        builder: (context, ref, child) {
          final filterState = ref.watch(establishmentFilterProvider);
          final filterNotifier = ref.read(establishmentFilterProvider.notifier);

          return Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Establishment name filter
              EstablishmentNameSearchField(initialValue: filterState.establishmentName, onSearchChanged: (value) => filterNotifier.setEstablishmentName(value), onClear: () => filterNotifier.setEstablishmentName(""), enabled: true),
              const SizedBox(height: 12.0),

              // Laboratory name filter
              LaboratoryNameSearchField(initialValue: filterState.laboratoryName, onSearchChanged: (value) => filterNotifier.setLaboratoryName(value), onClear: () => filterNotifier.setLaboratoryName(""), enabled: true),
              const SizedBox(height: 12.0),

              // Establishment type filter
              FilterItemWidget(
                label: filterState.selectedType?.name ?? "Tipo de establecimiento",
                onTap: () async {
                  final selectedType = await _showSelectionDialog<EstablishmentType>(context: context, ref: ref, title: "Seleccionar tipo", items: EstablishmentType.values, getItemName: (type) => type.name, getItemValue: (type) => type.code);

                  if (selectedType != null) {
                    filterNotifier.selectType(selectedType);
                  }
                },
              ),
              const SizedBox(height: 8.0),

              // Establishment category filter
              FilterItemWidget(
                label: filterState.selectedCategory?.name ?? "Categoría de establecimiento",
                onTap: () async {
                  final selectedCategory = await _showSelectionDialog<EstablishmentCategory>(
                    context: context,
                    ref: ref,
                    title: "Seleccionar categoría",
                    items: EstablishmentCategory.values,
                    getItemName: (cat) => cat.name,
                    getItemValue: (cat) => cat.code,
                  );

                  if (selectedCategory != null) {
                    filterNotifier.selectCategory(selectedCategory);
                  }
                },
              ),

              // Filter buttons
              if (filterState.hasFilters)
                Padding(padding: const EdgeInsets.only(top: 16.0), child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [TextButton(onPressed: () => filterNotifier.clearAllFilters(), child: const Text("Limpiar filtros"))])),
            ],
          );
        },
      ),
    ];

    return ListView(padding: const EdgeInsets.all(16.0), children: contentItems);
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/map/data/providers/ubigeo_providers.dart';
import 'package:buscafarma/features/map/domain/entities/ubigeo_models.dart';

class LocationSelector extends ConsumerStatefulWidget {
  final Function(String, String, String)? onLocationSelected;

  const LocationSelector({super.key, this.onLocationSelected});

  @override
  ConsumerState<LocationSelector> createState() => _LocationSelectorState();
}

class _LocationSelectorState extends ConsumerState<LocationSelector> {
  String? _selectedDepartmentCode;
  String? _selectedProvinceCode;
  String? _selectedDistrictCode;

  // Text controllers for search inputs
  final TextEditingController _departmentController = TextEditingController();
  final TextEditingController _provinceController = TextEditingController();
  final TextEditingController _districtController = TextEditingController();

  @override
  void initState() {
    super.initState();

    // Force initialization of the ubigeo data
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(ubigeoInitProvider);
    });
  }

  @override
  void dispose() {
    _departmentController.dispose();
    _provinceController.dispose();
    _districtController.dispose();
    super.dispose();
  }

  void _selectDepartment(Department department) {
    setState(() {
      _selectedDepartmentCode = department.codigo;
      _departmentController.text = department.descripcion;

      // Reset province and district when department changes
      _selectedProvinceCode = null;
      _selectedDistrictCode = null;
      _provinceController.clear();
      _districtController.clear();
    });

    // Clear search results
    ref.read(locationSearchProvider.notifier).clearSearch();
  }

  void _selectProvince(Province province) {
    setState(() {
      _selectedProvinceCode = province.codigo;
      _provinceController.text = province.descripcion;

      // Reset district when province changes
      _selectedDistrictCode = null;
      _districtController.clear();
    });

    // Clear search results
    ref.read(locationSearchProvider.notifier).clearSearch();
  }

  void _selectDistrict(District district) {
    setState(() {
      _selectedDistrictCode = district.codigo;
      _districtController.text = district.descripcion;
    });

    // Clear search results
    ref.read(locationSearchProvider.notifier).clearSearch();

    // Notify parent of the complete selection
    if (_selectedDepartmentCode != null && _selectedProvinceCode != null && _selectedDistrictCode != null && widget.onLocationSelected != null) {
      widget.onLocationSelected!(_selectedDepartmentCode!, _selectedProvinceCode!, _selectedDistrictCode!);
    }
  }

  @override
  Widget build(BuildContext context) {
    final initStatus = ref.watch(ubigeoInitProvider);
    final searchState = ref.watch(locationSearchProvider);

    return initStatus.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stackTrace) => Center(child: Text('Error cargando datos geográficos: $error')),
      data:
          (_) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Department selector
              _buildSearchField(
                label: 'Departamento',
                controller: _departmentController,
                onChanged: (query) {
                  ref.read(locationSearchProvider.notifier).searchLocations(query);
                },
                onClear: () {
                  setState(() {
                    _selectedDepartmentCode = null;
                    _selectedProvinceCode = null;
                    _selectedDistrictCode = null;
                    _provinceController.clear();
                    _districtController.clear();
                  });
                  ref.read(locationSearchProvider.notifier).clearSearch();
                },
              ),

              // Show department search results
              if (_departmentController.text.isNotEmpty && searchState.departments.isNotEmpty && _selectedDepartmentCode == null)
                _buildSearchResults<Department>(items: searchState.departments, itemBuilder: (department) => Text(department.descripcion), onItemSelected: _selectDepartment),

              const SizedBox(height: 16),

              // Province selector (enabled only when department is selected)
              _buildSearchField(
                label: 'Provincia',
                controller: _provinceController,
                enabled: _selectedDepartmentCode != null,
                onChanged: (query) {
                  ref.read(locationSearchProvider.notifier).searchLocations(query);
                },
                onClear: () {
                  setState(() {
                    _selectedProvinceCode = null;
                    _selectedDistrictCode = null;
                    _districtController.clear();
                  });
                  ref.read(locationSearchProvider.notifier).clearSearch();
                },
              ),

              // Show province search results or provinces for selected department
              if (_selectedDepartmentCode != null && _provinceController.text.isNotEmpty && _selectedProvinceCode == null)
                _buildSearchResults<Province>(
                  items: searchState.provinces.isNotEmpty ? searchState.provinces.where((p) => p.codigoDepartamento == _selectedDepartmentCode).toList() : ref.watch(provincesProvider(_selectedDepartmentCode!)),
                  itemBuilder: (province) => Text(province.descripcion),
                  onItemSelected: _selectProvince,
                ),

              const SizedBox(height: 16),

              // District selector (enabled only when province is selected)
              _buildSearchField(
                label: 'Distrito',
                controller: _districtController,
                enabled: _selectedProvinceCode != null,
                onChanged: (query) {
                  ref.read(locationSearchProvider.notifier).searchLocations(query);
                },
                onClear: () {
                  setState(() {
                    _selectedDistrictCode = null;
                  });
                  ref.read(locationSearchProvider.notifier).clearSearch();
                },
              ),

              // Show district search results or districts for selected province
              if (_selectedProvinceCode != null && _districtController.text.isNotEmpty && _selectedDistrictCode == null)
                _buildSearchResults<District>(
                  items: searchState.districts.isNotEmpty ? searchState.districts.where((d) => d.codigoProvincia == _selectedProvinceCode).toList() : ref.watch(districtsProvider(_selectedProvinceCode!)),
                  itemBuilder: (district) => Text(district.descripcion),
                  onItemSelected: _selectDistrict,
                ),
            ],
          ),
    );
  }

  Widget _buildSearchField({required String label, required TextEditingController controller, bool enabled = true, required Function(String) onChanged, required VoidCallback onClear}) {
    return TextField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
        suffixIcon:
            controller.text.isNotEmpty
                ? IconButton(
                  icon: const Icon(Icons.clear),
                  onPressed: () {
                    controller.clear();
                    onClear();
                  },
                )
                : null,
      ),
      enabled: enabled,
      onChanged: onChanged,
    );
  }

  Widget _buildSearchResults<T>({required List<T> items, required Widget Function(T) itemBuilder, required Function(T) onItemSelected}) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 200),
      margin: const EdgeInsets.only(top: 4),
      decoration: BoxDecoration(color: Theme.of(context).cardColor, border: Border.all(color: Theme.of(context).dividerColor), borderRadius: BorderRadius.circular(4)),
      child: ListView.builder(
        shrinkWrap: true,
        padding: EdgeInsets.zero,
        itemCount: items.length,
        itemBuilder: (context, index) {
          final item = items[index];
          return InkWell(onTap: () => onItemSelected(item), child: Padding(padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12), child: itemBuilder(item)));
        },
      ),
    );
  }
}

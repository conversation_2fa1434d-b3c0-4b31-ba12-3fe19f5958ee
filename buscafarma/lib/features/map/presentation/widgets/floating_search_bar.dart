import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/router/app_router.dart';
import 'package:buscafarma/core/router/app_routes.dart';
import 'package:buscafarma/features/auth/presentation/providers/auth_provider.dart';
import 'package:buscafarma/features/map/presentation/pages/map_page.dart';
import 'package:buscafarma/features/map/data/providers/minsa_providers.dart';
import 'package:buscafarma/features/map/presentation/providers/establishment_markers_provider.dart';

class FloatingSearchBar extends ConsumerWidget {
  final VoidCallback? onTap;
  final bool isPanelVisible;
  final VoidCallback? onClearSelection;

  const FloatingSearchBar({super.key, this.onTap, required this.isPanelVisible, this.onClearSelection});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final selectedMedicine = ref.watch(selectedMedicineProvider);
    final authState = ref.watch(authProvider);

    // Determine display text based on selected medicine
    final String displayText = selectedMedicine != null ? selectedMedicine.nombreProducto : "Buscar...";

    return SizedBox(
      height: 52.0, // Increased height for better touch targets
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainerHigh,
          borderRadius: BorderRadius.circular(28.0),
          boxShadow: [BoxShadow(color: Colors.black.withAlpha((0.1 * 255).round()), blurRadius: 10, spreadRadius: 1, offset: const Offset(0, 4))],
        ),
        child: Row(
          children: [
            // Search bar with Hero animation
            Expanded(
              child: Hero(
                tag: 'search_bar_hero',
                flightShuttleBuilder: (flightContext, animation, direction, fromContext, toContext) {
                  return Material(
                    color: Colors.transparent,
                    child: Container(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHigh,
                        borderRadius: BorderRadius.circular(28.0),
                        boxShadow: [BoxShadow(color: Colors.black.withAlpha((0.1 * 255).round()), blurRadius: 10, spreadRadius: 1, offset: const Offset(0, 4))],
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.search, color: colorScheme.onSurfaceVariant),
                          const SizedBox(width: 12.0),
                          Expanded(child: Text(displayText, style: TextStyle(color: colorScheme.onSurfaceVariant), maxLines: 1, overflow: TextOverflow.ellipsis)),
                        ],
                      ),
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.only(left: 16.0, top: 8.0, bottom: 8.0, right: 8.0),
                  child: Row(
                    children: [
                      Icon(Icons.search, color: colorScheme.onSurfaceVariant),
                      const SizedBox(width: 12.0),
                      Expanded(
                        child:
                            selectedMedicine != null
                                ? Text(displayText, style: TextStyle(color: colorScheme.onSurfaceVariant.withAlpha(230), fontSize: 16.0), maxLines: 1, overflow: TextOverflow.ellipsis)
                                : TextField(
                                  onTap: onTap,
                                  readOnly: true,
                                  focusNode: null,
                                  style: TextStyle(fontSize: 16.0),
                                  decoration: InputDecoration(hintText: "Buscar...", border: InputBorder.none, isDense: true, hintStyle: TextStyle(color: colorScheme.onSurfaceVariant)),
                                ),
                      ),
                      // Show clear button when medicine is selected
                      if (selectedMedicine != null)
                        Material(
                          color: Colors.transparent,
                          shape: const CircleBorder(),
                          clipBehavior: Clip.antiAlias,
                          child: IconButton(
                            onPressed: () {
                              // Signal cancellation before clearing state
                              ref.read(geocodingCancellationProvider.notifier).state = true;

                              // Clear selected medicine and markers
                              ref.read(selectedMedicineProvider.notifier).state = null;
                              ref.read(establishmentMarkersProvider.notifier).clearMarkers();
                              ref.read(markerLoadingProvider.notifier).state = false;

                              // Show feedback to user that operation was cancelled
                              ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Búsqueda cancelada'), duration: Duration(seconds: 1), behavior: SnackBarBehavior.floating));

                              // If there was an external onClearSelection callback, call it
                              if (onClearSelection != null) {
                                onClearSelection!();
                              }
                            },
                            icon: Icon(Icons.close, color: colorScheme.primary),
                            tooltip: 'Borrar búsqueda',
                            splashRadius: 20,
                            constraints: const BoxConstraints(minWidth: 40, minHeight: 40),
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),

            // User profile picture with Hero animation
            Padding(
              padding: const EdgeInsets.only(right: 8.0),
              child: Hero(
                tag: 'profile_avatar_hero',
                flightShuttleBuilder: (flightContext, animation, direction, fromContext, toContext) {
                  return Material(
                    color: Colors.transparent,
                    child: Container(
                      decoration: BoxDecoration(shape: BoxShape.circle, border: Border.all(color: colorScheme.primary.withValues(alpha: (0.4 * 255).round().toDouble()), width: 1.5)),
                      child: ClipOval(
                        child:
                            authState.user?.profilePictureUrl != null
                                ? Image.network(authState.user!.profilePictureUrl!, fit: BoxFit.cover, errorBuilder: (context, error, stackTrace) => Icon(Icons.person, color: colorScheme.onSurfaceVariant))
                                : Icon(Icons.person, color: colorScheme.onSurfaceVariant),
                      ),
                    ),
                  );
                },
                child: Material(
                  color: Colors.transparent,
                  shape: const CircleBorder(),
                  clipBehavior: Clip.antiAlias,
                  child: InkWell(
                    onTap: () {
                      // Navigate to profile page
                      AppRouter.navigateTo(context, AppRoutes.profile);
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(2.0),
                      child: Container(
                        width: 32,
                        height: 32,
                        decoration: BoxDecoration(shape: BoxShape.circle, border: Border.all(color: colorScheme.primary.withValues(alpha: (0.4 * 255).round().toDouble()), width: 1.5)),
                        child: ClipOval(
                          child:
                              authState.user?.profilePictureUrl != null
                                  ? Image.network(authState.user!.profilePictureUrl!, fit: BoxFit.cover, errorBuilder: (context, error, stackTrace) => Icon(Icons.person, color: colorScheme.onSurfaceVariant))
                                  : Icon(Icons.person, color: colorScheme.onSurfaceVariant),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

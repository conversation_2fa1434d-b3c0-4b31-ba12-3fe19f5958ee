import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';
import 'package:buscafarma/features/favorites/presentation/providers/favorites_provider.dart';
import 'package:latlong2/latlong.dart';
import 'package:url_launcher/url_launcher.dart';

class EstablishmentDetailsSheet extends ConsumerStatefulWidget {
  final ProductPrice? establishment;
  final ProductPrice? productPrice;
  final LatLng? location;
  final String? address;
  final bool isFavoriteContext;

  const EstablishmentDetailsSheet({super.key, this.establishment, this.productPrice, this.location, this.address, this.isFavoriteContext = false})
    : assert(establishment != null || productPrice != null, 'Either establishment or productPrice must be provided');

  @override
  ConsumerState<EstablishmentDetailsSheet> createState() => _EstablishmentDetailsSheetState();
}

class _EstablishmentDetailsSheetState extends ConsumerState<EstablishmentDetailsSheet> {
  bool _isAddingToFavorites = false;

  ProductPrice get _productPrice => widget.productPrice ?? widget.establishment!;

  Future<void> _openMapsNavigation() async {
    if (widget.location == null && widget.address == null) return;

    String? mapUrl;

    if (widget.location != null) {
      final lat = widget.location!.latitude;
      final lng = widget.location!.longitude;
      mapUrl = 'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng';
    } else if (widget.address != null) {
      mapUrl = 'https://www.google.com/maps/dir/?api=1&destination=${Uri.encodeComponent(widget.address!)}';
    }

    if (mapUrl == null) return;

    try {
      final uri = Uri.parse(mapUrl);
      if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
        debugPrint('No se pudo abrir Google Maps');

        // Solo intentamos Apple Maps si tenemos coordenadas y Google Maps falló
        if (widget.location != null) {
          final appleMapsUrl = 'https://maps.apple.com/?daddr=${widget.location!.latitude},${widget.location!.longitude}';
          final appleMapsUri = Uri.parse(appleMapsUrl);

          if (!await launchUrl(appleMapsUri, mode: LaunchMode.externalApplication)) {
            debugPrint('No se pudo abrir Apple Maps');
          }
        }
      }
    } catch (e) {
      debugPrint('Error al abrir la navegación de mapas: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.fromLTRB(16.0, 8.0, 16.0, 24.0),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Encabezado
          Row(
            children: [
              CircleAvatar(backgroundColor: colorScheme.primary, radius: 24, child: Icon(Icons.local_pharmacy, color: colorScheme.onPrimary, size: 24)),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(_productPrice.nombreComercial ?? 'Establecimiento', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold), maxLines: 2, overflow: TextOverflow.ellipsis),
                    // En lugar de razón social, usamos el nombre del producto
                    Text(_productPrice.nombreProducto ?? '', style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.onSurfaceVariant)),
                  ],
                ),
              ),
              // Favorite button
              _buildFavoriteButton(context, ref),
            ],
          ),

          const SizedBox(height: 20),

          // Precios (mostrar ambos precios)
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('Precios:', style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold, color: colorScheme.onSurface)),
              const SizedBox(height: 8),
              // Precio por paquete (precio1)
              Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                decoration: BoxDecoration(color: colorScheme.primaryContainer, borderRadius: BorderRadius.circular(8)),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('Por paquete:', style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.onPrimaryContainer)),
                    Text('S/ ${_productPrice.precio1.toStringAsFixed(2)}', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: colorScheme.primary)),
                  ],
                ),
              ),
              const SizedBox(height: 8),
              // Precio por unidad (precio2) - solo mostrar si está disponible
              if (_productPrice.precio2 != null)
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                  decoration: BoxDecoration(color: colorScheme.secondaryContainer, borderRadius: BorderRadius.circular(8)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Por unidad:', style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.onSecondaryContainer)),
                      Text('S/ ${_productPrice.precio2!.toStringAsFixed(2)}', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold, color: colorScheme.secondary)),
                    ],
                  ),
                ),
            ],
          ),

          const SizedBox(height: 16),

          // Información detallada
          _InfoRow(icon: Icons.location_on, label: 'Dirección', value: _productPrice.direccion ?? 'No disponible'),
          _InfoRow(icon: Icons.location_city, label: 'Distrito', value: _productPrice.distrito ?? 'No disponible'),
          if (_productPrice.telefono != null && _productPrice.telefono!.isNotEmpty)
            _InfoRow(
              icon: Icons.phone,
              label: 'Teléfono',
              value: _productPrice.telefono ?? 'No disponible',
              onTap: () async {
                final tel = _productPrice.telefono;
                if (tel != null && tel.isNotEmpty) {
                  final phoneUri = Uri.parse('tel:$tel');
                  if (!await launchUrl(phoneUri)) {
                    debugPrint('No se pudo realizar la llamada');
                  }
                }
              },
            ),

          // Información adicional
          _InfoRow(icon: Icons.medication, label: 'Laboratorio', value: _productPrice.nombreLaboratorio ?? 'No disponible'),

          // Ubicación - departamento, provincia
          if (_productPrice.departamento != null || _productPrice.provincia != null)
            _InfoRow(
              icon: Icons.map,
              label: 'Ubicación',
              value: [
                if (_productPrice.distrito != null) _productPrice.distrito,
                if (_productPrice.provincia != null) _productPrice.provincia,
                if (_productPrice.departamento != null) _productPrice.departamento,
              ].where((e) => e != null && e.isNotEmpty).join(', '),
            ),

          const SizedBox(height: 24),

          // Botones de acción
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  style: ElevatedButton.styleFrom(backgroundColor: colorScheme.primary, foregroundColor: colorScheme.onPrimary, padding: const EdgeInsets.symmetric(vertical: 12)),
                  onPressed: _openMapsNavigation,
                  icon: const Icon(Icons.directions),
                  label: const Text('Cómo llegar'),
                ),
              ),
              const SizedBox(width: 12),
              if (_productPrice.telefono != null && _productPrice.telefono!.isNotEmpty)
                IconButton(
                  style: IconButton.styleFrom(backgroundColor: colorScheme.secondaryContainer, foregroundColor: colorScheme.onSecondaryContainer),
                  onPressed: () async {
                    final tel = _productPrice.telefono;
                    if (tel != null && tel.isNotEmpty) {
                      final phoneUri = Uri.parse('tel:$tel');
                      if (!await launchUrl(phoneUri)) {
                        debugPrint('No se pudo realizar la llamada');
                      }
                    }
                  },
                  icon: const Icon(Icons.phone),
                ),
              const SizedBox(width: 8),
              IconButton(
                style: IconButton.styleFrom(backgroundColor: colorScheme.secondaryContainer, foregroundColor: colorScheme.onSecondaryContainer),
                onPressed: () async {
                  // Copiar la dirección al portapapeles
                  final address = _productPrice.direccion;
                  if (address != null && address.isNotEmpty) {
                    await Clipboard.setData(ClipboardData(text: address));
                    if (context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(const SnackBar(content: Text('Dirección copiada al portapapeles')));
                    }
                  }
                },
                icon: const Icon(Icons.copy),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildFavoriteButton(BuildContext context, WidgetRef ref) {
    final favoritesState = ref.watch(favoritesProvider);
    final isFavorite = favoritesState.isFavorite(_productPrice.codEstab, _productPrice.codProdE);

    return IconButton(
      onPressed: _isAddingToFavorites ? null : () => _toggleFavorite(context, ref),
      icon:
          _isAddingToFavorites
              ? SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2, color: Theme.of(context).colorScheme.primary))
              : Icon(isFavorite ? Icons.favorite : Icons.favorite_border, color: isFavorite ? Theme.of(context).colorScheme.error : Theme.of(context).colorScheme.onSurfaceVariant),
      tooltip: isFavorite ? 'Quitar de favoritos' : 'Agregar a favoritos',
    );
  }

  Future<void> _toggleFavorite(BuildContext context, WidgetRef ref) async {
    setState(() => _isAddingToFavorites = true);

    try {
      final favoritesNotifier = ref.read(favoritesProvider.notifier);
      final isFavorite = favoritesNotifier.isFavorite(_productPrice.codEstab, _productPrice.codProdE);

      bool success;
      String message;

      if (isFavorite) {
        success = await favoritesNotifier.removeFromFavorites(_productPrice.codEstab, _productPrice.codProdE);
        message = success ? 'Medicamento quitado de favoritos' : 'Error al quitar de favoritos';
      } else {
        success = await favoritesNotifier.addToFavorites(_productPrice);
        message = success ? 'Medicamento agregado a favoritos' : 'Error al agregar a favoritos';
      }

      // Clear any errors from the favorites provider after successful operations
      if (success) {
        favoritesNotifier.clearError();
      }

      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text(message), backgroundColor: success ? Colors.green : Colors.red, duration: const Duration(seconds: 2)));
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}'), backgroundColor: Colors.red, duration: const Duration(seconds: 3)));
      }
    } finally {
      if (mounted) {
        setState(() => _isAddingToFavorites = false);
      }
    }
  }
}

class _InfoRow extends StatelessWidget {
  final IconData icon;
  final String label;
  final String value;
  final VoidCallback? onTap;

  const _InfoRow({required this.icon, required this.label, required this.value, this.onTap});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Icon(icon, size: 20, color: colorScheme.primary),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(label, style: theme.textTheme.bodySmall?.copyWith(color: colorScheme.onSurfaceVariant)),
                  Text(
                    value,
                    style: theme.textTheme.bodyMedium?.copyWith(
                      fontWeight: onTap != null ? FontWeight.w500 : null,
                      color: onTap != null ? colorScheme.primary : null,
                      decoration: onTap != null ? TextDecoration.underline : null,
                      decorationColor: onTap != null ? colorScheme.primary : null,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

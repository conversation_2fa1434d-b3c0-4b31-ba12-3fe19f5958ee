import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:async';
import 'package:dio/dio.dart'; // Import for CancelToken
import 'package:buscafarma/features/map/data/providers/minsa_providers.dart';
import 'package:buscafarma/core/providers/disclaimer_provider.dart';
import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';
import 'package:buscafarma/features/map/presentation/widgets/shared_panel_parts.dart';
import 'package:buscafarma/features/geocoding/domain/entities/geocoding_detail_models.dart';
import 'package:buscafarma/features/geocoding/presentation/providers/geocoding_providers.dart';
import 'package:buscafarma/features/geocoding/domain/repositories/geocoding_repository.dart';
import 'package:buscafarma/features/map/presentation/providers/selected_ubigeo_state_provider.dart';
import 'package:buscafarma/features/map/presentation/providers/establishment_markers_provider.dart';
import 'package:buscafarma/features/map/presentation/pages/map_page.dart';
import 'package:buscafarma/features/map/presentation/providers/establishment_filter_provider.dart';
import 'package:latlong2/latlong.dart';

class SearchSuggestionsPanel extends ConsumerStatefulWidget {
  final VoidCallback onClose;
  final double topPadding;

  const SearchSuggestionsPanel({super.key, required this.onClose, required this.topPadding});

  @override
  ConsumerState<SearchSuggestionsPanel> createState() => _SearchSuggestionsPanelState();
}

class _SearchSuggestionsPanelState extends ConsumerState<SearchSuggestionsPanel> with SingleTickerProviderStateMixin {
  final TextEditingController _searchTermController = TextEditingController();
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  Timer? _debounceTimer;
  bool _filterByExactName = false;

  @override
  void initState() {
    super.initState();

    _animationController = AnimationController(vsync: this, duration: const Duration(milliseconds: 300));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeIn));

    _slideAnimation = Tween<Offset>(begin: const Offset(0.0, -0.1), end: Offset.zero).animate(CurvedAnimation(parent: _animationController, curve: Curves.easeOut));

    // Iniciar la animación al mostrar el panel
    _animationController.forward();

    // Agregar listener a searchTermController para implementar debounce
    _searchTermController.addListener(_onSearchChanged);
  }

  void _onSearchChanged() {
    // Cancelar el timer anterior
    if (_debounceTimer?.isActive ?? false) {
      _debounceTimer!.cancel();
    }

    // Crear un nuevo timer de 500ms
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      final query = _searchTermController.text.trim();
      if (query.isNotEmpty) {
        // Buscar medicamentos después de 500ms
        ref.read(medicineSearchProvider.notifier).searchMedicines(query);
      } else {
        // Limpiar resultados si el query está vacío
        ref.read(medicineSearchProvider.notifier).clearSearch();
      }
    });
  }

  void _onFilterByExactNameChanged(bool? value) {
    setState(() {
      _filterByExactName = value ?? false;
    });
  }

  @override
  void dispose() {
    _searchTermController.removeListener(_onSearchChanged);
    _searchTermController.dispose();
    _animationController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final searchState = ref.watch(medicineSearchProvider);

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Material(
          color: Colors.transparent, // Fondo transparente para el fade
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Scaffold(
              backgroundColor: colorScheme.surfaceContainerLowest, // Fondo completamente opaco
              appBar: PreferredSize(
                preferredSize: Size.fromHeight(widget.topPadding + 68.0),
                child: Container(
                  color: Colors.transparent,
                  child: Padding(
                    padding: EdgeInsets.only(top: widget.topPadding + 16.0, left: 16.0, right: 16.0, bottom: 8.0),
                    child: Hero(
                      tag: 'search_bar_hero',
                      child: Material(
                        color: Colors.transparent,
                        child: Container(
                          height: 52.0,
                          decoration: BoxDecoration(
                            color: colorScheme.surfaceContainerHigh,
                            borderRadius: BorderRadius.circular(30.0),
                            boxShadow: [BoxShadow(color: Colors.black.withAlpha((0.15 * 255).round()), blurRadius: 8, offset: const Offset(0, 4))],
                          ),
                          child: Row(
                            children: [
                              // Back button integrated into pill
                              Material(
                                color: Colors.transparent,
                                child: InkWell(
                                  borderRadius: BorderRadius.circular(30.0),
                                  onTap: () {
                                    // Animar la salida antes de cerrar
                                    _animationController.reverse().then((_) {
                                      widget.onClose();
                                    });
                                  },
                                  child: Padding(padding: const EdgeInsets.all(12.0), child: Icon(Icons.arrow_back, color: colorScheme.onSurfaceVariant, size: 22.0)),
                                ),
                              ),
                              // Search icon and field
                              Expanded(
                                child: Row(
                                  children: [
                                    Icon(Icons.search, color: colorScheme.onSurfaceVariant, size: 22.0),
                                    const SizedBox(width: 12.0),
                                    Expanded(
                                      child: TextField(
                                        controller: _searchTermController,
                                        autofocus: true,
                                        decoration: InputDecoration(hintText: "Buscar...", border: InputBorder.none, isDense: true, hintStyle: TextStyle(color: colorScheme.onSurfaceVariant.withAlpha((0.7 * 255).round()))),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              // Clear button
                              if (_searchTermController.text.isNotEmpty)
                                Material(
                                  color: Colors.transparent,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(30.0),
                                    onTap: () {
                                      _searchTermController.clear();
                                      ref.read(medicineSearchProvider.notifier).clearSearch();
                                    },
                                    child: Padding(padding: const EdgeInsets.all(12.0), child: Icon(Icons.close, color: colorScheme.onSurfaceVariant, size: 22.0)),
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              body: SlideTransition(
                position: _slideAnimation,
                child: FadeTransition(
                  opacity: _fadeAnimation,
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Checkbox para filtrar por medicamento exacto
                        if (_searchTermController.text.isNotEmpty) ...[
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
                            decoration: BoxDecoration(border: Border.all(color: theme.dividerColor.withAlpha(100)), borderRadius: BorderRadius.circular(8.0), color: theme.colorScheme.surfaceContainerHighest.withAlpha(50)),
                            child: Row(
                              children: [
                                Checkbox(value: _filterByExactName, onChanged: _onFilterByExactNameChanged, materialTapTargetSize: MaterialTapTargetSize.shrinkWrap),
                                const SizedBox(width: 8.0),
                                Expanded(child: Text("Ubicar sólo el medicamento prescrito", style: theme.textTheme.bodyMedium)),
                              ],
                            ),
                          ),
                          const SizedBox(height: 12.0),
                        ],
                        // Resultados de búsqueda
                        if (_searchTermController.text.isNotEmpty) ...[
                          Text("Resultados de búsqueda", style: theme.textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold)),
                          const SizedBox(height: 8.0),
                          // Data source disclaimer
                          Container(
                            padding: const EdgeInsets.all(12.0),
                            margin: const EdgeInsets.only(bottom: 12.0),
                            decoration: BoxDecoration(color: theme.colorScheme.surfaceContainerHighest.withValues(alpha: 0.3), borderRadius: BorderRadius.circular(8.0), border: Border.all(color: theme.colorScheme.outline.withValues(alpha: 0.3))),
                            child: Row(
                              children: [
                                Icon(Icons.info_outline, size: 16, color: theme.colorScheme.onSurfaceVariant),
                                const SizedBox(width: 8.0),
                                Expanded(child: Text("Datos del MINSA - BuscaFarma es una app privada independiente", style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant, fontSize: 11))),
                              ],
                            ),
                          ),
                          searchState.searchResults.when(
                            data: (products) {
                              if (products.isEmpty && !searchState.isSearching) {
                                return const Center(child: Padding(padding: EdgeInsets.all(16.0), child: Text("No se encontraron resultados")));
                              }

                              return Expanded(
                                child: ListView.separated(
                                  itemCount: products.length,
                                  separatorBuilder: (context, index) => const Divider(),
                                  itemBuilder: (context, index) {
                                    final product = products[index];
                                    return MedicineSearchResultItem(product: product, onPanelClose: widget.onClose, filterByExactName: _filterByExactName);
                                  },
                                ),
                              );
                            },
                            loading: () => const Center(child: Padding(padding: EdgeInsets.all(16.0), child: CircularProgressIndicator())),
                            error: (error, stack) => Center(child: Padding(padding: const EdgeInsets.all(16.0), child: Text("Error al buscar: $error"))),
                          ),
                        ] else
                          const Expanded(child: SharedPanelContentBody()),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

class MedicineSearchResultItem extends ConsumerWidget {
  final Product product;
  final VoidCallback onPanelClose;
  final bool filterByExactName;

  const MedicineSearchResultItem({super.key, required this.product, required this.onPanelClose, required this.filterByExactName});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final theme = Theme.of(context);
    // Get ScaffoldMessengerState before potential deactivation
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    return ListTile(
      title: Text(product.nombreProducto, style: theme.textTheme.titleMedium),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [const SizedBox(height: 4), Text("Concentración: ${product.concent}", style: theme.textTheme.bodyMedium), Text("Forma farmacéutica: ${product.nombreFormaFarmaceutica}", style: theme.textTheme.bodyMedium)],
      ),
      onTap: () async {
        // Check and show disclaimer for first-time data source usage
        final disclaimerService = ref.read(disclaimerServiceProvider);
        final shouldShowDisclaimer = await disclaimerService.shouldShowDisclaimer();

        if (shouldShowDisclaimer && context.mounted) {
          final acknowledged = await disclaimerService.showFirstTimeDataSourceDisclaimer(context);
          if (!acknowledged) {
            // User dismissed disclaimer, don't proceed with search
            return;
          }
        }

        // Guardar el medicamento seleccionado
        ref.read(selectedMedicineProvider.notifier).state = product;
        // Obtener la ubicación seleccionada del ubigeo
        final selectedUbigeo = ref.read(selectedUbigeoProvider);

        if (selectedUbigeo.selectedDepartment != null && selectedUbigeo.selectedProvince != null && selectedUbigeo.selectedDistrict != null) {
          // 1. IMPORTANT: Reset global cancellation flag for this new search job.
          ref.read(geocodingCancellationProvider.notifier).state = false;

          // 2. Create a NEW, UNIQUE Dio CancelToken for this specific geocoding job.
          final CancelToken jobSpecificCancelToken = CancelToken();

          // Set loading state to true when starting
          ref.read(markerLoadingProvider.notifier).state = true;

          // Mostrar un indicador de carga
          scaffoldMessenger.showSnackBar(const SnackBar(content: Text('Buscando establecimientos con este medicamento...'), duration: Duration(seconds: 2)));

          try {
            // Step 1: Get the marker notifier that we'll use later
            final markerNotifier = ref.read(establishmentMarkersProvider.notifier);

            // Get filter state and include in request
            final filterState = ref.read(establishmentFilterProvider);

            // Step 2: Get medicine prices
            final prices = await ref.read(
              medicinePricesProvider({
                'productCode': product.grupo,
                'departmentCode': selectedUbigeo.selectedDepartment!.codigo,
                'provinceCode': selectedUbigeo.selectedProvince!.codigo,
                'districtCode': selectedUbigeo.selectedDistrict!.codigo,
                'groupCode': product.codGrupoFF,
                'concentration': product.concent,
                'page': 1,
                'pageSize': 100, // Consider if this pageSize needs to be managed for very large datasets
                'establishmentCategory': filterState.selectedCategory?.code,
                'establishmentType': filterState.selectedType?.code,
                'establishmentName': filterState.establishmentName,
                'laboratoryName': filterState.laboratoryName,
                'productName': filterByExactName ? product.nombreProducto : null,
              }).future,
            );

            // Step 3: Get the repository we'll need for geocoding
            final geocodingRepository = ref.read(geocodingRepositoryProvider);

            // Capture other notifiers needed by the background process
            final loadingNotifier = ref.read(markerLoadingProvider.notifier);
            final globalCancellationControllerForJob = ref.read(geocodingCancellationProvider.notifier); // Pass the controller
            final progressController = ref.read(geocodingProgressProvider.notifier);
            final countsController = ref.read(geocodingCountsProvider.notifier);

            // Close the panel immediately to not block interaction
            onPanelClose();

            if (prices.isNotEmpty) {
              scaffoldMessenger.showSnackBar(const SnackBar(content: Text('Procesando ubicaciones de establecimientos...'), duration: Duration(seconds: 2)));

              await _processGeocodingInBackground(
                geocodingRepository: geocodingRepository,
                markerNotifier: markerNotifier,
                prices: prices,
                product: product,
                scaffoldMessenger: scaffoldMessenger,
                loadingNotifier: loadingNotifier,
                globalCancellationController: globalCancellationControllerForJob,
                progressController: progressController,
                countsController: countsController,
                jobSpecificCancelToken: jobSpecificCancelToken, // Pass the unique token for this job
              );
            } else {
              loadingNotifier.state = false;
              scaffoldMessenger.showSnackBar(SnackBar(content: Text('No se encontraron establecimientos con ${product.nombreProducto} en la ubicación seleccionada'), duration: const Duration(seconds: 3)));
            }
          } catch (e, s) {
            // Ensure loading is off and log error
            ref.read(markerLoadingProvider.notifier).state = false;
            print('Error al iniciar la búsqueda de establecimientos: $e\n$s');
            scaffoldMessenger.showSnackBar(SnackBar(content: Text('Error al iniciar búsqueda: $e'), backgroundColor: Colors.red, duration: const Duration(seconds: 3)));
          }
        } else {
          scaffoldMessenger.showSnackBar(const SnackBar(content: Text('Por favor seleccione una ubicación (departamento, provincia y distrito) primero'), duration: Duration(seconds: 3)));
          onPanelClose();
        }
      },
    );
  }

  static Future<void> _processGeocodingInBackground({
    required GeocodingRepository geocodingRepository,
    required EstablishmentMarkersNotifier markerNotifier,
    required List<ProductPrice> prices,
    required Product product,
    required ScaffoldMessengerState scaffoldMessenger,
    required StateController<bool> loadingNotifier,
    required StateController<bool> globalCancellationController,
    required StateController<double> progressController,
    required StateController<({int total, int processed})> countsController,
    required CancelToken jobSpecificCancelToken,
  }) async {
    int foundCount = 0;

    bool checkCancelAndCleanup() {
      bool wasCancelledInternally = false;
      if (jobSpecificCancelToken.isCancelled) {
        wasCancelledInternally = true;
      } else if (globalCancellationController.state) {
        jobSpecificCancelToken.cancel('Global cancellation provider triggered job-specific token');
        wasCancelledInternally = true;
      }

      if (wasCancelledInternally) {
        markerNotifier.clearMarkers();
        if (loadingNotifier.state) {
          loadingNotifier.state = false;
        }
      }
      return wasCancelledInternally;
    }

    if (checkCancelAndCleanup()) {
      return;
    }

    try {
      final addressesToProcess =
          prices.map((price) {
            final address = "${price.direccion}, ${price.distrito}, ${price.provincia}, ${price.departamento}, Perú";
            return (price, address);
          }).toList();

      final totalCount = addressesToProcess.length;
      int processedCount = 0;

      if (!checkCancelAndCleanup()) {
        progressController.state = 0.0;
        countsController.state = (total: totalCount, processed: 0);
      }

      for (int i = 0; i < addressesToProcess.length; i += 3) {
        if (checkCancelAndCleanup()) {
          return;
        }

        final batch = addressesToProcess.skip(i).take(3);
        List<Future<void>> batchFutures = [];

        for (final item in batch) {
          if (checkCancelAndCleanup()) break;

          final (price, address) = item;

          batchFutures.add(() async {
            if (checkCancelAndCleanup()) return;

            GeocodingResult? geocodingResult;
            try {
              // Use new Google Maps geocoding method instead of the previous one
              geocodingResult = await geocodingRepository.geocodeWithGoogleMaps(address, cancelToken: jobSpecificCancelToken);
            } catch (e) {
              // Errors are silently caught if related to cancellation,
              // otherwise they might propagate if not DioException with CancelToken.
              print('Error during Google Maps geocoding: $e');
            }

            if (checkCancelAndCleanup()) return;

            processedCount++;
            if (!jobSpecificCancelToken.isCancelled) {
              double progress = totalCount > 0 ? processedCount / totalCount.toDouble() : 0.0;
              progressController.state = progress;
              countsController.state = (total: totalCount, processed: processedCount);
            }

            if (geocodingResult != null && !jobSpecificCancelToken.isCancelled) {
              foundCount++;
              final newMarker = EstablishmentMarker(price: price, location: LatLng(geocodingResult.latitude, geocodingResult.longitude));

              if (!jobSpecificCancelToken.isCancelled) {
                if (foundCount == 1) {
                  markerNotifier.setMarkers([newMarker]);
                } else {
                  markerNotifier.addMarker(newMarker);
                }
              }
            }
          }());
        }

        if (checkCancelAndCleanup()) return;

        if (batchFutures.isNotEmpty) {
          try {
            await Future.wait(batchFutures);
          } catch (e) {
            // Error during Future.wait, usually from a future that threw an unhandled error.
            if (checkCancelAndCleanup()) return;
          }
        }

        if (checkCancelAndCleanup()) return;

        if (!jobSpecificCancelToken.isCancelled) {
          await Future.delayed(const Duration(milliseconds: 300));
        }
      }

      if (!jobSpecificCancelToken.isCancelled) {
        progressController.state = 1.0;
        countsController.state = (total: totalCount, processed: processedCount);
      }
    } catch (e, s) {
      // Outer error, ensure cleanup. This log can be kept for debugging crashes.
      print("Geocoding job: Outer error in geocoding process: $e\n$s");
      checkCancelAndCleanup();
    } finally {
      if (!jobSpecificCancelToken.isCancelled && loadingNotifier.state) {
        loadingNotifier.state = false;
      }
    }
  }
}

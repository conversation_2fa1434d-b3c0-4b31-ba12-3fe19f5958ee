import 'package:buscafarma/features/map/domain/entities/ubigeo_models.dart';

abstract class UbigeoRepository {
  /// Load and initialize data from the ubigeo.json file
  Future<void> loadData();

  /// Get all departments
  List<Department> getDepartments();

  /// Get provinces for a specific department
  List<Province> getProvincesByDepartment(String departmentCode);

  /// Get districts for a specific province
  List<District> getDistrictsByProvince(String provinceCode);

  /// Search for a department by name (partial match)
  List<Department> searchDepartments(String query);

  /// Search for a province by name (partial match)
  List<Province> searchProvinces(String query);

  /// Search for a district by name (partial match)
  List<District> searchDistricts(String query);
}

import 'package:dartz/dartz.dart';
import 'package:buscafarma/core/error/failures.dart';
import 'package:buscafarma/features/map/domain/entities/location.dart';

abstract class LocationRepository {
  /// Gets the current device location
  Future<Either<Failure, Location>> getCurrentLocation();

  /// Checks if location services are enabled
  Future<bool> isLocationServiceEnabled();

  /// Requests location permission
  Future<bool> requestLocationPermission();
}

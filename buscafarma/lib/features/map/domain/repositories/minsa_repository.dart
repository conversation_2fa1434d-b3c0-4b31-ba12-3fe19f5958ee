import 'package:buscafarma/features/map/domain/entities/minsa_models.dart';

abstract class MinsaRepository {
  /// Busca productos por nombre
  Future<List<Product>> searchProducts(String query, {int page = 1, int pageSize = 10});

  /// Obtiene los precios de un producto específico
  Future<List<ProductPrice>> findProductPrices({
    required int productCode,
    String? productName,
    required String departmentCode,
    required String provinceCode,
    required String districtCode,
    String? establishmentName,
    String? laboratoryName,
    String? catEstablecimiento,
    String? codTipoEstablecimiento,
    required String groupCode,
    required String concentration,
    int? page,
    int? pageSize,
  });

  /// Obtiene los detalles de un producto específico
  Future<ProductDetail?> getProductDetail(int productCode, String establishmentCode);
}

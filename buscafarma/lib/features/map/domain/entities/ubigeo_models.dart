import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'ubigeo_models.freezed.dart';
part 'ubigeo_models.g.dart';

// Models for geographic data (departments, provinces, districts)
@freezed
abstract class UbigeoResponse with _$UbigeoResponse {
  const factory UbigeoResponse({
    required String codigo,
    required String mensaje,
    required List<Department> data,
    dynamic entidad,
    required int cantidad,
    dynamic codigoEntidad,
    dynamic codigosValidos,
    dynamic paginas,
    dynamic totalData,
    dynamic resultadoProceso,
    dynamic mensajesErrores,
  }) = _UbigeoResponse;

  factory UbigeoResponse.fromJson(Map<String, dynamic> json) => _$UbigeoResponseFromJson(json);
}

@freezed
abstract class Department with _$Department {
  const factory Department({required String codigo, required String descripcion, required List<Province> provincias}) = _Department;

  factory Department.fromJson(Map<String, dynamic> json) => _$DepartmentFromJson(json);
}

@freezed
abstract class Province with _$Province {
  const factory Province({required String codigo, required String descripcion, required String codigoDepartamento, List<District>? distritos, String? codigoProvincia}) = _Province;

  factory Province.fromJson(Map<String, dynamic> json) => _$ProvinceFromJson(json);
}

@freezed
abstract class District with _$District {
  const factory District({required String codigo, required String descripcion, required String codigoProvincia}) = _District;

  factory District.fromJson(Map<String, dynamic> json) => _$DistrictFromJson(json);
}

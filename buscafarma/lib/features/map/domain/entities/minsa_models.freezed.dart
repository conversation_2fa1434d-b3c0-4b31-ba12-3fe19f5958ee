// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'minsa_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$MinsaResponse<T> implements DiagnosticableTreeMixin {

 String get codigo; String get mensaje; List<T> get data; T? get entidad; int? get cantidad;
/// Create a copy of MinsaResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$MinsaResponseCopyWith<T, MinsaResponse<T>> get copyWith => _$MinsaResponseCopyWithImpl<T, MinsaResponse<T>>(this as MinsaResponse<T>, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'MinsaResponse<$T>'))
    ..add(DiagnosticsProperty('codigo', codigo))..add(DiagnosticsProperty('mensaje', mensaje))..add(DiagnosticsProperty('data', data))..add(DiagnosticsProperty('entidad', entidad))..add(DiagnosticsProperty('cantidad', cantidad));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is MinsaResponse<T>&&(identical(other.codigo, codigo) || other.codigo == codigo)&&(identical(other.mensaje, mensaje) || other.mensaje == mensaje)&&const DeepCollectionEquality().equals(other.data, data)&&const DeepCollectionEquality().equals(other.entidad, entidad)&&(identical(other.cantidad, cantidad) || other.cantidad == cantidad));
}


@override
int get hashCode => Object.hash(runtimeType,codigo,mensaje,const DeepCollectionEquality().hash(data),const DeepCollectionEquality().hash(entidad),cantidad);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'MinsaResponse<$T>(codigo: $codigo, mensaje: $mensaje, data: $data, entidad: $entidad, cantidad: $cantidad)';
}


}

/// @nodoc
abstract mixin class $MinsaResponseCopyWith<T,$Res>  {
  factory $MinsaResponseCopyWith(MinsaResponse<T> value, $Res Function(MinsaResponse<T>) _then) = _$MinsaResponseCopyWithImpl;
@useResult
$Res call({
 String codigo, String mensaje, List<T> data, T? entidad, int? cantidad
});




}
/// @nodoc
class _$MinsaResponseCopyWithImpl<T,$Res>
    implements $MinsaResponseCopyWith<T, $Res> {
  _$MinsaResponseCopyWithImpl(this._self, this._then);

  final MinsaResponse<T> _self;
  final $Res Function(MinsaResponse<T>) _then;

/// Create a copy of MinsaResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? codigo = null,Object? mensaje = null,Object? data = null,Object? entidad = freezed,Object? cantidad = freezed,}) {
  return _then(_self.copyWith(
codigo: null == codigo ? _self.codigo : codigo // ignore: cast_nullable_to_non_nullable
as String,mensaje: null == mensaje ? _self.mensaje : mensaje // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as List<T>,entidad: freezed == entidad ? _self.entidad : entidad // ignore: cast_nullable_to_non_nullable
as T?,cantidad: freezed == cantidad ? _self.cantidad : cantidad // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}

}


/// @nodoc


class _MinsaResponse<T> with DiagnosticableTreeMixin implements MinsaResponse<T> {
  const _MinsaResponse({required this.codigo, required this.mensaje, required final  List<T> data, this.entidad, this.cantidad}): _data = data;
  

@override final  String codigo;
@override final  String mensaje;
 final  List<T> _data;
@override List<T> get data {
  if (_data is EqualUnmodifiableListView) return _data;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_data);
}

@override final  T? entidad;
@override final  int? cantidad;

/// Create a copy of MinsaResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$MinsaResponseCopyWith<T, _MinsaResponse<T>> get copyWith => __$MinsaResponseCopyWithImpl<T, _MinsaResponse<T>>(this, _$identity);


@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'MinsaResponse<$T>'))
    ..add(DiagnosticsProperty('codigo', codigo))..add(DiagnosticsProperty('mensaje', mensaje))..add(DiagnosticsProperty('data', data))..add(DiagnosticsProperty('entidad', entidad))..add(DiagnosticsProperty('cantidad', cantidad));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _MinsaResponse<T>&&(identical(other.codigo, codigo) || other.codigo == codigo)&&(identical(other.mensaje, mensaje) || other.mensaje == mensaje)&&const DeepCollectionEquality().equals(other._data, _data)&&const DeepCollectionEquality().equals(other.entidad, entidad)&&(identical(other.cantidad, cantidad) || other.cantidad == cantidad));
}


@override
int get hashCode => Object.hash(runtimeType,codigo,mensaje,const DeepCollectionEquality().hash(_data),const DeepCollectionEquality().hash(entidad),cantidad);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'MinsaResponse<$T>(codigo: $codigo, mensaje: $mensaje, data: $data, entidad: $entidad, cantidad: $cantidad)';
}


}

/// @nodoc
abstract mixin class _$MinsaResponseCopyWith<T,$Res> implements $MinsaResponseCopyWith<T, $Res> {
  factory _$MinsaResponseCopyWith(_MinsaResponse<T> value, $Res Function(_MinsaResponse<T>) _then) = __$MinsaResponseCopyWithImpl;
@override @useResult
$Res call({
 String codigo, String mensaje, List<T> data, T? entidad, int? cantidad
});




}
/// @nodoc
class __$MinsaResponseCopyWithImpl<T,$Res>
    implements _$MinsaResponseCopyWith<T, $Res> {
  __$MinsaResponseCopyWithImpl(this._self, this._then);

  final _MinsaResponse<T> _self;
  final $Res Function(_MinsaResponse<T>) _then;

/// Create a copy of MinsaResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? codigo = null,Object? mensaje = null,Object? data = null,Object? entidad = freezed,Object? cantidad = freezed,}) {
  return _then(_MinsaResponse<T>(
codigo: null == codigo ? _self.codigo : codigo // ignore: cast_nullable_to_non_nullable
as String,mensaje: null == mensaje ? _self.mensaje : mensaje // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self._data : data // ignore: cast_nullable_to_non_nullable
as List<T>,entidad: freezed == entidad ? _self.entidad : entidad // ignore: cast_nullable_to_non_nullable
as T?,cantidad: freezed == cantidad ? _self.cantidad : cantidad // ignore: cast_nullable_to_non_nullable
as int?,
  ));
}


}


/// @nodoc
mixin _$ProductDetail implements DiagnosticableTreeMixin {

 double get precio1; double? get precio2; String get nombreProducto; String get paisFabricacion; String get registroSanitario; String get condicionVenta; String get tipoProducto; String get nombreTitular; String get nombreFabricante; String get presentacion; String get laboratorio; String get directorTecnico; String get nombreComercial; String get telefono; String get direccion; String get departamento; String get provincia; String get distrito; String get horarioAtencion; String get ubigeo; String get catCodigo; String get email; String get ruc;
/// Create a copy of ProductDetail
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductDetailCopyWith<ProductDetail> get copyWith => _$ProductDetailCopyWithImpl<ProductDetail>(this as ProductDetail, _$identity);

  /// Serializes this ProductDetail to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ProductDetail'))
    ..add(DiagnosticsProperty('precio1', precio1))..add(DiagnosticsProperty('precio2', precio2))..add(DiagnosticsProperty('nombreProducto', nombreProducto))..add(DiagnosticsProperty('paisFabricacion', paisFabricacion))..add(DiagnosticsProperty('registroSanitario', registroSanitario))..add(DiagnosticsProperty('condicionVenta', condicionVenta))..add(DiagnosticsProperty('tipoProducto', tipoProducto))..add(DiagnosticsProperty('nombreTitular', nombreTitular))..add(DiagnosticsProperty('nombreFabricante', nombreFabricante))..add(DiagnosticsProperty('presentacion', presentacion))..add(DiagnosticsProperty('laboratorio', laboratorio))..add(DiagnosticsProperty('directorTecnico', directorTecnico))..add(DiagnosticsProperty('nombreComercial', nombreComercial))..add(DiagnosticsProperty('telefono', telefono))..add(DiagnosticsProperty('direccion', direccion))..add(DiagnosticsProperty('departamento', departamento))..add(DiagnosticsProperty('provincia', provincia))..add(DiagnosticsProperty('distrito', distrito))..add(DiagnosticsProperty('horarioAtencion', horarioAtencion))..add(DiagnosticsProperty('ubigeo', ubigeo))..add(DiagnosticsProperty('catCodigo', catCodigo))..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('ruc', ruc));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductDetail&&(identical(other.precio1, precio1) || other.precio1 == precio1)&&(identical(other.precio2, precio2) || other.precio2 == precio2)&&(identical(other.nombreProducto, nombreProducto) || other.nombreProducto == nombreProducto)&&(identical(other.paisFabricacion, paisFabricacion) || other.paisFabricacion == paisFabricacion)&&(identical(other.registroSanitario, registroSanitario) || other.registroSanitario == registroSanitario)&&(identical(other.condicionVenta, condicionVenta) || other.condicionVenta == condicionVenta)&&(identical(other.tipoProducto, tipoProducto) || other.tipoProducto == tipoProducto)&&(identical(other.nombreTitular, nombreTitular) || other.nombreTitular == nombreTitular)&&(identical(other.nombreFabricante, nombreFabricante) || other.nombreFabricante == nombreFabricante)&&(identical(other.presentacion, presentacion) || other.presentacion == presentacion)&&(identical(other.laboratorio, laboratorio) || other.laboratorio == laboratorio)&&(identical(other.directorTecnico, directorTecnico) || other.directorTecnico == directorTecnico)&&(identical(other.nombreComercial, nombreComercial) || other.nombreComercial == nombreComercial)&&(identical(other.telefono, telefono) || other.telefono == telefono)&&(identical(other.direccion, direccion) || other.direccion == direccion)&&(identical(other.departamento, departamento) || other.departamento == departamento)&&(identical(other.provincia, provincia) || other.provincia == provincia)&&(identical(other.distrito, distrito) || other.distrito == distrito)&&(identical(other.horarioAtencion, horarioAtencion) || other.horarioAtencion == horarioAtencion)&&(identical(other.ubigeo, ubigeo) || other.ubigeo == ubigeo)&&(identical(other.catCodigo, catCodigo) || other.catCodigo == catCodigo)&&(identical(other.email, email) || other.email == email)&&(identical(other.ruc, ruc) || other.ruc == ruc));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,precio1,precio2,nombreProducto,paisFabricacion,registroSanitario,condicionVenta,tipoProducto,nombreTitular,nombreFabricante,presentacion,laboratorio,directorTecnico,nombreComercial,telefono,direccion,departamento,provincia,distrito,horarioAtencion,ubigeo,catCodigo,email,ruc]);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ProductDetail(precio1: $precio1, precio2: $precio2, nombreProducto: $nombreProducto, paisFabricacion: $paisFabricacion, registroSanitario: $registroSanitario, condicionVenta: $condicionVenta, tipoProducto: $tipoProducto, nombreTitular: $nombreTitular, nombreFabricante: $nombreFabricante, presentacion: $presentacion, laboratorio: $laboratorio, directorTecnico: $directorTecnico, nombreComercial: $nombreComercial, telefono: $telefono, direccion: $direccion, departamento: $departamento, provincia: $provincia, distrito: $distrito, horarioAtencion: $horarioAtencion, ubigeo: $ubigeo, catCodigo: $catCodigo, email: $email, ruc: $ruc)';
}


}

/// @nodoc
abstract mixin class $ProductDetailCopyWith<$Res>  {
  factory $ProductDetailCopyWith(ProductDetail value, $Res Function(ProductDetail) _then) = _$ProductDetailCopyWithImpl;
@useResult
$Res call({
 double precio1, double? precio2, String nombreProducto, String paisFabricacion, String registroSanitario, String condicionVenta, String tipoProducto, String nombreTitular, String nombreFabricante, String presentacion, String laboratorio, String directorTecnico, String nombreComercial, String telefono, String direccion, String departamento, String provincia, String distrito, String horarioAtencion, String ubigeo, String catCodigo, String email, String ruc
});




}
/// @nodoc
class _$ProductDetailCopyWithImpl<$Res>
    implements $ProductDetailCopyWith<$Res> {
  _$ProductDetailCopyWithImpl(this._self, this._then);

  final ProductDetail _self;
  final $Res Function(ProductDetail) _then;

/// Create a copy of ProductDetail
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? precio1 = null,Object? precio2 = freezed,Object? nombreProducto = null,Object? paisFabricacion = null,Object? registroSanitario = null,Object? condicionVenta = null,Object? tipoProducto = null,Object? nombreTitular = null,Object? nombreFabricante = null,Object? presentacion = null,Object? laboratorio = null,Object? directorTecnico = null,Object? nombreComercial = null,Object? telefono = null,Object? direccion = null,Object? departamento = null,Object? provincia = null,Object? distrito = null,Object? horarioAtencion = null,Object? ubigeo = null,Object? catCodigo = null,Object? email = null,Object? ruc = null,}) {
  return _then(_self.copyWith(
precio1: null == precio1 ? _self.precio1 : precio1 // ignore: cast_nullable_to_non_nullable
as double,precio2: freezed == precio2 ? _self.precio2 : precio2 // ignore: cast_nullable_to_non_nullable
as double?,nombreProducto: null == nombreProducto ? _self.nombreProducto : nombreProducto // ignore: cast_nullable_to_non_nullable
as String,paisFabricacion: null == paisFabricacion ? _self.paisFabricacion : paisFabricacion // ignore: cast_nullable_to_non_nullable
as String,registroSanitario: null == registroSanitario ? _self.registroSanitario : registroSanitario // ignore: cast_nullable_to_non_nullable
as String,condicionVenta: null == condicionVenta ? _self.condicionVenta : condicionVenta // ignore: cast_nullable_to_non_nullable
as String,tipoProducto: null == tipoProducto ? _self.tipoProducto : tipoProducto // ignore: cast_nullable_to_non_nullable
as String,nombreTitular: null == nombreTitular ? _self.nombreTitular : nombreTitular // ignore: cast_nullable_to_non_nullable
as String,nombreFabricante: null == nombreFabricante ? _self.nombreFabricante : nombreFabricante // ignore: cast_nullable_to_non_nullable
as String,presentacion: null == presentacion ? _self.presentacion : presentacion // ignore: cast_nullable_to_non_nullable
as String,laboratorio: null == laboratorio ? _self.laboratorio : laboratorio // ignore: cast_nullable_to_non_nullable
as String,directorTecnico: null == directorTecnico ? _self.directorTecnico : directorTecnico // ignore: cast_nullable_to_non_nullable
as String,nombreComercial: null == nombreComercial ? _self.nombreComercial : nombreComercial // ignore: cast_nullable_to_non_nullable
as String,telefono: null == telefono ? _self.telefono : telefono // ignore: cast_nullable_to_non_nullable
as String,direccion: null == direccion ? _self.direccion : direccion // ignore: cast_nullable_to_non_nullable
as String,departamento: null == departamento ? _self.departamento : departamento // ignore: cast_nullable_to_non_nullable
as String,provincia: null == provincia ? _self.provincia : provincia // ignore: cast_nullable_to_non_nullable
as String,distrito: null == distrito ? _self.distrito : distrito // ignore: cast_nullable_to_non_nullable
as String,horarioAtencion: null == horarioAtencion ? _self.horarioAtencion : horarioAtencion // ignore: cast_nullable_to_non_nullable
as String,ubigeo: null == ubigeo ? _self.ubigeo : ubigeo // ignore: cast_nullable_to_non_nullable
as String,catCodigo: null == catCodigo ? _self.catCodigo : catCodigo // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,ruc: null == ruc ? _self.ruc : ruc // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ProductDetail with DiagnosticableTreeMixin implements ProductDetail {
  const _ProductDetail({required this.precio1, this.precio2, required this.nombreProducto, required this.paisFabricacion, required this.registroSanitario, required this.condicionVenta, required this.tipoProducto, required this.nombreTitular, required this.nombreFabricante, required this.presentacion, required this.laboratorio, required this.directorTecnico, required this.nombreComercial, required this.telefono, required this.direccion, required this.departamento, required this.provincia, required this.distrito, required this.horarioAtencion, required this.ubigeo, required this.catCodigo, required this.email, required this.ruc});
  factory _ProductDetail.fromJson(Map<String, dynamic> json) => _$ProductDetailFromJson(json);

@override final  double precio1;
@override final  double? precio2;
@override final  String nombreProducto;
@override final  String paisFabricacion;
@override final  String registroSanitario;
@override final  String condicionVenta;
@override final  String tipoProducto;
@override final  String nombreTitular;
@override final  String nombreFabricante;
@override final  String presentacion;
@override final  String laboratorio;
@override final  String directorTecnico;
@override final  String nombreComercial;
@override final  String telefono;
@override final  String direccion;
@override final  String departamento;
@override final  String provincia;
@override final  String distrito;
@override final  String horarioAtencion;
@override final  String ubigeo;
@override final  String catCodigo;
@override final  String email;
@override final  String ruc;

/// Create a copy of ProductDetail
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductDetailCopyWith<_ProductDetail> get copyWith => __$ProductDetailCopyWithImpl<_ProductDetail>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductDetailToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ProductDetail'))
    ..add(DiagnosticsProperty('precio1', precio1))..add(DiagnosticsProperty('precio2', precio2))..add(DiagnosticsProperty('nombreProducto', nombreProducto))..add(DiagnosticsProperty('paisFabricacion', paisFabricacion))..add(DiagnosticsProperty('registroSanitario', registroSanitario))..add(DiagnosticsProperty('condicionVenta', condicionVenta))..add(DiagnosticsProperty('tipoProducto', tipoProducto))..add(DiagnosticsProperty('nombreTitular', nombreTitular))..add(DiagnosticsProperty('nombreFabricante', nombreFabricante))..add(DiagnosticsProperty('presentacion', presentacion))..add(DiagnosticsProperty('laboratorio', laboratorio))..add(DiagnosticsProperty('directorTecnico', directorTecnico))..add(DiagnosticsProperty('nombreComercial', nombreComercial))..add(DiagnosticsProperty('telefono', telefono))..add(DiagnosticsProperty('direccion', direccion))..add(DiagnosticsProperty('departamento', departamento))..add(DiagnosticsProperty('provincia', provincia))..add(DiagnosticsProperty('distrito', distrito))..add(DiagnosticsProperty('horarioAtencion', horarioAtencion))..add(DiagnosticsProperty('ubigeo', ubigeo))..add(DiagnosticsProperty('catCodigo', catCodigo))..add(DiagnosticsProperty('email', email))..add(DiagnosticsProperty('ruc', ruc));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductDetail&&(identical(other.precio1, precio1) || other.precio1 == precio1)&&(identical(other.precio2, precio2) || other.precio2 == precio2)&&(identical(other.nombreProducto, nombreProducto) || other.nombreProducto == nombreProducto)&&(identical(other.paisFabricacion, paisFabricacion) || other.paisFabricacion == paisFabricacion)&&(identical(other.registroSanitario, registroSanitario) || other.registroSanitario == registroSanitario)&&(identical(other.condicionVenta, condicionVenta) || other.condicionVenta == condicionVenta)&&(identical(other.tipoProducto, tipoProducto) || other.tipoProducto == tipoProducto)&&(identical(other.nombreTitular, nombreTitular) || other.nombreTitular == nombreTitular)&&(identical(other.nombreFabricante, nombreFabricante) || other.nombreFabricante == nombreFabricante)&&(identical(other.presentacion, presentacion) || other.presentacion == presentacion)&&(identical(other.laboratorio, laboratorio) || other.laboratorio == laboratorio)&&(identical(other.directorTecnico, directorTecnico) || other.directorTecnico == directorTecnico)&&(identical(other.nombreComercial, nombreComercial) || other.nombreComercial == nombreComercial)&&(identical(other.telefono, telefono) || other.telefono == telefono)&&(identical(other.direccion, direccion) || other.direccion == direccion)&&(identical(other.departamento, departamento) || other.departamento == departamento)&&(identical(other.provincia, provincia) || other.provincia == provincia)&&(identical(other.distrito, distrito) || other.distrito == distrito)&&(identical(other.horarioAtencion, horarioAtencion) || other.horarioAtencion == horarioAtencion)&&(identical(other.ubigeo, ubigeo) || other.ubigeo == ubigeo)&&(identical(other.catCodigo, catCodigo) || other.catCodigo == catCodigo)&&(identical(other.email, email) || other.email == email)&&(identical(other.ruc, ruc) || other.ruc == ruc));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,precio1,precio2,nombreProducto,paisFabricacion,registroSanitario,condicionVenta,tipoProducto,nombreTitular,nombreFabricante,presentacion,laboratorio,directorTecnico,nombreComercial,telefono,direccion,departamento,provincia,distrito,horarioAtencion,ubigeo,catCodigo,email,ruc]);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ProductDetail(precio1: $precio1, precio2: $precio2, nombreProducto: $nombreProducto, paisFabricacion: $paisFabricacion, registroSanitario: $registroSanitario, condicionVenta: $condicionVenta, tipoProducto: $tipoProducto, nombreTitular: $nombreTitular, nombreFabricante: $nombreFabricante, presentacion: $presentacion, laboratorio: $laboratorio, directorTecnico: $directorTecnico, nombreComercial: $nombreComercial, telefono: $telefono, direccion: $direccion, departamento: $departamento, provincia: $provincia, distrito: $distrito, horarioAtencion: $horarioAtencion, ubigeo: $ubigeo, catCodigo: $catCodigo, email: $email, ruc: $ruc)';
}


}

/// @nodoc
abstract mixin class _$ProductDetailCopyWith<$Res> implements $ProductDetailCopyWith<$Res> {
  factory _$ProductDetailCopyWith(_ProductDetail value, $Res Function(_ProductDetail) _then) = __$ProductDetailCopyWithImpl;
@override @useResult
$Res call({
 double precio1, double? precio2, String nombreProducto, String paisFabricacion, String registroSanitario, String condicionVenta, String tipoProducto, String nombreTitular, String nombreFabricante, String presentacion, String laboratorio, String directorTecnico, String nombreComercial, String telefono, String direccion, String departamento, String provincia, String distrito, String horarioAtencion, String ubigeo, String catCodigo, String email, String ruc
});




}
/// @nodoc
class __$ProductDetailCopyWithImpl<$Res>
    implements _$ProductDetailCopyWith<$Res> {
  __$ProductDetailCopyWithImpl(this._self, this._then);

  final _ProductDetail _self;
  final $Res Function(_ProductDetail) _then;

/// Create a copy of ProductDetail
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? precio1 = null,Object? precio2 = freezed,Object? nombreProducto = null,Object? paisFabricacion = null,Object? registroSanitario = null,Object? condicionVenta = null,Object? tipoProducto = null,Object? nombreTitular = null,Object? nombreFabricante = null,Object? presentacion = null,Object? laboratorio = null,Object? directorTecnico = null,Object? nombreComercial = null,Object? telefono = null,Object? direccion = null,Object? departamento = null,Object? provincia = null,Object? distrito = null,Object? horarioAtencion = null,Object? ubigeo = null,Object? catCodigo = null,Object? email = null,Object? ruc = null,}) {
  return _then(_ProductDetail(
precio1: null == precio1 ? _self.precio1 : precio1 // ignore: cast_nullable_to_non_nullable
as double,precio2: freezed == precio2 ? _self.precio2 : precio2 // ignore: cast_nullable_to_non_nullable
as double?,nombreProducto: null == nombreProducto ? _self.nombreProducto : nombreProducto // ignore: cast_nullable_to_non_nullable
as String,paisFabricacion: null == paisFabricacion ? _self.paisFabricacion : paisFabricacion // ignore: cast_nullable_to_non_nullable
as String,registroSanitario: null == registroSanitario ? _self.registroSanitario : registroSanitario // ignore: cast_nullable_to_non_nullable
as String,condicionVenta: null == condicionVenta ? _self.condicionVenta : condicionVenta // ignore: cast_nullable_to_non_nullable
as String,tipoProducto: null == tipoProducto ? _self.tipoProducto : tipoProducto // ignore: cast_nullable_to_non_nullable
as String,nombreTitular: null == nombreTitular ? _self.nombreTitular : nombreTitular // ignore: cast_nullable_to_non_nullable
as String,nombreFabricante: null == nombreFabricante ? _self.nombreFabricante : nombreFabricante // ignore: cast_nullable_to_non_nullable
as String,presentacion: null == presentacion ? _self.presentacion : presentacion // ignore: cast_nullable_to_non_nullable
as String,laboratorio: null == laboratorio ? _self.laboratorio : laboratorio // ignore: cast_nullable_to_non_nullable
as String,directorTecnico: null == directorTecnico ? _self.directorTecnico : directorTecnico // ignore: cast_nullable_to_non_nullable
as String,nombreComercial: null == nombreComercial ? _self.nombreComercial : nombreComercial // ignore: cast_nullable_to_non_nullable
as String,telefono: null == telefono ? _self.telefono : telefono // ignore: cast_nullable_to_non_nullable
as String,direccion: null == direccion ? _self.direccion : direccion // ignore: cast_nullable_to_non_nullable
as String,departamento: null == departamento ? _self.departamento : departamento // ignore: cast_nullable_to_non_nullable
as String,provincia: null == provincia ? _self.provincia : provincia // ignore: cast_nullable_to_non_nullable
as String,distrito: null == distrito ? _self.distrito : distrito // ignore: cast_nullable_to_non_nullable
as String,horarioAtencion: null == horarioAtencion ? _self.horarioAtencion : horarioAtencion // ignore: cast_nullable_to_non_nullable
as String,ubigeo: null == ubigeo ? _self.ubigeo : ubigeo // ignore: cast_nullable_to_non_nullable
as String,catCodigo: null == catCodigo ? _self.catCodigo : catCodigo // ignore: cast_nullable_to_non_nullable
as String,email: null == email ? _self.email : email // ignore: cast_nullable_to_non_nullable
as String,ruc: null == ruc ? _self.ruc : ruc // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$Product implements DiagnosticableTreeMixin {

 int? get codigoProducto; String get nombreProducto; String get concent; String? get presentacion; int? get fracciones; String get nombreFormaFarmaceutica; String? get nroRegistroSanitario; String? get titular; int get grupo; String get codGrupoFF;
/// Create a copy of Product
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductCopyWith<Product> get copyWith => _$ProductCopyWithImpl<Product>(this as Product, _$identity);

  /// Serializes this Product to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Product'))
    ..add(DiagnosticsProperty('codigoProducto', codigoProducto))..add(DiagnosticsProperty('nombreProducto', nombreProducto))..add(DiagnosticsProperty('concent', concent))..add(DiagnosticsProperty('presentacion', presentacion))..add(DiagnosticsProperty('fracciones', fracciones))..add(DiagnosticsProperty('nombreFormaFarmaceutica', nombreFormaFarmaceutica))..add(DiagnosticsProperty('nroRegistroSanitario', nroRegistroSanitario))..add(DiagnosticsProperty('titular', titular))..add(DiagnosticsProperty('grupo', grupo))..add(DiagnosticsProperty('codGrupoFF', codGrupoFF));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Product&&(identical(other.codigoProducto, codigoProducto) || other.codigoProducto == codigoProducto)&&(identical(other.nombreProducto, nombreProducto) || other.nombreProducto == nombreProducto)&&(identical(other.concent, concent) || other.concent == concent)&&(identical(other.presentacion, presentacion) || other.presentacion == presentacion)&&(identical(other.fracciones, fracciones) || other.fracciones == fracciones)&&(identical(other.nombreFormaFarmaceutica, nombreFormaFarmaceutica) || other.nombreFormaFarmaceutica == nombreFormaFarmaceutica)&&(identical(other.nroRegistroSanitario, nroRegistroSanitario) || other.nroRegistroSanitario == nroRegistroSanitario)&&(identical(other.titular, titular) || other.titular == titular)&&(identical(other.grupo, grupo) || other.grupo == grupo)&&(identical(other.codGrupoFF, codGrupoFF) || other.codGrupoFF == codGrupoFF));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codigoProducto,nombreProducto,concent,presentacion,fracciones,nombreFormaFarmaceutica,nroRegistroSanitario,titular,grupo,codGrupoFF);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Product(codigoProducto: $codigoProducto, nombreProducto: $nombreProducto, concent: $concent, presentacion: $presentacion, fracciones: $fracciones, nombreFormaFarmaceutica: $nombreFormaFarmaceutica, nroRegistroSanitario: $nroRegistroSanitario, titular: $titular, grupo: $grupo, codGrupoFF: $codGrupoFF)';
}


}

/// @nodoc
abstract mixin class $ProductCopyWith<$Res>  {
  factory $ProductCopyWith(Product value, $Res Function(Product) _then) = _$ProductCopyWithImpl;
@useResult
$Res call({
 int? codigoProducto, String nombreProducto, String concent, String? presentacion, int? fracciones, String nombreFormaFarmaceutica, String? nroRegistroSanitario, String? titular, int grupo, String codGrupoFF
});




}
/// @nodoc
class _$ProductCopyWithImpl<$Res>
    implements $ProductCopyWith<$Res> {
  _$ProductCopyWithImpl(this._self, this._then);

  final Product _self;
  final $Res Function(Product) _then;

/// Create a copy of Product
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? codigoProducto = freezed,Object? nombreProducto = null,Object? concent = null,Object? presentacion = freezed,Object? fracciones = freezed,Object? nombreFormaFarmaceutica = null,Object? nroRegistroSanitario = freezed,Object? titular = freezed,Object? grupo = null,Object? codGrupoFF = null,}) {
  return _then(_self.copyWith(
codigoProducto: freezed == codigoProducto ? _self.codigoProducto : codigoProducto // ignore: cast_nullable_to_non_nullable
as int?,nombreProducto: null == nombreProducto ? _self.nombreProducto : nombreProducto // ignore: cast_nullable_to_non_nullable
as String,concent: null == concent ? _self.concent : concent // ignore: cast_nullable_to_non_nullable
as String,presentacion: freezed == presentacion ? _self.presentacion : presentacion // ignore: cast_nullable_to_non_nullable
as String?,fracciones: freezed == fracciones ? _self.fracciones : fracciones // ignore: cast_nullable_to_non_nullable
as int?,nombreFormaFarmaceutica: null == nombreFormaFarmaceutica ? _self.nombreFormaFarmaceutica : nombreFormaFarmaceutica // ignore: cast_nullable_to_non_nullable
as String,nroRegistroSanitario: freezed == nroRegistroSanitario ? _self.nroRegistroSanitario : nroRegistroSanitario // ignore: cast_nullable_to_non_nullable
as String?,titular: freezed == titular ? _self.titular : titular // ignore: cast_nullable_to_non_nullable
as String?,grupo: null == grupo ? _self.grupo : grupo // ignore: cast_nullable_to_non_nullable
as int,codGrupoFF: null == codGrupoFF ? _self.codGrupoFF : codGrupoFF // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Product with DiagnosticableTreeMixin implements Product {
  const _Product({this.codigoProducto, required this.nombreProducto, required this.concent, this.presentacion, this.fracciones, required this.nombreFormaFarmaceutica, this.nroRegistroSanitario, this.titular, required this.grupo, required this.codGrupoFF});
  factory _Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);

@override final  int? codigoProducto;
@override final  String nombreProducto;
@override final  String concent;
@override final  String? presentacion;
@override final  int? fracciones;
@override final  String nombreFormaFarmaceutica;
@override final  String? nroRegistroSanitario;
@override final  String? titular;
@override final  int grupo;
@override final  String codGrupoFF;

/// Create a copy of Product
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductCopyWith<_Product> get copyWith => __$ProductCopyWithImpl<_Product>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Product'))
    ..add(DiagnosticsProperty('codigoProducto', codigoProducto))..add(DiagnosticsProperty('nombreProducto', nombreProducto))..add(DiagnosticsProperty('concent', concent))..add(DiagnosticsProperty('presentacion', presentacion))..add(DiagnosticsProperty('fracciones', fracciones))..add(DiagnosticsProperty('nombreFormaFarmaceutica', nombreFormaFarmaceutica))..add(DiagnosticsProperty('nroRegistroSanitario', nroRegistroSanitario))..add(DiagnosticsProperty('titular', titular))..add(DiagnosticsProperty('grupo', grupo))..add(DiagnosticsProperty('codGrupoFF', codGrupoFF));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Product&&(identical(other.codigoProducto, codigoProducto) || other.codigoProducto == codigoProducto)&&(identical(other.nombreProducto, nombreProducto) || other.nombreProducto == nombreProducto)&&(identical(other.concent, concent) || other.concent == concent)&&(identical(other.presentacion, presentacion) || other.presentacion == presentacion)&&(identical(other.fracciones, fracciones) || other.fracciones == fracciones)&&(identical(other.nombreFormaFarmaceutica, nombreFormaFarmaceutica) || other.nombreFormaFarmaceutica == nombreFormaFarmaceutica)&&(identical(other.nroRegistroSanitario, nroRegistroSanitario) || other.nroRegistroSanitario == nroRegistroSanitario)&&(identical(other.titular, titular) || other.titular == titular)&&(identical(other.grupo, grupo) || other.grupo == grupo)&&(identical(other.codGrupoFF, codGrupoFF) || other.codGrupoFF == codGrupoFF));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codigoProducto,nombreProducto,concent,presentacion,fracciones,nombreFormaFarmaceutica,nroRegistroSanitario,titular,grupo,codGrupoFF);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Product(codigoProducto: $codigoProducto, nombreProducto: $nombreProducto, concent: $concent, presentacion: $presentacion, fracciones: $fracciones, nombreFormaFarmaceutica: $nombreFormaFarmaceutica, nroRegistroSanitario: $nroRegistroSanitario, titular: $titular, grupo: $grupo, codGrupoFF: $codGrupoFF)';
}


}

/// @nodoc
abstract mixin class _$ProductCopyWith<$Res> implements $ProductCopyWith<$Res> {
  factory _$ProductCopyWith(_Product value, $Res Function(_Product) _then) = __$ProductCopyWithImpl;
@override @useResult
$Res call({
 int? codigoProducto, String nombreProducto, String concent, String? presentacion, int? fracciones, String nombreFormaFarmaceutica, String? nroRegistroSanitario, String? titular, int grupo, String codGrupoFF
});




}
/// @nodoc
class __$ProductCopyWithImpl<$Res>
    implements _$ProductCopyWith<$Res> {
  __$ProductCopyWithImpl(this._self, this._then);

  final _Product _self;
  final $Res Function(_Product) _then;

/// Create a copy of Product
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? codigoProducto = freezed,Object? nombreProducto = null,Object? concent = null,Object? presentacion = freezed,Object? fracciones = freezed,Object? nombreFormaFarmaceutica = null,Object? nroRegistroSanitario = freezed,Object? titular = freezed,Object? grupo = null,Object? codGrupoFF = null,}) {
  return _then(_Product(
codigoProducto: freezed == codigoProducto ? _self.codigoProducto : codigoProducto // ignore: cast_nullable_to_non_nullable
as int?,nombreProducto: null == nombreProducto ? _self.nombreProducto : nombreProducto // ignore: cast_nullable_to_non_nullable
as String,concent: null == concent ? _self.concent : concent // ignore: cast_nullable_to_non_nullable
as String,presentacion: freezed == presentacion ? _self.presentacion : presentacion // ignore: cast_nullable_to_non_nullable
as String?,fracciones: freezed == fracciones ? _self.fracciones : fracciones // ignore: cast_nullable_to_non_nullable
as int?,nombreFormaFarmaceutica: null == nombreFormaFarmaceutica ? _self.nombreFormaFarmaceutica : nombreFormaFarmaceutica // ignore: cast_nullable_to_non_nullable
as String,nroRegistroSanitario: freezed == nroRegistroSanitario ? _self.nroRegistroSanitario : nroRegistroSanitario // ignore: cast_nullable_to_non_nullable
as String?,titular: freezed == titular ? _self.titular : titular // ignore: cast_nullable_to_non_nullable
as String?,grupo: null == grupo ? _self.grupo : grupo // ignore: cast_nullable_to_non_nullable
as int,codGrupoFF: null == codGrupoFF ? _self.codGrupoFF : codGrupoFF // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}


/// @nodoc
mixin _$ProductPrice implements DiagnosticableTreeMixin {

 String get codEstab; int get codProdE; String? get fecha; String? get nombreProducto; double get precio1; double? get precio2; double? get precio3; String? get codGrupoFF; String? get ubicodigo; String? get direccion; String? get telefono; String? get nomGrupoFF; String get setcodigo; String? get nombreComercial; String? get grupo; String? get totalPA; String get concent; String get nombreFormaFarmaceutica; int get fracciones; String? get totalRegistros; String? get nombreLaboratorio; String? get nombreTitular; String? get catCodigo; String get nombreSustancia; String? get fabricante; String? get departamento; String? get provincia; String? get distrito;
/// Create a copy of ProductPrice
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProductPriceCopyWith<ProductPrice> get copyWith => _$ProductPriceCopyWithImpl<ProductPrice>(this as ProductPrice, _$identity);

  /// Serializes this ProductPrice to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ProductPrice'))
    ..add(DiagnosticsProperty('codEstab', codEstab))..add(DiagnosticsProperty('codProdE', codProdE))..add(DiagnosticsProperty('fecha', fecha))..add(DiagnosticsProperty('nombreProducto', nombreProducto))..add(DiagnosticsProperty('precio1', precio1))..add(DiagnosticsProperty('precio2', precio2))..add(DiagnosticsProperty('precio3', precio3))..add(DiagnosticsProperty('codGrupoFF', codGrupoFF))..add(DiagnosticsProperty('ubicodigo', ubicodigo))..add(DiagnosticsProperty('direccion', direccion))..add(DiagnosticsProperty('telefono', telefono))..add(DiagnosticsProperty('nomGrupoFF', nomGrupoFF))..add(DiagnosticsProperty('setcodigo', setcodigo))..add(DiagnosticsProperty('nombreComercial', nombreComercial))..add(DiagnosticsProperty('grupo', grupo))..add(DiagnosticsProperty('totalPA', totalPA))..add(DiagnosticsProperty('concent', concent))..add(DiagnosticsProperty('nombreFormaFarmaceutica', nombreFormaFarmaceutica))..add(DiagnosticsProperty('fracciones', fracciones))..add(DiagnosticsProperty('totalRegistros', totalRegistros))..add(DiagnosticsProperty('nombreLaboratorio', nombreLaboratorio))..add(DiagnosticsProperty('nombreTitular', nombreTitular))..add(DiagnosticsProperty('catCodigo', catCodigo))..add(DiagnosticsProperty('nombreSustancia', nombreSustancia))..add(DiagnosticsProperty('fabricante', fabricante))..add(DiagnosticsProperty('departamento', departamento))..add(DiagnosticsProperty('provincia', provincia))..add(DiagnosticsProperty('distrito', distrito));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ProductPrice&&(identical(other.codEstab, codEstab) || other.codEstab == codEstab)&&(identical(other.codProdE, codProdE) || other.codProdE == codProdE)&&(identical(other.fecha, fecha) || other.fecha == fecha)&&(identical(other.nombreProducto, nombreProducto) || other.nombreProducto == nombreProducto)&&(identical(other.precio1, precio1) || other.precio1 == precio1)&&(identical(other.precio2, precio2) || other.precio2 == precio2)&&(identical(other.precio3, precio3) || other.precio3 == precio3)&&(identical(other.codGrupoFF, codGrupoFF) || other.codGrupoFF == codGrupoFF)&&(identical(other.ubicodigo, ubicodigo) || other.ubicodigo == ubicodigo)&&(identical(other.direccion, direccion) || other.direccion == direccion)&&(identical(other.telefono, telefono) || other.telefono == telefono)&&(identical(other.nomGrupoFF, nomGrupoFF) || other.nomGrupoFF == nomGrupoFF)&&(identical(other.setcodigo, setcodigo) || other.setcodigo == setcodigo)&&(identical(other.nombreComercial, nombreComercial) || other.nombreComercial == nombreComercial)&&(identical(other.grupo, grupo) || other.grupo == grupo)&&(identical(other.totalPA, totalPA) || other.totalPA == totalPA)&&(identical(other.concent, concent) || other.concent == concent)&&(identical(other.nombreFormaFarmaceutica, nombreFormaFarmaceutica) || other.nombreFormaFarmaceutica == nombreFormaFarmaceutica)&&(identical(other.fracciones, fracciones) || other.fracciones == fracciones)&&(identical(other.totalRegistros, totalRegistros) || other.totalRegistros == totalRegistros)&&(identical(other.nombreLaboratorio, nombreLaboratorio) || other.nombreLaboratorio == nombreLaboratorio)&&(identical(other.nombreTitular, nombreTitular) || other.nombreTitular == nombreTitular)&&(identical(other.catCodigo, catCodigo) || other.catCodigo == catCodigo)&&(identical(other.nombreSustancia, nombreSustancia) || other.nombreSustancia == nombreSustancia)&&(identical(other.fabricante, fabricante) || other.fabricante == fabricante)&&(identical(other.departamento, departamento) || other.departamento == departamento)&&(identical(other.provincia, provincia) || other.provincia == provincia)&&(identical(other.distrito, distrito) || other.distrito == distrito));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,codEstab,codProdE,fecha,nombreProducto,precio1,precio2,precio3,codGrupoFF,ubicodigo,direccion,telefono,nomGrupoFF,setcodigo,nombreComercial,grupo,totalPA,concent,nombreFormaFarmaceutica,fracciones,totalRegistros,nombreLaboratorio,nombreTitular,catCodigo,nombreSustancia,fabricante,departamento,provincia,distrito]);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ProductPrice(codEstab: $codEstab, codProdE: $codProdE, fecha: $fecha, nombreProducto: $nombreProducto, precio1: $precio1, precio2: $precio2, precio3: $precio3, codGrupoFF: $codGrupoFF, ubicodigo: $ubicodigo, direccion: $direccion, telefono: $telefono, nomGrupoFF: $nomGrupoFF, setcodigo: $setcodigo, nombreComercial: $nombreComercial, grupo: $grupo, totalPA: $totalPA, concent: $concent, nombreFormaFarmaceutica: $nombreFormaFarmaceutica, fracciones: $fracciones, totalRegistros: $totalRegistros, nombreLaboratorio: $nombreLaboratorio, nombreTitular: $nombreTitular, catCodigo: $catCodigo, nombreSustancia: $nombreSustancia, fabricante: $fabricante, departamento: $departamento, provincia: $provincia, distrito: $distrito)';
}


}

/// @nodoc
abstract mixin class $ProductPriceCopyWith<$Res>  {
  factory $ProductPriceCopyWith(ProductPrice value, $Res Function(ProductPrice) _then) = _$ProductPriceCopyWithImpl;
@useResult
$Res call({
 String codEstab, int codProdE, String? fecha, String? nombreProducto, double precio1, double? precio2, double? precio3, String? codGrupoFF, String? ubicodigo, String? direccion, String? telefono, String? nomGrupoFF, String setcodigo, String? nombreComercial, String? grupo, String? totalPA, String concent, String nombreFormaFarmaceutica, int fracciones, String? totalRegistros, String? nombreLaboratorio, String? nombreTitular, String? catCodigo, String nombreSustancia, String? fabricante, String? departamento, String? provincia, String? distrito
});




}
/// @nodoc
class _$ProductPriceCopyWithImpl<$Res>
    implements $ProductPriceCopyWith<$Res> {
  _$ProductPriceCopyWithImpl(this._self, this._then);

  final ProductPrice _self;
  final $Res Function(ProductPrice) _then;

/// Create a copy of ProductPrice
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? codEstab = null,Object? codProdE = null,Object? fecha = freezed,Object? nombreProducto = freezed,Object? precio1 = null,Object? precio2 = freezed,Object? precio3 = freezed,Object? codGrupoFF = freezed,Object? ubicodigo = freezed,Object? direccion = freezed,Object? telefono = freezed,Object? nomGrupoFF = freezed,Object? setcodigo = null,Object? nombreComercial = freezed,Object? grupo = freezed,Object? totalPA = freezed,Object? concent = null,Object? nombreFormaFarmaceutica = null,Object? fracciones = null,Object? totalRegistros = freezed,Object? nombreLaboratorio = freezed,Object? nombreTitular = freezed,Object? catCodigo = freezed,Object? nombreSustancia = null,Object? fabricante = freezed,Object? departamento = freezed,Object? provincia = freezed,Object? distrito = freezed,}) {
  return _then(_self.copyWith(
codEstab: null == codEstab ? _self.codEstab : codEstab // ignore: cast_nullable_to_non_nullable
as String,codProdE: null == codProdE ? _self.codProdE : codProdE // ignore: cast_nullable_to_non_nullable
as int,fecha: freezed == fecha ? _self.fecha : fecha // ignore: cast_nullable_to_non_nullable
as String?,nombreProducto: freezed == nombreProducto ? _self.nombreProducto : nombreProducto // ignore: cast_nullable_to_non_nullable
as String?,precio1: null == precio1 ? _self.precio1 : precio1 // ignore: cast_nullable_to_non_nullable
as double,precio2: freezed == precio2 ? _self.precio2 : precio2 // ignore: cast_nullable_to_non_nullable
as double?,precio3: freezed == precio3 ? _self.precio3 : precio3 // ignore: cast_nullable_to_non_nullable
as double?,codGrupoFF: freezed == codGrupoFF ? _self.codGrupoFF : codGrupoFF // ignore: cast_nullable_to_non_nullable
as String?,ubicodigo: freezed == ubicodigo ? _self.ubicodigo : ubicodigo // ignore: cast_nullable_to_non_nullable
as String?,direccion: freezed == direccion ? _self.direccion : direccion // ignore: cast_nullable_to_non_nullable
as String?,telefono: freezed == telefono ? _self.telefono : telefono // ignore: cast_nullable_to_non_nullable
as String?,nomGrupoFF: freezed == nomGrupoFF ? _self.nomGrupoFF : nomGrupoFF // ignore: cast_nullable_to_non_nullable
as String?,setcodigo: null == setcodigo ? _self.setcodigo : setcodigo // ignore: cast_nullable_to_non_nullable
as String,nombreComercial: freezed == nombreComercial ? _self.nombreComercial : nombreComercial // ignore: cast_nullable_to_non_nullable
as String?,grupo: freezed == grupo ? _self.grupo : grupo // ignore: cast_nullable_to_non_nullable
as String?,totalPA: freezed == totalPA ? _self.totalPA : totalPA // ignore: cast_nullable_to_non_nullable
as String?,concent: null == concent ? _self.concent : concent // ignore: cast_nullable_to_non_nullable
as String,nombreFormaFarmaceutica: null == nombreFormaFarmaceutica ? _self.nombreFormaFarmaceutica : nombreFormaFarmaceutica // ignore: cast_nullable_to_non_nullable
as String,fracciones: null == fracciones ? _self.fracciones : fracciones // ignore: cast_nullable_to_non_nullable
as int,totalRegistros: freezed == totalRegistros ? _self.totalRegistros : totalRegistros // ignore: cast_nullable_to_non_nullable
as String?,nombreLaboratorio: freezed == nombreLaboratorio ? _self.nombreLaboratorio : nombreLaboratorio // ignore: cast_nullable_to_non_nullable
as String?,nombreTitular: freezed == nombreTitular ? _self.nombreTitular : nombreTitular // ignore: cast_nullable_to_non_nullable
as String?,catCodigo: freezed == catCodigo ? _self.catCodigo : catCodigo // ignore: cast_nullable_to_non_nullable
as String?,nombreSustancia: null == nombreSustancia ? _self.nombreSustancia : nombreSustancia // ignore: cast_nullable_to_non_nullable
as String,fabricante: freezed == fabricante ? _self.fabricante : fabricante // ignore: cast_nullable_to_non_nullable
as String?,departamento: freezed == departamento ? _self.departamento : departamento // ignore: cast_nullable_to_non_nullable
as String?,provincia: freezed == provincia ? _self.provincia : provincia // ignore: cast_nullable_to_non_nullable
as String?,distrito: freezed == distrito ? _self.distrito : distrito // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ProductPrice with DiagnosticableTreeMixin implements ProductPrice {
  const _ProductPrice({required this.codEstab, required this.codProdE, this.fecha, this.nombreProducto, required this.precio1, this.precio2, this.precio3, this.codGrupoFF, this.ubicodigo, this.direccion, this.telefono, this.nomGrupoFF, required this.setcodigo, this.nombreComercial, this.grupo, this.totalPA, required this.concent, required this.nombreFormaFarmaceutica, required this.fracciones, this.totalRegistros, this.nombreLaboratorio, this.nombreTitular, this.catCodigo, required this.nombreSustancia, this.fabricante, this.departamento, this.provincia, this.distrito});
  factory _ProductPrice.fromJson(Map<String, dynamic> json) => _$ProductPriceFromJson(json);

@override final  String codEstab;
@override final  int codProdE;
@override final  String? fecha;
@override final  String? nombreProducto;
@override final  double precio1;
@override final  double? precio2;
@override final  double? precio3;
@override final  String? codGrupoFF;
@override final  String? ubicodigo;
@override final  String? direccion;
@override final  String? telefono;
@override final  String? nomGrupoFF;
@override final  String setcodigo;
@override final  String? nombreComercial;
@override final  String? grupo;
@override final  String? totalPA;
@override final  String concent;
@override final  String nombreFormaFarmaceutica;
@override final  int fracciones;
@override final  String? totalRegistros;
@override final  String? nombreLaboratorio;
@override final  String? nombreTitular;
@override final  String? catCodigo;
@override final  String nombreSustancia;
@override final  String? fabricante;
@override final  String? departamento;
@override final  String? provincia;
@override final  String? distrito;

/// Create a copy of ProductPrice
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProductPriceCopyWith<_ProductPrice> get copyWith => __$ProductPriceCopyWithImpl<_ProductPrice>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProductPriceToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'ProductPrice'))
    ..add(DiagnosticsProperty('codEstab', codEstab))..add(DiagnosticsProperty('codProdE', codProdE))..add(DiagnosticsProperty('fecha', fecha))..add(DiagnosticsProperty('nombreProducto', nombreProducto))..add(DiagnosticsProperty('precio1', precio1))..add(DiagnosticsProperty('precio2', precio2))..add(DiagnosticsProperty('precio3', precio3))..add(DiagnosticsProperty('codGrupoFF', codGrupoFF))..add(DiagnosticsProperty('ubicodigo', ubicodigo))..add(DiagnosticsProperty('direccion', direccion))..add(DiagnosticsProperty('telefono', telefono))..add(DiagnosticsProperty('nomGrupoFF', nomGrupoFF))..add(DiagnosticsProperty('setcodigo', setcodigo))..add(DiagnosticsProperty('nombreComercial', nombreComercial))..add(DiagnosticsProperty('grupo', grupo))..add(DiagnosticsProperty('totalPA', totalPA))..add(DiagnosticsProperty('concent', concent))..add(DiagnosticsProperty('nombreFormaFarmaceutica', nombreFormaFarmaceutica))..add(DiagnosticsProperty('fracciones', fracciones))..add(DiagnosticsProperty('totalRegistros', totalRegistros))..add(DiagnosticsProperty('nombreLaboratorio', nombreLaboratorio))..add(DiagnosticsProperty('nombreTitular', nombreTitular))..add(DiagnosticsProperty('catCodigo', catCodigo))..add(DiagnosticsProperty('nombreSustancia', nombreSustancia))..add(DiagnosticsProperty('fabricante', fabricante))..add(DiagnosticsProperty('departamento', departamento))..add(DiagnosticsProperty('provincia', provincia))..add(DiagnosticsProperty('distrito', distrito));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ProductPrice&&(identical(other.codEstab, codEstab) || other.codEstab == codEstab)&&(identical(other.codProdE, codProdE) || other.codProdE == codProdE)&&(identical(other.fecha, fecha) || other.fecha == fecha)&&(identical(other.nombreProducto, nombreProducto) || other.nombreProducto == nombreProducto)&&(identical(other.precio1, precio1) || other.precio1 == precio1)&&(identical(other.precio2, precio2) || other.precio2 == precio2)&&(identical(other.precio3, precio3) || other.precio3 == precio3)&&(identical(other.codGrupoFF, codGrupoFF) || other.codGrupoFF == codGrupoFF)&&(identical(other.ubicodigo, ubicodigo) || other.ubicodigo == ubicodigo)&&(identical(other.direccion, direccion) || other.direccion == direccion)&&(identical(other.telefono, telefono) || other.telefono == telefono)&&(identical(other.nomGrupoFF, nomGrupoFF) || other.nomGrupoFF == nomGrupoFF)&&(identical(other.setcodigo, setcodigo) || other.setcodigo == setcodigo)&&(identical(other.nombreComercial, nombreComercial) || other.nombreComercial == nombreComercial)&&(identical(other.grupo, grupo) || other.grupo == grupo)&&(identical(other.totalPA, totalPA) || other.totalPA == totalPA)&&(identical(other.concent, concent) || other.concent == concent)&&(identical(other.nombreFormaFarmaceutica, nombreFormaFarmaceutica) || other.nombreFormaFarmaceutica == nombreFormaFarmaceutica)&&(identical(other.fracciones, fracciones) || other.fracciones == fracciones)&&(identical(other.totalRegistros, totalRegistros) || other.totalRegistros == totalRegistros)&&(identical(other.nombreLaboratorio, nombreLaboratorio) || other.nombreLaboratorio == nombreLaboratorio)&&(identical(other.nombreTitular, nombreTitular) || other.nombreTitular == nombreTitular)&&(identical(other.catCodigo, catCodigo) || other.catCodigo == catCodigo)&&(identical(other.nombreSustancia, nombreSustancia) || other.nombreSustancia == nombreSustancia)&&(identical(other.fabricante, fabricante) || other.fabricante == fabricante)&&(identical(other.departamento, departamento) || other.departamento == departamento)&&(identical(other.provincia, provincia) || other.provincia == provincia)&&(identical(other.distrito, distrito) || other.distrito == distrito));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hashAll([runtimeType,codEstab,codProdE,fecha,nombreProducto,precio1,precio2,precio3,codGrupoFF,ubicodigo,direccion,telefono,nomGrupoFF,setcodigo,nombreComercial,grupo,totalPA,concent,nombreFormaFarmaceutica,fracciones,totalRegistros,nombreLaboratorio,nombreTitular,catCodigo,nombreSustancia,fabricante,departamento,provincia,distrito]);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'ProductPrice(codEstab: $codEstab, codProdE: $codProdE, fecha: $fecha, nombreProducto: $nombreProducto, precio1: $precio1, precio2: $precio2, precio3: $precio3, codGrupoFF: $codGrupoFF, ubicodigo: $ubicodigo, direccion: $direccion, telefono: $telefono, nomGrupoFF: $nomGrupoFF, setcodigo: $setcodigo, nombreComercial: $nombreComercial, grupo: $grupo, totalPA: $totalPA, concent: $concent, nombreFormaFarmaceutica: $nombreFormaFarmaceutica, fracciones: $fracciones, totalRegistros: $totalRegistros, nombreLaboratorio: $nombreLaboratorio, nombreTitular: $nombreTitular, catCodigo: $catCodigo, nombreSustancia: $nombreSustancia, fabricante: $fabricante, departamento: $departamento, provincia: $provincia, distrito: $distrito)';
}


}

/// @nodoc
abstract mixin class _$ProductPriceCopyWith<$Res> implements $ProductPriceCopyWith<$Res> {
  factory _$ProductPriceCopyWith(_ProductPrice value, $Res Function(_ProductPrice) _then) = __$ProductPriceCopyWithImpl;
@override @useResult
$Res call({
 String codEstab, int codProdE, String? fecha, String? nombreProducto, double precio1, double? precio2, double? precio3, String? codGrupoFF, String? ubicodigo, String? direccion, String? telefono, String? nomGrupoFF, String setcodigo, String? nombreComercial, String? grupo, String? totalPA, String concent, String nombreFormaFarmaceutica, int fracciones, String? totalRegistros, String? nombreLaboratorio, String? nombreTitular, String? catCodigo, String nombreSustancia, String? fabricante, String? departamento, String? provincia, String? distrito
});




}
/// @nodoc
class __$ProductPriceCopyWithImpl<$Res>
    implements _$ProductPriceCopyWith<$Res> {
  __$ProductPriceCopyWithImpl(this._self, this._then);

  final _ProductPrice _self;
  final $Res Function(_ProductPrice) _then;

/// Create a copy of ProductPrice
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? codEstab = null,Object? codProdE = null,Object? fecha = freezed,Object? nombreProducto = freezed,Object? precio1 = null,Object? precio2 = freezed,Object? precio3 = freezed,Object? codGrupoFF = freezed,Object? ubicodigo = freezed,Object? direccion = freezed,Object? telefono = freezed,Object? nomGrupoFF = freezed,Object? setcodigo = null,Object? nombreComercial = freezed,Object? grupo = freezed,Object? totalPA = freezed,Object? concent = null,Object? nombreFormaFarmaceutica = null,Object? fracciones = null,Object? totalRegistros = freezed,Object? nombreLaboratorio = freezed,Object? nombreTitular = freezed,Object? catCodigo = freezed,Object? nombreSustancia = null,Object? fabricante = freezed,Object? departamento = freezed,Object? provincia = freezed,Object? distrito = freezed,}) {
  return _then(_ProductPrice(
codEstab: null == codEstab ? _self.codEstab : codEstab // ignore: cast_nullable_to_non_nullable
as String,codProdE: null == codProdE ? _self.codProdE : codProdE // ignore: cast_nullable_to_non_nullable
as int,fecha: freezed == fecha ? _self.fecha : fecha // ignore: cast_nullable_to_non_nullable
as String?,nombreProducto: freezed == nombreProducto ? _self.nombreProducto : nombreProducto // ignore: cast_nullable_to_non_nullable
as String?,precio1: null == precio1 ? _self.precio1 : precio1 // ignore: cast_nullable_to_non_nullable
as double,precio2: freezed == precio2 ? _self.precio2 : precio2 // ignore: cast_nullable_to_non_nullable
as double?,precio3: freezed == precio3 ? _self.precio3 : precio3 // ignore: cast_nullable_to_non_nullable
as double?,codGrupoFF: freezed == codGrupoFF ? _self.codGrupoFF : codGrupoFF // ignore: cast_nullable_to_non_nullable
as String?,ubicodigo: freezed == ubicodigo ? _self.ubicodigo : ubicodigo // ignore: cast_nullable_to_non_nullable
as String?,direccion: freezed == direccion ? _self.direccion : direccion // ignore: cast_nullable_to_non_nullable
as String?,telefono: freezed == telefono ? _self.telefono : telefono // ignore: cast_nullable_to_non_nullable
as String?,nomGrupoFF: freezed == nomGrupoFF ? _self.nomGrupoFF : nomGrupoFF // ignore: cast_nullable_to_non_nullable
as String?,setcodigo: null == setcodigo ? _self.setcodigo : setcodigo // ignore: cast_nullable_to_non_nullable
as String,nombreComercial: freezed == nombreComercial ? _self.nombreComercial : nombreComercial // ignore: cast_nullable_to_non_nullable
as String?,grupo: freezed == grupo ? _self.grupo : grupo // ignore: cast_nullable_to_non_nullable
as String?,totalPA: freezed == totalPA ? _self.totalPA : totalPA // ignore: cast_nullable_to_non_nullable
as String?,concent: null == concent ? _self.concent : concent // ignore: cast_nullable_to_non_nullable
as String,nombreFormaFarmaceutica: null == nombreFormaFarmaceutica ? _self.nombreFormaFarmaceutica : nombreFormaFarmaceutica // ignore: cast_nullable_to_non_nullable
as String,fracciones: null == fracciones ? _self.fracciones : fracciones // ignore: cast_nullable_to_non_nullable
as int,totalRegistros: freezed == totalRegistros ? _self.totalRegistros : totalRegistros // ignore: cast_nullable_to_non_nullable
as String?,nombreLaboratorio: freezed == nombreLaboratorio ? _self.nombreLaboratorio : nombreLaboratorio // ignore: cast_nullable_to_non_nullable
as String?,nombreTitular: freezed == nombreTitular ? _self.nombreTitular : nombreTitular // ignore: cast_nullable_to_non_nullable
as String?,catCodigo: freezed == catCodigo ? _self.catCodigo : catCodigo // ignore: cast_nullable_to_non_nullable
as String?,nombreSustancia: null == nombreSustancia ? _self.nombreSustancia : nombreSustancia // ignore: cast_nullable_to_non_nullable
as String,fabricante: freezed == fabricante ? _self.fabricante : fabricante // ignore: cast_nullable_to_non_nullable
as String?,departamento: freezed == departamento ? _self.departamento : departamento // ignore: cast_nullable_to_non_nullable
as String?,provincia: freezed == provincia ? _self.provincia : provincia // ignore: cast_nullable_to_non_nullable
as String?,distrito: freezed == distrito ? _self.distrito : distrito // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on

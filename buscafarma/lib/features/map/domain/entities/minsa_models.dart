import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'minsa_models.freezed.dart';
part 'minsa_models.g.dart';

// Modelo base para las respuestas de la API de MINSA
@Freezed(genericArgumentFactories: true)
abstract class MinsaResponse<T> with _$MinsaResponse<T> {
  const factory MinsaResponse({required String codigo, required String mensaje, required List<T> data, T? entidad, int? cantidad}) = _MinsaResponse<T>;

  factory MinsaResponse.fromJson(Map<String, dynamic> json, T Function(Object?) fromJsonT) {
    final List<dynamic> dataJson = json['data'] ?? [];
    final dynamic entidadJson = json['entidad'];

    return MinsaResponse(codigo: json['codigo'] ?? '', mensaje: json['mensaje'] ?? '', data: dataJson.map((item) => fromJsonT(item)).toList().cast<T>(), entidad: entidadJson != null ? fromJsonT(entidadJson) : null, cantidad: json['cantidad']);
  }
}

// Modelo para los detalles de un producto
@freezed
abstract class ProductDetail with _$ProductDetail {
  const factory ProductDetail({
    required double precio1,
    double? precio2,
    required String nombreProducto,
    required String paisFabricacion,
    required String registroSanitario,
    required String condicionVenta,
    required String tipoProducto,
    required String nombreTitular,
    required String nombreFabricante,
    required String presentacion,
    required String laboratorio,
    required String directorTecnico,
    required String nombreComercial,
    required String telefono,
    required String direccion,
    required String departamento,
    required String provincia,
    required String distrito,
    required String horarioAtencion,
    required String ubigeo,
    required String catCodigo,
    required String email,
    required String ruc,
  }) = _ProductDetail;

  factory ProductDetail.fromJson(Map<String, dynamic> json) => _$ProductDetailFromJson(json);
}

// Modelo para los productos
@freezed
abstract class Product with _$Product {
  const factory Product({
    int? codigoProducto,
    required String nombreProducto,
    required String concent,
    String? presentacion,
    int? fracciones,
    required String nombreFormaFarmaceutica,
    String? nroRegistroSanitario,
    String? titular,
    required int grupo,
    required String codGrupoFF,
  }) = _Product;

  factory Product.fromJson(Map<String, dynamic> json) => _$ProductFromJson(json);
}

// Modelo para los precios de productos
@freezed
abstract class ProductPrice with _$ProductPrice {
  const factory ProductPrice({
    required String codEstab,
    required int codProdE,
    String? fecha,
    String? nombreProducto,
    required double precio1,
    double? precio2,
    double? precio3,
    String? codGrupoFF,
    String? ubicodigo,
    String? direccion,
    String? telefono,
    String? nomGrupoFF,
    required String setcodigo,
    String? nombreComercial,
    String? grupo,
    String? totalPA,
    required String concent,
    required String nombreFormaFarmaceutica,
    required int fracciones,
    String? totalRegistros,
    String? nombreLaboratorio,
    String? nombreTitular,
    String? catCodigo,
    required String nombreSustancia,
    String? fabricante,
    String? departamento,
    String? provincia,
    String? distrito,
  }) = _ProductPrice;

  factory ProductPrice.fromJson(Map<String, dynamic> json) => _$ProductPriceFromJson(json);
}

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'minsa_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ProductDetail _$ProductDetailFromJson(Map<String, dynamic> json) =>
    _ProductDetail(
      precio1: (json['precio1'] as num).toDouble(),
      precio2: (json['precio2'] as num?)?.toDouble(),
      nombreProducto: json['nombreProducto'] as String,
      paisFabricacion: json['paisFabricacion'] as String,
      registroSanitario: json['registroSanitario'] as String,
      condicionVenta: json['condicionVenta'] as String,
      tipoProducto: json['tipoProducto'] as String,
      nombreTitular: json['nombreTitular'] as String,
      nombreFabricante: json['nombreFabricante'] as String,
      presentacion: json['presentacion'] as String,
      laboratorio: json['laboratorio'] as String,
      directorTecnico: json['directorTecnico'] as String,
      nombreComercial: json['nombreComercial'] as String,
      telefono: json['telefono'] as String,
      direccion: json['direccion'] as String,
      departamento: json['departamento'] as String,
      provincia: json['provincia'] as String,
      distrito: json['distrito'] as String,
      horarioAtencion: json['horarioAtencion'] as String,
      ubigeo: json['ubigeo'] as String,
      catCodigo: json['catCodigo'] as String,
      email: json['email'] as String,
      ruc: json['ruc'] as String,
    );

Map<String, dynamic> _$ProductDetailToJson(_ProductDetail instance) =>
    <String, dynamic>{
      'precio1': instance.precio1,
      'precio2': instance.precio2,
      'nombreProducto': instance.nombreProducto,
      'paisFabricacion': instance.paisFabricacion,
      'registroSanitario': instance.registroSanitario,
      'condicionVenta': instance.condicionVenta,
      'tipoProducto': instance.tipoProducto,
      'nombreTitular': instance.nombreTitular,
      'nombreFabricante': instance.nombreFabricante,
      'presentacion': instance.presentacion,
      'laboratorio': instance.laboratorio,
      'directorTecnico': instance.directorTecnico,
      'nombreComercial': instance.nombreComercial,
      'telefono': instance.telefono,
      'direccion': instance.direccion,
      'departamento': instance.departamento,
      'provincia': instance.provincia,
      'distrito': instance.distrito,
      'horarioAtencion': instance.horarioAtencion,
      'ubigeo': instance.ubigeo,
      'catCodigo': instance.catCodigo,
      'email': instance.email,
      'ruc': instance.ruc,
    };

_Product _$ProductFromJson(Map<String, dynamic> json) => _Product(
  codigoProducto: (json['codigoProducto'] as num?)?.toInt(),
  nombreProducto: json['nombreProducto'] as String,
  concent: json['concent'] as String,
  presentacion: json['presentacion'] as String?,
  fracciones: (json['fracciones'] as num?)?.toInt(),
  nombreFormaFarmaceutica: json['nombreFormaFarmaceutica'] as String,
  nroRegistroSanitario: json['nroRegistroSanitario'] as String?,
  titular: json['titular'] as String?,
  grupo: (json['grupo'] as num).toInt(),
  codGrupoFF: json['codGrupoFF'] as String,
);

Map<String, dynamic> _$ProductToJson(_Product instance) => <String, dynamic>{
  'codigoProducto': instance.codigoProducto,
  'nombreProducto': instance.nombreProducto,
  'concent': instance.concent,
  'presentacion': instance.presentacion,
  'fracciones': instance.fracciones,
  'nombreFormaFarmaceutica': instance.nombreFormaFarmaceutica,
  'nroRegistroSanitario': instance.nroRegistroSanitario,
  'titular': instance.titular,
  'grupo': instance.grupo,
  'codGrupoFF': instance.codGrupoFF,
};

_ProductPrice _$ProductPriceFromJson(Map<String, dynamic> json) =>
    _ProductPrice(
      codEstab: json['codEstab'] as String,
      codProdE: (json['codProdE'] as num).toInt(),
      fecha: json['fecha'] as String?,
      nombreProducto: json['nombreProducto'] as String?,
      precio1: (json['precio1'] as num).toDouble(),
      precio2: (json['precio2'] as num?)?.toDouble(),
      precio3: (json['precio3'] as num?)?.toDouble(),
      codGrupoFF: json['codGrupoFF'] as String?,
      ubicodigo: json['ubicodigo'] as String?,
      direccion: json['direccion'] as String?,
      telefono: json['telefono'] as String?,
      nomGrupoFF: json['nomGrupoFF'] as String?,
      setcodigo: json['setcodigo'] as String,
      nombreComercial: json['nombreComercial'] as String?,
      grupo: json['grupo'] as String?,
      totalPA: json['totalPA'] as String?,
      concent: json['concent'] as String,
      nombreFormaFarmaceutica: json['nombreFormaFarmaceutica'] as String,
      fracciones: (json['fracciones'] as num).toInt(),
      totalRegistros: json['totalRegistros'] as String?,
      nombreLaboratorio: json['nombreLaboratorio'] as String?,
      nombreTitular: json['nombreTitular'] as String?,
      catCodigo: json['catCodigo'] as String?,
      nombreSustancia: json['nombreSustancia'] as String,
      fabricante: json['fabricante'] as String?,
      departamento: json['departamento'] as String?,
      provincia: json['provincia'] as String?,
      distrito: json['distrito'] as String?,
    );

Map<String, dynamic> _$ProductPriceToJson(_ProductPrice instance) =>
    <String, dynamic>{
      'codEstab': instance.codEstab,
      'codProdE': instance.codProdE,
      'fecha': instance.fecha,
      'nombreProducto': instance.nombreProducto,
      'precio1': instance.precio1,
      'precio2': instance.precio2,
      'precio3': instance.precio3,
      'codGrupoFF': instance.codGrupoFF,
      'ubicodigo': instance.ubicodigo,
      'direccion': instance.direccion,
      'telefono': instance.telefono,
      'nomGrupoFF': instance.nomGrupoFF,
      'setcodigo': instance.setcodigo,
      'nombreComercial': instance.nombreComercial,
      'grupo': instance.grupo,
      'totalPA': instance.totalPA,
      'concent': instance.concent,
      'nombreFormaFarmaceutica': instance.nombreFormaFarmaceutica,
      'fracciones': instance.fracciones,
      'totalRegistros': instance.totalRegistros,
      'nombreLaboratorio': instance.nombreLaboratorio,
      'nombreTitular': instance.nombreTitular,
      'catCodigo': instance.catCodigo,
      'nombreSustancia': instance.nombreSustancia,
      'fabricante': instance.fabricante,
      'departamento': instance.departamento,
      'provincia': instance.provincia,
      'distrito': instance.distrito,
    };

// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'ubigeo_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$UbigeoResponse implements DiagnosticableTreeMixin {

 String get codigo; String get mensaje; List<Department> get data; dynamic get entidad; int get cantidad; dynamic get codigoEntidad; dynamic get codigosValidos; dynamic get paginas; dynamic get totalData; dynamic get resultadoProceso; dynamic get mensajesErrores;
/// Create a copy of UbigeoResponse
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UbigeoResponseCopyWith<UbigeoResponse> get copyWith => _$UbigeoResponseCopyWithImpl<UbigeoResponse>(this as UbigeoResponse, _$identity);

  /// Serializes this UbigeoResponse to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'UbigeoResponse'))
    ..add(DiagnosticsProperty('codigo', codigo))..add(DiagnosticsProperty('mensaje', mensaje))..add(DiagnosticsProperty('data', data))..add(DiagnosticsProperty('entidad', entidad))..add(DiagnosticsProperty('cantidad', cantidad))..add(DiagnosticsProperty('codigoEntidad', codigoEntidad))..add(DiagnosticsProperty('codigosValidos', codigosValidos))..add(DiagnosticsProperty('paginas', paginas))..add(DiagnosticsProperty('totalData', totalData))..add(DiagnosticsProperty('resultadoProceso', resultadoProceso))..add(DiagnosticsProperty('mensajesErrores', mensajesErrores));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UbigeoResponse&&(identical(other.codigo, codigo) || other.codigo == codigo)&&(identical(other.mensaje, mensaje) || other.mensaje == mensaje)&&const DeepCollectionEquality().equals(other.data, data)&&const DeepCollectionEquality().equals(other.entidad, entidad)&&(identical(other.cantidad, cantidad) || other.cantidad == cantidad)&&const DeepCollectionEquality().equals(other.codigoEntidad, codigoEntidad)&&const DeepCollectionEquality().equals(other.codigosValidos, codigosValidos)&&const DeepCollectionEquality().equals(other.paginas, paginas)&&const DeepCollectionEquality().equals(other.totalData, totalData)&&const DeepCollectionEquality().equals(other.resultadoProceso, resultadoProceso)&&const DeepCollectionEquality().equals(other.mensajesErrores, mensajesErrores));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codigo,mensaje,const DeepCollectionEquality().hash(data),const DeepCollectionEquality().hash(entidad),cantidad,const DeepCollectionEquality().hash(codigoEntidad),const DeepCollectionEquality().hash(codigosValidos),const DeepCollectionEquality().hash(paginas),const DeepCollectionEquality().hash(totalData),const DeepCollectionEquality().hash(resultadoProceso),const DeepCollectionEquality().hash(mensajesErrores));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'UbigeoResponse(codigo: $codigo, mensaje: $mensaje, data: $data, entidad: $entidad, cantidad: $cantidad, codigoEntidad: $codigoEntidad, codigosValidos: $codigosValidos, paginas: $paginas, totalData: $totalData, resultadoProceso: $resultadoProceso, mensajesErrores: $mensajesErrores)';
}


}

/// @nodoc
abstract mixin class $UbigeoResponseCopyWith<$Res>  {
  factory $UbigeoResponseCopyWith(UbigeoResponse value, $Res Function(UbigeoResponse) _then) = _$UbigeoResponseCopyWithImpl;
@useResult
$Res call({
 String codigo, String mensaje, List<Department> data, dynamic entidad, int cantidad, dynamic codigoEntidad, dynamic codigosValidos, dynamic paginas, dynamic totalData, dynamic resultadoProceso, dynamic mensajesErrores
});




}
/// @nodoc
class _$UbigeoResponseCopyWithImpl<$Res>
    implements $UbigeoResponseCopyWith<$Res> {
  _$UbigeoResponseCopyWithImpl(this._self, this._then);

  final UbigeoResponse _self;
  final $Res Function(UbigeoResponse) _then;

/// Create a copy of UbigeoResponse
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? codigo = null,Object? mensaje = null,Object? data = null,Object? entidad = freezed,Object? cantidad = null,Object? codigoEntidad = freezed,Object? codigosValidos = freezed,Object? paginas = freezed,Object? totalData = freezed,Object? resultadoProceso = freezed,Object? mensajesErrores = freezed,}) {
  return _then(_self.copyWith(
codigo: null == codigo ? _self.codigo : codigo // ignore: cast_nullable_to_non_nullable
as String,mensaje: null == mensaje ? _self.mensaje : mensaje // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self.data : data // ignore: cast_nullable_to_non_nullable
as List<Department>,entidad: freezed == entidad ? _self.entidad : entidad // ignore: cast_nullable_to_non_nullable
as dynamic,cantidad: null == cantidad ? _self.cantidad : cantidad // ignore: cast_nullable_to_non_nullable
as int,codigoEntidad: freezed == codigoEntidad ? _self.codigoEntidad : codigoEntidad // ignore: cast_nullable_to_non_nullable
as dynamic,codigosValidos: freezed == codigosValidos ? _self.codigosValidos : codigosValidos // ignore: cast_nullable_to_non_nullable
as dynamic,paginas: freezed == paginas ? _self.paginas : paginas // ignore: cast_nullable_to_non_nullable
as dynamic,totalData: freezed == totalData ? _self.totalData : totalData // ignore: cast_nullable_to_non_nullable
as dynamic,resultadoProceso: freezed == resultadoProceso ? _self.resultadoProceso : resultadoProceso // ignore: cast_nullable_to_non_nullable
as dynamic,mensajesErrores: freezed == mensajesErrores ? _self.mensajesErrores : mensajesErrores // ignore: cast_nullable_to_non_nullable
as dynamic,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _UbigeoResponse with DiagnosticableTreeMixin implements UbigeoResponse {
  const _UbigeoResponse({required this.codigo, required this.mensaje, required final  List<Department> data, this.entidad, required this.cantidad, this.codigoEntidad, this.codigosValidos, this.paginas, this.totalData, this.resultadoProceso, this.mensajesErrores}): _data = data;
  factory _UbigeoResponse.fromJson(Map<String, dynamic> json) => _$UbigeoResponseFromJson(json);

@override final  String codigo;
@override final  String mensaje;
 final  List<Department> _data;
@override List<Department> get data {
  if (_data is EqualUnmodifiableListView) return _data;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_data);
}

@override final  dynamic entidad;
@override final  int cantidad;
@override final  dynamic codigoEntidad;
@override final  dynamic codigosValidos;
@override final  dynamic paginas;
@override final  dynamic totalData;
@override final  dynamic resultadoProceso;
@override final  dynamic mensajesErrores;

/// Create a copy of UbigeoResponse
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UbigeoResponseCopyWith<_UbigeoResponse> get copyWith => __$UbigeoResponseCopyWithImpl<_UbigeoResponse>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$UbigeoResponseToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'UbigeoResponse'))
    ..add(DiagnosticsProperty('codigo', codigo))..add(DiagnosticsProperty('mensaje', mensaje))..add(DiagnosticsProperty('data', data))..add(DiagnosticsProperty('entidad', entidad))..add(DiagnosticsProperty('cantidad', cantidad))..add(DiagnosticsProperty('codigoEntidad', codigoEntidad))..add(DiagnosticsProperty('codigosValidos', codigosValidos))..add(DiagnosticsProperty('paginas', paginas))..add(DiagnosticsProperty('totalData', totalData))..add(DiagnosticsProperty('resultadoProceso', resultadoProceso))..add(DiagnosticsProperty('mensajesErrores', mensajesErrores));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UbigeoResponse&&(identical(other.codigo, codigo) || other.codigo == codigo)&&(identical(other.mensaje, mensaje) || other.mensaje == mensaje)&&const DeepCollectionEquality().equals(other._data, _data)&&const DeepCollectionEquality().equals(other.entidad, entidad)&&(identical(other.cantidad, cantidad) || other.cantidad == cantidad)&&const DeepCollectionEquality().equals(other.codigoEntidad, codigoEntidad)&&const DeepCollectionEquality().equals(other.codigosValidos, codigosValidos)&&const DeepCollectionEquality().equals(other.paginas, paginas)&&const DeepCollectionEquality().equals(other.totalData, totalData)&&const DeepCollectionEquality().equals(other.resultadoProceso, resultadoProceso)&&const DeepCollectionEquality().equals(other.mensajesErrores, mensajesErrores));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codigo,mensaje,const DeepCollectionEquality().hash(_data),const DeepCollectionEquality().hash(entidad),cantidad,const DeepCollectionEquality().hash(codigoEntidad),const DeepCollectionEquality().hash(codigosValidos),const DeepCollectionEquality().hash(paginas),const DeepCollectionEquality().hash(totalData),const DeepCollectionEquality().hash(resultadoProceso),const DeepCollectionEquality().hash(mensajesErrores));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'UbigeoResponse(codigo: $codigo, mensaje: $mensaje, data: $data, entidad: $entidad, cantidad: $cantidad, codigoEntidad: $codigoEntidad, codigosValidos: $codigosValidos, paginas: $paginas, totalData: $totalData, resultadoProceso: $resultadoProceso, mensajesErrores: $mensajesErrores)';
}


}

/// @nodoc
abstract mixin class _$UbigeoResponseCopyWith<$Res> implements $UbigeoResponseCopyWith<$Res> {
  factory _$UbigeoResponseCopyWith(_UbigeoResponse value, $Res Function(_UbigeoResponse) _then) = __$UbigeoResponseCopyWithImpl;
@override @useResult
$Res call({
 String codigo, String mensaje, List<Department> data, dynamic entidad, int cantidad, dynamic codigoEntidad, dynamic codigosValidos, dynamic paginas, dynamic totalData, dynamic resultadoProceso, dynamic mensajesErrores
});




}
/// @nodoc
class __$UbigeoResponseCopyWithImpl<$Res>
    implements _$UbigeoResponseCopyWith<$Res> {
  __$UbigeoResponseCopyWithImpl(this._self, this._then);

  final _UbigeoResponse _self;
  final $Res Function(_UbigeoResponse) _then;

/// Create a copy of UbigeoResponse
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? codigo = null,Object? mensaje = null,Object? data = null,Object? entidad = freezed,Object? cantidad = null,Object? codigoEntidad = freezed,Object? codigosValidos = freezed,Object? paginas = freezed,Object? totalData = freezed,Object? resultadoProceso = freezed,Object? mensajesErrores = freezed,}) {
  return _then(_UbigeoResponse(
codigo: null == codigo ? _self.codigo : codigo // ignore: cast_nullable_to_non_nullable
as String,mensaje: null == mensaje ? _self.mensaje : mensaje // ignore: cast_nullable_to_non_nullable
as String,data: null == data ? _self._data : data // ignore: cast_nullable_to_non_nullable
as List<Department>,entidad: freezed == entidad ? _self.entidad : entidad // ignore: cast_nullable_to_non_nullable
as dynamic,cantidad: null == cantidad ? _self.cantidad : cantidad // ignore: cast_nullable_to_non_nullable
as int,codigoEntidad: freezed == codigoEntidad ? _self.codigoEntidad : codigoEntidad // ignore: cast_nullable_to_non_nullable
as dynamic,codigosValidos: freezed == codigosValidos ? _self.codigosValidos : codigosValidos // ignore: cast_nullable_to_non_nullable
as dynamic,paginas: freezed == paginas ? _self.paginas : paginas // ignore: cast_nullable_to_non_nullable
as dynamic,totalData: freezed == totalData ? _self.totalData : totalData // ignore: cast_nullable_to_non_nullable
as dynamic,resultadoProceso: freezed == resultadoProceso ? _self.resultadoProceso : resultadoProceso // ignore: cast_nullable_to_non_nullable
as dynamic,mensajesErrores: freezed == mensajesErrores ? _self.mensajesErrores : mensajesErrores // ignore: cast_nullable_to_non_nullable
as dynamic,
  ));
}


}


/// @nodoc
mixin _$Department implements DiagnosticableTreeMixin {

 String get codigo; String get descripcion; List<Province> get provincias;
/// Create a copy of Department
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DepartmentCopyWith<Department> get copyWith => _$DepartmentCopyWithImpl<Department>(this as Department, _$identity);

  /// Serializes this Department to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Department'))
    ..add(DiagnosticsProperty('codigo', codigo))..add(DiagnosticsProperty('descripcion', descripcion))..add(DiagnosticsProperty('provincias', provincias));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Department&&(identical(other.codigo, codigo) || other.codigo == codigo)&&(identical(other.descripcion, descripcion) || other.descripcion == descripcion)&&const DeepCollectionEquality().equals(other.provincias, provincias));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codigo,descripcion,const DeepCollectionEquality().hash(provincias));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Department(codigo: $codigo, descripcion: $descripcion, provincias: $provincias)';
}


}

/// @nodoc
abstract mixin class $DepartmentCopyWith<$Res>  {
  factory $DepartmentCopyWith(Department value, $Res Function(Department) _then) = _$DepartmentCopyWithImpl;
@useResult
$Res call({
 String codigo, String descripcion, List<Province> provincias
});




}
/// @nodoc
class _$DepartmentCopyWithImpl<$Res>
    implements $DepartmentCopyWith<$Res> {
  _$DepartmentCopyWithImpl(this._self, this._then);

  final Department _self;
  final $Res Function(Department) _then;

/// Create a copy of Department
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? codigo = null,Object? descripcion = null,Object? provincias = null,}) {
  return _then(_self.copyWith(
codigo: null == codigo ? _self.codigo : codigo // ignore: cast_nullable_to_non_nullable
as String,descripcion: null == descripcion ? _self.descripcion : descripcion // ignore: cast_nullable_to_non_nullable
as String,provincias: null == provincias ? _self.provincias : provincias // ignore: cast_nullable_to_non_nullable
as List<Province>,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Department with DiagnosticableTreeMixin implements Department {
  const _Department({required this.codigo, required this.descripcion, required final  List<Province> provincias}): _provincias = provincias;
  factory _Department.fromJson(Map<String, dynamic> json) => _$DepartmentFromJson(json);

@override final  String codigo;
@override final  String descripcion;
 final  List<Province> _provincias;
@override List<Province> get provincias {
  if (_provincias is EqualUnmodifiableListView) return _provincias;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_provincias);
}


/// Create a copy of Department
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DepartmentCopyWith<_Department> get copyWith => __$DepartmentCopyWithImpl<_Department>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DepartmentToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Department'))
    ..add(DiagnosticsProperty('codigo', codigo))..add(DiagnosticsProperty('descripcion', descripcion))..add(DiagnosticsProperty('provincias', provincias));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Department&&(identical(other.codigo, codigo) || other.codigo == codigo)&&(identical(other.descripcion, descripcion) || other.descripcion == descripcion)&&const DeepCollectionEquality().equals(other._provincias, _provincias));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codigo,descripcion,const DeepCollectionEquality().hash(_provincias));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Department(codigo: $codigo, descripcion: $descripcion, provincias: $provincias)';
}


}

/// @nodoc
abstract mixin class _$DepartmentCopyWith<$Res> implements $DepartmentCopyWith<$Res> {
  factory _$DepartmentCopyWith(_Department value, $Res Function(_Department) _then) = __$DepartmentCopyWithImpl;
@override @useResult
$Res call({
 String codigo, String descripcion, List<Province> provincias
});




}
/// @nodoc
class __$DepartmentCopyWithImpl<$Res>
    implements _$DepartmentCopyWith<$Res> {
  __$DepartmentCopyWithImpl(this._self, this._then);

  final _Department _self;
  final $Res Function(_Department) _then;

/// Create a copy of Department
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? codigo = null,Object? descripcion = null,Object? provincias = null,}) {
  return _then(_Department(
codigo: null == codigo ? _self.codigo : codigo // ignore: cast_nullable_to_non_nullable
as String,descripcion: null == descripcion ? _self.descripcion : descripcion // ignore: cast_nullable_to_non_nullable
as String,provincias: null == provincias ? _self._provincias : provincias // ignore: cast_nullable_to_non_nullable
as List<Province>,
  ));
}


}


/// @nodoc
mixin _$Province implements DiagnosticableTreeMixin {

 String get codigo; String get descripcion; String get codigoDepartamento; List<District>? get distritos; String? get codigoProvincia;
/// Create a copy of Province
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ProvinceCopyWith<Province> get copyWith => _$ProvinceCopyWithImpl<Province>(this as Province, _$identity);

  /// Serializes this Province to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Province'))
    ..add(DiagnosticsProperty('codigo', codigo))..add(DiagnosticsProperty('descripcion', descripcion))..add(DiagnosticsProperty('codigoDepartamento', codigoDepartamento))..add(DiagnosticsProperty('distritos', distritos))..add(DiagnosticsProperty('codigoProvincia', codigoProvincia));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Province&&(identical(other.codigo, codigo) || other.codigo == codigo)&&(identical(other.descripcion, descripcion) || other.descripcion == descripcion)&&(identical(other.codigoDepartamento, codigoDepartamento) || other.codigoDepartamento == codigoDepartamento)&&const DeepCollectionEquality().equals(other.distritos, distritos)&&(identical(other.codigoProvincia, codigoProvincia) || other.codigoProvincia == codigoProvincia));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codigo,descripcion,codigoDepartamento,const DeepCollectionEquality().hash(distritos),codigoProvincia);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Province(codigo: $codigo, descripcion: $descripcion, codigoDepartamento: $codigoDepartamento, distritos: $distritos, codigoProvincia: $codigoProvincia)';
}


}

/// @nodoc
abstract mixin class $ProvinceCopyWith<$Res>  {
  factory $ProvinceCopyWith(Province value, $Res Function(Province) _then) = _$ProvinceCopyWithImpl;
@useResult
$Res call({
 String codigo, String descripcion, String codigoDepartamento, List<District>? distritos, String? codigoProvincia
});




}
/// @nodoc
class _$ProvinceCopyWithImpl<$Res>
    implements $ProvinceCopyWith<$Res> {
  _$ProvinceCopyWithImpl(this._self, this._then);

  final Province _self;
  final $Res Function(Province) _then;

/// Create a copy of Province
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? codigo = null,Object? descripcion = null,Object? codigoDepartamento = null,Object? distritos = freezed,Object? codigoProvincia = freezed,}) {
  return _then(_self.copyWith(
codigo: null == codigo ? _self.codigo : codigo // ignore: cast_nullable_to_non_nullable
as String,descripcion: null == descripcion ? _self.descripcion : descripcion // ignore: cast_nullable_to_non_nullable
as String,codigoDepartamento: null == codigoDepartamento ? _self.codigoDepartamento : codigoDepartamento // ignore: cast_nullable_to_non_nullable
as String,distritos: freezed == distritos ? _self.distritos : distritos // ignore: cast_nullable_to_non_nullable
as List<District>?,codigoProvincia: freezed == codigoProvincia ? _self.codigoProvincia : codigoProvincia // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _Province with DiagnosticableTreeMixin implements Province {
  const _Province({required this.codigo, required this.descripcion, required this.codigoDepartamento, final  List<District>? distritos, this.codigoProvincia}): _distritos = distritos;
  factory _Province.fromJson(Map<String, dynamic> json) => _$ProvinceFromJson(json);

@override final  String codigo;
@override final  String descripcion;
@override final  String codigoDepartamento;
 final  List<District>? _distritos;
@override List<District>? get distritos {
  final value = _distritos;
  if (value == null) return null;
  if (_distritos is EqualUnmodifiableListView) return _distritos;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(value);
}

@override final  String? codigoProvincia;

/// Create a copy of Province
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ProvinceCopyWith<_Province> get copyWith => __$ProvinceCopyWithImpl<_Province>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ProvinceToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'Province'))
    ..add(DiagnosticsProperty('codigo', codigo))..add(DiagnosticsProperty('descripcion', descripcion))..add(DiagnosticsProperty('codigoDepartamento', codigoDepartamento))..add(DiagnosticsProperty('distritos', distritos))..add(DiagnosticsProperty('codigoProvincia', codigoProvincia));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _Province&&(identical(other.codigo, codigo) || other.codigo == codigo)&&(identical(other.descripcion, descripcion) || other.descripcion == descripcion)&&(identical(other.codigoDepartamento, codigoDepartamento) || other.codigoDepartamento == codigoDepartamento)&&const DeepCollectionEquality().equals(other._distritos, _distritos)&&(identical(other.codigoProvincia, codigoProvincia) || other.codigoProvincia == codigoProvincia));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codigo,descripcion,codigoDepartamento,const DeepCollectionEquality().hash(_distritos),codigoProvincia);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'Province(codigo: $codigo, descripcion: $descripcion, codigoDepartamento: $codigoDepartamento, distritos: $distritos, codigoProvincia: $codigoProvincia)';
}


}

/// @nodoc
abstract mixin class _$ProvinceCopyWith<$Res> implements $ProvinceCopyWith<$Res> {
  factory _$ProvinceCopyWith(_Province value, $Res Function(_Province) _then) = __$ProvinceCopyWithImpl;
@override @useResult
$Res call({
 String codigo, String descripcion, String codigoDepartamento, List<District>? distritos, String? codigoProvincia
});




}
/// @nodoc
class __$ProvinceCopyWithImpl<$Res>
    implements _$ProvinceCopyWith<$Res> {
  __$ProvinceCopyWithImpl(this._self, this._then);

  final _Province _self;
  final $Res Function(_Province) _then;

/// Create a copy of Province
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? codigo = null,Object? descripcion = null,Object? codigoDepartamento = null,Object? distritos = freezed,Object? codigoProvincia = freezed,}) {
  return _then(_Province(
codigo: null == codigo ? _self.codigo : codigo // ignore: cast_nullable_to_non_nullable
as String,descripcion: null == descripcion ? _self.descripcion : descripcion // ignore: cast_nullable_to_non_nullable
as String,codigoDepartamento: null == codigoDepartamento ? _self.codigoDepartamento : codigoDepartamento // ignore: cast_nullable_to_non_nullable
as String,distritos: freezed == distritos ? _self._distritos : distritos // ignore: cast_nullable_to_non_nullable
as List<District>?,codigoProvincia: freezed == codigoProvincia ? _self.codigoProvincia : codigoProvincia // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$District implements DiagnosticableTreeMixin {

 String get codigo; String get descripcion; String get codigoProvincia;
/// Create a copy of District
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$DistrictCopyWith<District> get copyWith => _$DistrictCopyWithImpl<District>(this as District, _$identity);

  /// Serializes this District to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'District'))
    ..add(DiagnosticsProperty('codigo', codigo))..add(DiagnosticsProperty('descripcion', descripcion))..add(DiagnosticsProperty('codigoProvincia', codigoProvincia));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is District&&(identical(other.codigo, codigo) || other.codigo == codigo)&&(identical(other.descripcion, descripcion) || other.descripcion == descripcion)&&(identical(other.codigoProvincia, codigoProvincia) || other.codigoProvincia == codigoProvincia));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codigo,descripcion,codigoProvincia);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'District(codigo: $codigo, descripcion: $descripcion, codigoProvincia: $codigoProvincia)';
}


}

/// @nodoc
abstract mixin class $DistrictCopyWith<$Res>  {
  factory $DistrictCopyWith(District value, $Res Function(District) _then) = _$DistrictCopyWithImpl;
@useResult
$Res call({
 String codigo, String descripcion, String codigoProvincia
});




}
/// @nodoc
class _$DistrictCopyWithImpl<$Res>
    implements $DistrictCopyWith<$Res> {
  _$DistrictCopyWithImpl(this._self, this._then);

  final District _self;
  final $Res Function(District) _then;

/// Create a copy of District
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? codigo = null,Object? descripcion = null,Object? codigoProvincia = null,}) {
  return _then(_self.copyWith(
codigo: null == codigo ? _self.codigo : codigo // ignore: cast_nullable_to_non_nullable
as String,descripcion: null == descripcion ? _self.descripcion : descripcion // ignore: cast_nullable_to_non_nullable
as String,codigoProvincia: null == codigoProvincia ? _self.codigoProvincia : codigoProvincia // ignore: cast_nullable_to_non_nullable
as String,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _District with DiagnosticableTreeMixin implements District {
  const _District({required this.codigo, required this.descripcion, required this.codigoProvincia});
  factory _District.fromJson(Map<String, dynamic> json) => _$DistrictFromJson(json);

@override final  String codigo;
@override final  String descripcion;
@override final  String codigoProvincia;

/// Create a copy of District
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$DistrictCopyWith<_District> get copyWith => __$DistrictCopyWithImpl<_District>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$DistrictToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'District'))
    ..add(DiagnosticsProperty('codigo', codigo))..add(DiagnosticsProperty('descripcion', descripcion))..add(DiagnosticsProperty('codigoProvincia', codigoProvincia));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _District&&(identical(other.codigo, codigo) || other.codigo == codigo)&&(identical(other.descripcion, descripcion) || other.descripcion == descripcion)&&(identical(other.codigoProvincia, codigoProvincia) || other.codigoProvincia == codigoProvincia));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,codigo,descripcion,codigoProvincia);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'District(codigo: $codigo, descripcion: $descripcion, codigoProvincia: $codigoProvincia)';
}


}

/// @nodoc
abstract mixin class _$DistrictCopyWith<$Res> implements $DistrictCopyWith<$Res> {
  factory _$DistrictCopyWith(_District value, $Res Function(_District) _then) = __$DistrictCopyWithImpl;
@override @useResult
$Res call({
 String codigo, String descripcion, String codigoProvincia
});




}
/// @nodoc
class __$DistrictCopyWithImpl<$Res>
    implements _$DistrictCopyWith<$Res> {
  __$DistrictCopyWithImpl(this._self, this._then);

  final _District _self;
  final $Res Function(_District) _then;

/// Create a copy of District
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? codigo = null,Object? descripcion = null,Object? codigoProvincia = null,}) {
  return _then(_District(
codigo: null == codigo ? _self.codigo : codigo // ignore: cast_nullable_to_non_nullable
as String,descripcion: null == descripcion ? _self.descripcion : descripcion // ignore: cast_nullable_to_non_nullable
as String,codigoProvincia: null == codigoProvincia ? _self.codigoProvincia : codigoProvincia // ignore: cast_nullable_to_non_nullable
as String,
  ));
}


}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'ubigeo_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_UbigeoResponse _$UbigeoResponseFromJson(Map<String, dynamic> json) =>
    _UbigeoResponse(
      codigo: json['codigo'] as String,
      mensaje: json['mensaje'] as String,
      data:
          (json['data'] as List<dynamic>)
              .map((e) => Department.fromJson(e as Map<String, dynamic>))
              .toList(),
      entidad: json['entidad'],
      cantidad: (json['cantidad'] as num).toInt(),
      codigoEntidad: json['codigoEntidad'],
      codigosValidos: json['codigosValidos'],
      paginas: json['paginas'],
      totalData: json['totalData'],
      resultadoProceso: json['resultadoProceso'],
      mensajesErrores: json['mensajesErrores'],
    );

Map<String, dynamic> _$UbigeoResponseToJson(_UbigeoResponse instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'mensaje': instance.mensaje,
      'data': instance.data,
      'entidad': instance.entidad,
      'cantidad': instance.cantidad,
      'codigoEntidad': instance.codigoEntidad,
      'codigosValidos': instance.codigosValidos,
      'paginas': instance.paginas,
      'totalData': instance.totalData,
      'resultadoProceso': instance.resultadoProceso,
      'mensajesErrores': instance.mensajesErrores,
    };

_Department _$DepartmentFromJson(Map<String, dynamic> json) => _Department(
  codigo: json['codigo'] as String,
  descripcion: json['descripcion'] as String,
  provincias:
      (json['provincias'] as List<dynamic>)
          .map((e) => Province.fromJson(e as Map<String, dynamic>))
          .toList(),
);

Map<String, dynamic> _$DepartmentToJson(_Department instance) =>
    <String, dynamic>{
      'codigo': instance.codigo,
      'descripcion': instance.descripcion,
      'provincias': instance.provincias,
    };

_Province _$ProvinceFromJson(Map<String, dynamic> json) => _Province(
  codigo: json['codigo'] as String,
  descripcion: json['descripcion'] as String,
  codigoDepartamento: json['codigoDepartamento'] as String,
  distritos:
      (json['distritos'] as List<dynamic>?)
          ?.map((e) => District.fromJson(e as Map<String, dynamic>))
          .toList(),
  codigoProvincia: json['codigoProvincia'] as String?,
);

Map<String, dynamic> _$ProvinceToJson(_Province instance) => <String, dynamic>{
  'codigo': instance.codigo,
  'descripcion': instance.descripcion,
  'codigoDepartamento': instance.codigoDepartamento,
  'distritos': instance.distritos,
  'codigoProvincia': instance.codigoProvincia,
};

_District _$DistrictFromJson(Map<String, dynamic> json) => _District(
  codigo: json['codigo'] as String,
  descripcion: json['descripcion'] as String,
  codigoProvincia: json['codigoProvincia'] as String,
);

Map<String, dynamic> _$DistrictToJson(_District instance) => <String, dynamic>{
  'codigo': instance.codigo,
  'descripcion': instance.descripcion,
  'codigoProvincia': instance.codigoProvincia,
};

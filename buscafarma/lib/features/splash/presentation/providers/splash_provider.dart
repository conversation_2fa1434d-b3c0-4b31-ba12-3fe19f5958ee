import 'dart:async';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/splash/domain/entities/splash_state.dart';
import 'package:buscafarma/features/auth/presentation/providers/auth_provider.dart';
import 'package:buscafarma/core/providers/theme_provider.dart';
import 'package:buscafarma/features/update/presentation/services/update_service.dart';

/// Provider for splash screen state management
final splashProvider = StateNotifierProvider<SplashNotifier, SplashState>((ref) {
  return SplashNotifier(ref);
});

/// StateNotifier for managing splash screen initialization logic
class SplashNotifier extends StateNotifier<SplashState> {
  final Ref _ref;
  Timer? _minimumDisplayTimer;
  Timer? _timeoutTimer;

  /// Minimum time to display splash screen for good UX (in milliseconds)
  static const int _minimumDisplayTimeMs = 1500;

  /// Maximum time to wait for initialization before timing out (in milliseconds)
  static const int _timeoutMs = 10000;

  SplashNotifier(this._ref) : super(SplashState.initial());

  /// Initialize the application and determine navigation target
  Future<void> initialize() async {
    if (state.status != SplashStatus.initializing) {
      return; // Already initialized or in progress
    }

    // Start minimum display timer
    _startMinimumDisplayTimer();

    // Start timeout timer
    _startTimeoutTimer();

    try {
      // Step 1: Check authentication status
      await _checkAuthentication();

      // Step 2: Load theme preferences
      await _loadThemePreferences();

      // Step 3: Check for app updates
      await _checkForUpdates();

      // Step 4: Wait for minimum display time if needed
      await _waitForMinimumTime();

      // Step 5: Determine navigation target and complete initialization
      await _completeInitialization();
    } catch (error) {
      _handleError('Initialization failed: ${error.toString()}');
    } finally {
      _cancelTimers();
    }
  }

  /// Check user authentication status
  Future<void> _checkAuthentication() async {
    state = state.copyWith(status: SplashStatus.checkingAuth, progress: 0.2);

    try {
      // Use existing auth provider to check authentication
      await _ref.read(authProvider.notifier).checkAuthState();

      state = state.copyWith(authCheckComplete: true, progress: 0.6);
    } catch (error) {
      // If auth check fails, we'll still continue but mark as unauthenticated
      state = state.copyWith(authCheckComplete: true, progress: 0.6);
    }
  }

  /// Load theme preferences
  Future<void> _loadThemePreferences() async {
    state = state.copyWith(status: SplashStatus.loadingPreferences, progress: 0.6);

    try {
      // Theme provider automatically loads preferences on initialization
      // We just need to ensure it's initialized
      _ref.read(themeProvider);

      // Small delay to ensure theme loading is complete
      await Future.delayed(const Duration(milliseconds: 300));

      state = state.copyWith(themeLoadComplete: true, progress: 0.7);
    } catch (error) {
      // Theme loading failure is not critical, continue with defaults
      state = state.copyWith(themeLoadComplete: true, progress: 0.7);
    }
  }

  /// Check for app updates
  Future<void> _checkForUpdates() async {
    state = state.copyWith(
      status: SplashStatus.loadingPreferences, // Reuse existing status
      progress: 0.8,
    );

    try {
      // Get update service and check for updates
      final updateService = _ref.read(updateServiceProvider);

      // Check if updates are supported on this platform
      final isSupported = await updateService.isUpdateSupported();
      if (!isSupported) {
        // Skip update check on unsupported platforms (iOS, etc.)
        state = state.copyWith(progress: 0.9);
        return;
      }

      // Check for updates (this will handle mandatory updates automatically)
      await updateService.checkAndHandleUpdate(autoStartMandatory: true, showOptionalPrompt: false);

      state = state.copyWith(progress: 0.9);
    } catch (error) {
      // Update check failure is not critical for app startup
      // Log the error but continue with initialization
      state = state.copyWith(progress: 0.9);
    }
  }

  /// Wait for minimum display time to elapse
  Future<void> _waitForMinimumTime() async {
    if (!state.minimumTimeElapsed) {
      // Wait for the minimum display timer to complete
      final completer = Completer<void>();

      // Check if timer is still active
      if (_minimumDisplayTimer?.isActive == true) {
        // Create a new timer for the remaining time
        Timer(const Duration(milliseconds: 100), () {
          _checkMinimumTimeCompleted(completer);
        });

        await completer.future;
      } else {
        // Timer already completed
        state = state.copyWith(minimumTimeElapsed: true);
      }
    }
  }

  /// Recursively check if minimum time has elapsed
  void _checkMinimumTimeCompleted(Completer<void> completer) {
    if (state.minimumTimeElapsed || _minimumDisplayTimer?.isActive != true) {
      if (!completer.isCompleted) {
        completer.complete();
      }
    } else {
      // Check again in 100ms
      Timer(const Duration(milliseconds: 100), () {
        _checkMinimumTimeCompleted(completer);
      });
    }
  }

  /// Complete initialization and determine navigation target
  Future<void> _completeInitialization() async {
    // Determine navigation target based on authentication status
    final authState = _ref.read(authProvider);
    final navigationTarget = authState.isAuthenticated ? SplashNavigationTarget.main : SplashNavigationTarget.login;

    state = SplashState.completed(navigationTarget);
  }

  /// Start minimum display timer
  void _startMinimumDisplayTimer() {
    _minimumDisplayTimer = Timer(Duration(milliseconds: _minimumDisplayTimeMs), () {
      state = state.copyWith(minimumTimeElapsed: true);
    });
  }

  /// Start timeout timer
  void _startTimeoutTimer() {
    _timeoutTimer = Timer(Duration(milliseconds: _timeoutMs), () {
      if (state.status != SplashStatus.completed && state.status != SplashStatus.error) {
        _handleError('Initialization timed out');
      }
    });
  }

  /// Handle initialization errors
  void _handleError(String message) {
    state = SplashState.error(message);
  }

  /// Retry initialization after error
  Future<void> retry() async {
    state = SplashState.initial();
    await initialize();
  }

  /// Cancel all active timers
  void _cancelTimers() {
    _minimumDisplayTimer?.cancel();
    _timeoutTimer?.cancel();
  }

  @override
  void dispose() {
    _cancelTimers();
    super.dispose();
  }
}

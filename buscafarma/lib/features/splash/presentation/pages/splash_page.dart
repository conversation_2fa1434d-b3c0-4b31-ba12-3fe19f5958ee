import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/splash/presentation/providers/splash_provider.dart';
import 'package:buscafarma/features/splash/domain/entities/splash_state.dart';
import 'package:buscafarma/core/router/app_router.dart';
import 'package:buscafarma/core/router/app_routes.dart';
import 'package:buscafarma/core/widgets/app_logo.dart';
import 'package:buscafarma/core/animations/animation_constants.dart';
import 'package:buscafarma/core/animations/staggered_animation_controller.dart';
import 'package:buscafarma/core/widgets/animated_loading_indicator.dart';

/// Splash screen page that handles app initialization
class SplashPage extends ConsumerStatefulWidget {
  const SplashPage({super.key});

  @override
  ConsumerState<SplashPage> createState() => _SplashPageState();
}

class _SplashPageState extends ConsumerState<SplashPage> with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late StaggeredAnimationController _staggeredController;

  // Individual animations for each element
  late Animation<double> _logoFadeAnimation;
  late Animation<double> _logoScaleAnimation;
  late Animation<double> _logoRotationAnimation;
  late Animation<double> _titleFadeAnimation;
  late Animation<Offset> _titleSlideAnimation;
  late Animation<double> _taglineFadeAnimation;
  late Animation<Offset> _taglineSlideAnimation;
  late Animation<double> _loadingFadeAnimation;
  late Animation<double> _loadingScaleAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize main animation controller with enhanced duration
    _animationController = AnimationController(duration: AnimationConstants.splashEntranceDuration, vsync: this);

    // Initialize staggered animation controller
    _staggeredController = StaggeredAnimationController(controller: _animationController);

    _setupAnimations();

    // Start animations
    _animationController.forward();

    // Start initialization
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(splashProvider.notifier).initialize();
    });
  }

  void _setupAnimations() {
    // Logo animations - bounce entrance with rotation
    _logoFadeAnimation = _staggeredController.createFadeAnimation(key: 'logo_fade', delay: AnimationTimings.splash['logo']!, duration: const Duration(milliseconds: 600));

    _logoScaleAnimation = _staggeredController.createScaleAnimation(
      key: 'logo_scale',
      delay: AnimationTimings.splash['logo']!,
      fromScale: AnimationConstants.logoEntranceScale,
      duration: const Duration(milliseconds: 800),
      curve: AnimationConstants.bounceCurve,
    );

    _logoRotationAnimation = _staggeredController.createRotationAnimation(key: 'logo_rotation', delay: AnimationTimings.splash['logo']!, duration: const Duration(milliseconds: 600));

    // Title animations - slide from left
    _titleFadeAnimation = _staggeredController.createFadeAnimation(key: 'title_fade', delay: AnimationTimings.splash['title']!, duration: const Duration(milliseconds: 400));

    _titleSlideAnimation = _staggeredController.createSlideAnimation(key: 'title_slide', delay: AnimationTimings.splash['title']!, fromOffset: AnimationConstants.slideFromLeft, duration: const Duration(milliseconds: 500));

    // Tagline animations - slide from right
    _taglineFadeAnimation = _staggeredController.createFadeAnimation(key: 'tagline_fade', delay: AnimationTimings.splash['tagline']!, duration: const Duration(milliseconds: 400));

    _taglineSlideAnimation = _staggeredController.createSlideAnimation(key: 'tagline_slide', delay: AnimationTimings.splash['tagline']!, fromOffset: AnimationConstants.slideFromRight, duration: const Duration(milliseconds: 500));

    // Loading indicator animations - scale entrance
    _loadingFadeAnimation = _staggeredController.createFadeAnimation(key: 'loading_fade', delay: AnimationTimings.splash['loading']!, duration: const Duration(milliseconds: 300));

    _loadingScaleAnimation = _staggeredController.createScaleAnimation(key: 'loading_scale', delay: AnimationTimings.splash['loading']!, fromScale: 0.5, duration: const Duration(milliseconds: 400), curve: AnimationConstants.bounceCurve);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final splashState = ref.watch(splashProvider);

    // Listen for navigation events
    ref.listen<SplashState>(splashProvider, (previous, current) {
      if (current.isReadyToNavigate && current.navigationTarget != null) {
        _navigateToTarget(current.navigationTarget!);
      }
    });

    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.surface,
      body: SafeArea(
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return _buildContent(context, splashState);
          },
        ),
      ),
    );
  }

  /// Build the main content based on splash state
  Widget _buildContent(BuildContext context, SplashState splashState) {
    if (splashState.hasError) {
      return _buildErrorContent(context, splashState);
    }

    return _buildLoadingContent(context, splashState);
  }

  /// Build loading content with app branding and progress indicator
  Widget _buildLoadingContent(BuildContext context, SplashState splashState) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // App logo with sophisticated entrance animation
          FadeTransition(opacity: _logoFadeAnimation, child: ScaleTransition(scale: _logoScaleAnimation, child: Transform.rotate(angle: _logoRotationAnimation.value, child: AppLogo.large(color: colorScheme.primary)))),

          const SizedBox(height: 20),

          // App name with slide animation from left
          SlideTransition(position: _titleSlideAnimation, child: FadeTransition(opacity: _titleFadeAnimation, child: Text('BuscaFarma', style: theme.textTheme.headlineMedium?.copyWith(fontWeight: FontWeight.bold, color: colorScheme.onSurface)))),

          const SizedBox(height: 16),

          // App tagline with slide animation from right
          SlideTransition(
            position: _taglineSlideAnimation,
            child: FadeTransition(opacity: _taglineFadeAnimation, child: Text('Encuentra farmacias cercanas a ti', style: theme.textTheme.bodyLarge?.copyWith(color: colorScheme.onSurfaceVariant), textAlign: TextAlign.center)),
          ),

          const SizedBox(height: 60),

          // Enhanced loading indicator with scale animation
          FadeTransition(
            opacity: _loadingFadeAnimation,
            child: ScaleTransition(
              scale: _loadingScaleAnimation,
              child: AnimatedLoadingIndicator(size: 40, color: colorScheme.primary, progress: splashState.progress > 0 ? splashState.progress : null, state: _getLoadingStateFromSplash(splashState)),
            ),
          ),

          const SizedBox(height: 24),

          // Loading status text with fade animation
          FadeTransition(opacity: _loadingFadeAnimation, child: _buildStatusText(context, splashState)),
        ],
      ),
    );
  }

  /// Convert splash state to loading indicator state
  LoadingState _getLoadingStateFromSplash(SplashState splashState) {
    if (splashState.hasError) {
      return LoadingState.error;
    } else if (splashState.status == SplashStatus.completed) {
      return LoadingState.success;
    } else {
      return LoadingState.loading;
    }
  }

  /// Build error content with retry option
  Widget _buildErrorContent(BuildContext context, SplashState splashState) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // Error icon
            Icon(Icons.error_outline, size: 64, color: colorScheme.error),

            const SizedBox(height: 24),

            // Error title
            Text('Error de inicialización', style: theme.textTheme.headlineSmall?.copyWith(color: colorScheme.error, fontWeight: FontWeight.bold), textAlign: TextAlign.center),

            const SizedBox(height: 16),

            // Error message
            Text(splashState.errorMessage ?? 'Ha ocurrido un error inesperado', style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.onSurfaceVariant), textAlign: TextAlign.center),

            const SizedBox(height: 32),

            // Retry button
            FilledButton.icon(
              onPressed: () {
                ref.read(splashProvider.notifier).retry();
              },
              icon: const Icon(Icons.refresh),
              label: const Text('Reintentar'),
            ),
          ],
        ),
      ),
    );
  }

  /// Build status text based on current initialization step
  Widget _buildStatusText(BuildContext context, SplashState splashState) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    String statusText;
    switch (splashState.status) {
      case SplashStatus.initializing:
        statusText = 'Iniciando aplicación...';
        break;
      case SplashStatus.checkingAuth:
        statusText = 'Verificando autenticación...';
        break;
      case SplashStatus.loadingPreferences:
        statusText = 'Cargando preferencias...';
        break;
      case SplashStatus.completed:
        statusText = 'Listo';
        break;
      case SplashStatus.error:
        statusText = 'Error';
        break;
    }

    return AnimatedSwitcher(duration: const Duration(milliseconds: 300), child: Text(statusText, key: ValueKey(statusText), style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.onSurfaceVariant), textAlign: TextAlign.center));
  }

  /// Navigate to the appropriate target screen
  void _navigateToTarget(SplashNavigationTarget target) {
    final targetRoute = target == SplashNavigationTarget.main ? AppRoutes.map : AppRoutes.login;

    // Use replace and clearStack to ensure splash screen is removed from navigation stack
    AppRouter.navigateTo(context, targetRoute, replace: true, clearStack: true);
  }
}

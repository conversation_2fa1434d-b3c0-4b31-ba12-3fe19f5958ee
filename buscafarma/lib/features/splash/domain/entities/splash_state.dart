import 'package:freezed_annotation/freezed_annotation.dart';

part 'splash_state.freezed.dart';

/// Enumeration of possible splash screen initialization statuses
enum SplashStatus {
  /// Initial state when splash screen starts
  initializing,

  /// Authentication check in progress
  checkingAuth,

  /// Loading user preferences and theme
  loadingPreferences,

  /// Initialization completed successfully
  completed,

  /// An error occurred during initialization
  error,
}

/// Enumeration of possible navigation targets after splash completion
enum SplashNavigationTarget {
  /// Navigate to login screen (user not authenticated)
  login,

  /// Navigate to main app/map screen (user authenticated)
  main,
}

/// Immutable state model for splash screen initialization
@freezed
abstract class SplashState with _$SplashState {
  const factory SplashState({
    /// Current initialization status
    @Default(SplashStatus.initializing) SplashStatus status,

    /// Error message if initialization fails
    String? errorMessage,

    /// Target route to navigate to after initialization
    SplashNavigationTarget? navigationTarget,

    /// Whether authentication check is complete
    @Default(false) bool authCheckComplete,

    /// Whether theme loading is complete
    @Default(false) bool themeLoadComplete,

    /// Whether minimum display time has elapsed
    @Default(false) bool minimumTimeElapsed,

    /// Whether first-time announcement should be shown after navigation
    @Default(false) bool shouldShowAnnouncement,

    /// Progress percentage (0.0 to 1.0) for visual feedback
    @Default(0.0) double progress,
  }) = _SplashState;

  /// Factory constructor for initial state
  factory SplashState.initial() => const SplashState();

  /// Factory constructor for error state
  factory SplashState.error(String message) => SplashState(status: SplashStatus.error, errorMessage: message);

  /// Factory constructor for completed state
  factory SplashState.completed(SplashNavigationTarget target) => SplashState(status: SplashStatus.completed, navigationTarget: target, authCheckComplete: true, themeLoadComplete: true, minimumTimeElapsed: true, progress: 1.0);
}

/// Extension methods for SplashState
extension SplashStateExtensions on SplashState {
  /// Whether initialization is in progress
  bool get isInitializing => status == SplashStatus.initializing || status == SplashStatus.checkingAuth || status == SplashStatus.loadingPreferences;

  /// Whether initialization is complete and ready to navigate
  bool get isReadyToNavigate => status == SplashStatus.completed && navigationTarget != null;

  /// Whether an error occurred
  bool get hasError => status == SplashStatus.error;

  /// Whether all initialization tasks are complete
  bool get allTasksComplete => authCheckComplete && themeLoadComplete && minimumTimeElapsed;
}

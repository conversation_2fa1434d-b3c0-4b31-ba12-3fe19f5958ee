// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'splash_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$SplashState {

/// Current initialization status
 SplashStatus get status;/// Error message if initialization fails
 String? get errorMessage;/// Target route to navigate to after initialization
 SplashNavigationTarget? get navigationTarget;/// Whether authentication check is complete
 bool get authCheckComplete;/// Whether theme loading is complete
 bool get themeLoadComplete;/// Whether minimum display time has elapsed
 bool get minimumTimeElapsed;/// Whether first-time announcement should be shown after navigation
 bool get shouldShowAnnouncement;/// Progress percentage (0.0 to 1.0) for visual feedback
 double get progress;
/// Create a copy of SplashState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$SplashStateCopyWith<SplashState> get copyWith => _$SplashStateCopyWithImpl<SplashState>(this as SplashState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is SplashState&&(identical(other.status, status) || other.status == status)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.navigationTarget, navigationTarget) || other.navigationTarget == navigationTarget)&&(identical(other.authCheckComplete, authCheckComplete) || other.authCheckComplete == authCheckComplete)&&(identical(other.themeLoadComplete, themeLoadComplete) || other.themeLoadComplete == themeLoadComplete)&&(identical(other.minimumTimeElapsed, minimumTimeElapsed) || other.minimumTimeElapsed == minimumTimeElapsed)&&(identical(other.shouldShowAnnouncement, shouldShowAnnouncement) || other.shouldShowAnnouncement == shouldShowAnnouncement)&&(identical(other.progress, progress) || other.progress == progress));
}


@override
int get hashCode => Object.hash(runtimeType,status,errorMessage,navigationTarget,authCheckComplete,themeLoadComplete,minimumTimeElapsed,shouldShowAnnouncement,progress);

@override
String toString() {
  return 'SplashState(status: $status, errorMessage: $errorMessage, navigationTarget: $navigationTarget, authCheckComplete: $authCheckComplete, themeLoadComplete: $themeLoadComplete, minimumTimeElapsed: $minimumTimeElapsed, shouldShowAnnouncement: $shouldShowAnnouncement, progress: $progress)';
}


}

/// @nodoc
abstract mixin class $SplashStateCopyWith<$Res>  {
  factory $SplashStateCopyWith(SplashState value, $Res Function(SplashState) _then) = _$SplashStateCopyWithImpl;
@useResult
$Res call({
 SplashStatus status, String? errorMessage, SplashNavigationTarget? navigationTarget, bool authCheckComplete, bool themeLoadComplete, bool minimumTimeElapsed, bool shouldShowAnnouncement, double progress
});




}
/// @nodoc
class _$SplashStateCopyWithImpl<$Res>
    implements $SplashStateCopyWith<$Res> {
  _$SplashStateCopyWithImpl(this._self, this._then);

  final SplashState _self;
  final $Res Function(SplashState) _then;

/// Create a copy of SplashState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? errorMessage = freezed,Object? navigationTarget = freezed,Object? authCheckComplete = null,Object? themeLoadComplete = null,Object? minimumTimeElapsed = null,Object? shouldShowAnnouncement = null,Object? progress = null,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SplashStatus,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,navigationTarget: freezed == navigationTarget ? _self.navigationTarget : navigationTarget // ignore: cast_nullable_to_non_nullable
as SplashNavigationTarget?,authCheckComplete: null == authCheckComplete ? _self.authCheckComplete : authCheckComplete // ignore: cast_nullable_to_non_nullable
as bool,themeLoadComplete: null == themeLoadComplete ? _self.themeLoadComplete : themeLoadComplete // ignore: cast_nullable_to_non_nullable
as bool,minimumTimeElapsed: null == minimumTimeElapsed ? _self.minimumTimeElapsed : minimumTimeElapsed // ignore: cast_nullable_to_non_nullable
as bool,shouldShowAnnouncement: null == shouldShowAnnouncement ? _self.shouldShowAnnouncement : shouldShowAnnouncement // ignore: cast_nullable_to_non_nullable
as bool,progress: null == progress ? _self.progress : progress // ignore: cast_nullable_to_non_nullable
as double,
  ));
}

}


/// @nodoc


class _SplashState implements SplashState {
  const _SplashState({this.status = SplashStatus.initializing, this.errorMessage, this.navigationTarget, this.authCheckComplete = false, this.themeLoadComplete = false, this.minimumTimeElapsed = false, this.shouldShowAnnouncement = false, this.progress = 0.0});
  

/// Current initialization status
@override@JsonKey() final  SplashStatus status;
/// Error message if initialization fails
@override final  String? errorMessage;
/// Target route to navigate to after initialization
@override final  SplashNavigationTarget? navigationTarget;
/// Whether authentication check is complete
@override@JsonKey() final  bool authCheckComplete;
/// Whether theme loading is complete
@override@JsonKey() final  bool themeLoadComplete;
/// Whether minimum display time has elapsed
@override@JsonKey() final  bool minimumTimeElapsed;
/// Whether first-time announcement should be shown after navigation
@override@JsonKey() final  bool shouldShowAnnouncement;
/// Progress percentage (0.0 to 1.0) for visual feedback
@override@JsonKey() final  double progress;

/// Create a copy of SplashState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$SplashStateCopyWith<_SplashState> get copyWith => __$SplashStateCopyWithImpl<_SplashState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _SplashState&&(identical(other.status, status) || other.status == status)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.navigationTarget, navigationTarget) || other.navigationTarget == navigationTarget)&&(identical(other.authCheckComplete, authCheckComplete) || other.authCheckComplete == authCheckComplete)&&(identical(other.themeLoadComplete, themeLoadComplete) || other.themeLoadComplete == themeLoadComplete)&&(identical(other.minimumTimeElapsed, minimumTimeElapsed) || other.minimumTimeElapsed == minimumTimeElapsed)&&(identical(other.shouldShowAnnouncement, shouldShowAnnouncement) || other.shouldShowAnnouncement == shouldShowAnnouncement)&&(identical(other.progress, progress) || other.progress == progress));
}


@override
int get hashCode => Object.hash(runtimeType,status,errorMessage,navigationTarget,authCheckComplete,themeLoadComplete,minimumTimeElapsed,shouldShowAnnouncement,progress);

@override
String toString() {
  return 'SplashState(status: $status, errorMessage: $errorMessage, navigationTarget: $navigationTarget, authCheckComplete: $authCheckComplete, themeLoadComplete: $themeLoadComplete, minimumTimeElapsed: $minimumTimeElapsed, shouldShowAnnouncement: $shouldShowAnnouncement, progress: $progress)';
}


}

/// @nodoc
abstract mixin class _$SplashStateCopyWith<$Res> implements $SplashStateCopyWith<$Res> {
  factory _$SplashStateCopyWith(_SplashState value, $Res Function(_SplashState) _then) = __$SplashStateCopyWithImpl;
@override @useResult
$Res call({
 SplashStatus status, String? errorMessage, SplashNavigationTarget? navigationTarget, bool authCheckComplete, bool themeLoadComplete, bool minimumTimeElapsed, bool shouldShowAnnouncement, double progress
});




}
/// @nodoc
class __$SplashStateCopyWithImpl<$Res>
    implements _$SplashStateCopyWith<$Res> {
  __$SplashStateCopyWithImpl(this._self, this._then);

  final _SplashState _self;
  final $Res Function(_SplashState) _then;

/// Create a copy of SplashState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? errorMessage = freezed,Object? navigationTarget = freezed,Object? authCheckComplete = null,Object? themeLoadComplete = null,Object? minimumTimeElapsed = null,Object? shouldShowAnnouncement = null,Object? progress = null,}) {
  return _then(_SplashState(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as SplashStatus,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,navigationTarget: freezed == navigationTarget ? _self.navigationTarget : navigationTarget // ignore: cast_nullable_to_non_nullable
as SplashNavigationTarget?,authCheckComplete: null == authCheckComplete ? _self.authCheckComplete : authCheckComplete // ignore: cast_nullable_to_non_nullable
as bool,themeLoadComplete: null == themeLoadComplete ? _self.themeLoadComplete : themeLoadComplete // ignore: cast_nullable_to_non_nullable
as bool,minimumTimeElapsed: null == minimumTimeElapsed ? _self.minimumTimeElapsed : minimumTimeElapsed // ignore: cast_nullable_to_non_nullable
as bool,shouldShowAnnouncement: null == shouldShowAnnouncement ? _self.shouldShowAnnouncement : shouldShowAnnouncement // ignore: cast_nullable_to_non_nullable
as bool,progress: null == progress ? _self.progress : progress // ignore: cast_nullable_to_non_nullable
as double,
  ));
}


}

// dart format on

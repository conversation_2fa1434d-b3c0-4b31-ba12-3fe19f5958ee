import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/widgets/ads/banner_ad_widget.dart';
import 'package:buscafarma/features/map/presentation/pages/map_page.dart';
import 'package:buscafarma/features/favorites/presentation/pages/favorites_page.dart';
import 'package:buscafarma/features/favorites/presentation/providers/favorites_provider.dart';
import 'package:buscafarma/core/animations/animation_constants.dart';
import 'package:buscafarma/core/providers/navigation_tracking_provider.dart';
import 'package:buscafarma/core/widgets/ads/interstitial_ad_manager.dart';

// Provider to track current navigation index
final navigationIndexProvider = StateProvider<int>((ref) => 0);

class MainNavigationPage extends ConsumerStatefulWidget {
  const MainNavigationPage({super.key});

  @override
  ConsumerState<MainNavigationPage> createState() => _MainNavigationPageState();
}

class _MainNavigationPageState extends ConsumerState<MainNavigationPage> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<Offset> _slideAnimation;
  int _previousIndex = 0;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(duration: AnimationConstants.stateTransitionDuration, vsync: this);
    _slideAnimation = Tween<Offset>(begin: Offset.zero, end: Offset.zero).animate(CurvedAnimation(parent: _animationController, curve: AnimationConstants.emphasizedCurve));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  /// Trigger slide animation when tab changes
  void _animateTabChange(int newIndex) {
    if (newIndex == _previousIndex) return;

    // Determine slide direction
    final slideOffset =
        newIndex > _previousIndex
            ? const Offset(1.0, 0.0) // Slide from right (going to favorites)
            : const Offset(-1.0, 0.0); // Slide from left (going to maps)

    _slideAnimation = Tween<Offset>(begin: slideOffset, end: Offset.zero).animate(CurvedAnimation(parent: _animationController, curve: AnimationConstants.emphasizedCurve));

    _previousIndex = newIndex;
    _animationController.reset();
    _animationController.forward();
  }

  /// Create animated overlay for smooth transitions
  Widget _buildAnimatedContent(int currentIndex) {
    return SlideTransition(position: _slideAnimation, child: IndexedStack(index: currentIndex, children: const [MapPage(), FavoritesPage()]));
  }

  @override
  Widget build(BuildContext context) {
    final currentIndex = ref.watch(navigationIndexProvider);
    final favoritesState = ref.watch(favoritesProvider);

    return PopScope(
      canPop: currentIndex == 0, // Only allow popping when on maps tab (index 0)
      onPopInvokedWithResult: (didPop, result) {
        // If we're on favorites tab (index 1) and the pop was prevented
        if (!didPop && currentIndex == 1) {
          // Trigger animation before switching to maps tab
          _animateTabChange(0);
          ref.read(navigationIndexProvider.notifier).state = 0;
        }
      },
      child: Scaffold(
        body: Column(
          children: [
            Expanded(child: _buildAnimatedContent(currentIndex)),
            // Banner ad above bottom navigation
            BannerAdWidget.navigationPage(
              onAdLoaded: () {
                debugPrint('Navigation page banner ad loaded');
              },
              onAdLoadFailed: () {
                debugPrint('Navigation page banner ad failed to load');
              },
            ),
          ],
        ),
        bottomNavigationBar: BottomNavigationBar(
          currentIndex: currentIndex,
          onTap: (index) async {
            // Track navigation action for ad frequency
            ref.read(navigationTrackingProvider.notifier).incrementNavigationAction();

            // Check if we should show an interstitial ad based on navigation frequency
            final shouldShowAd = ref.read(shouldShowNavigationAdProvider);
            if (shouldShowAd) {
              // Show interstitial ad with navigation context
              await InterstitialAdManager.showWithContext(ref, 'navigation_tab_change');
            }

            // Trigger animation before changing index
            _animateTabChange(index);
            ref.read(navigationIndexProvider.notifier).state = index;

            // Load favorites and clear errors when navigating to favorites tab
            if (index == 1) {
              final favoritesNotifier = ref.read(favoritesProvider.notifier);
              // Clear any existing errors
              favoritesNotifier.clearError();
              // Load favorites if empty or refresh if there were errors
              if (favoritesState.favorites.isEmpty && !favoritesState.isLoading) {
                favoritesNotifier.loadFavorites(refresh: true);
              }
            }
          },
          type: BottomNavigationBarType.fixed,
          items: [const BottomNavigationBarItem(icon: Icon(Icons.map), label: 'Mapa'), BottomNavigationBarItem(icon: _buildFavoritesIcon(context, favoritesState), label: 'Favoritos')],
        ),
      ),
    );
  }

  Widget _buildFavoritesIcon(BuildContext context, FavoritesState favoritesState) {
    final theme = Theme.of(context);
    final favoritesCount = favoritesState.pagination?.total ?? 0;

    if (favoritesCount == 0) {
      return const Icon(Icons.favorite_border);
    }

    return Stack(
      clipBehavior: Clip.none,
      children: [
        const Icon(Icons.favorite),
        if (favoritesCount > 0)
          Positioned(
            right: -6,
            top: -6,
            child: Container(
              padding: const EdgeInsets.all(2),
              decoration: BoxDecoration(color: theme.colorScheme.error, borderRadius: BorderRadius.circular(10)),
              constraints: const BoxConstraints(minWidth: 16, minHeight: 16),
              child: Text(favoritesCount > 99 ? '99+' : favoritesCount.toString(), style: TextStyle(color: theme.colorScheme.onError, fontSize: 10, fontWeight: FontWeight.bold), textAlign: TextAlign.center),
            ),
          ),
      ],
    );
  }
}

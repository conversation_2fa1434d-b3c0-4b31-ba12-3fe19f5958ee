import 'package:dio/dio.dart';
import 'package:buscafarma/features/geocoding/domain/entities/geocoding_detail_models.dart';
import 'package:buscafarma/features/geocoding/domain/repositories/geocoding_repository.dart';

class GeocodingRepositoryImpl implements GeocodingRepository {
  final Dio _dio;
  static const String _geoapifyBaseUrl = 'https://api.geoapify.com/v1/geocode';
  static const String _geoapifyApiKey = '********************************'; // API Key from user example

  GeocodingRepositoryImpl(this._dio);

  @override
  Future<GeocodingResult?> forwardGeocode(String address, {CancelToken? cancelToken}) async {
    try {
      final encodedAddress = Uri.encodeComponent(address);
      final url = '$_geoapifyBaseUrl/search?apiKey=$_geoapifyApiKey&text=$encodedAddress';

      final response = await _dio.get(url, cancelToken: cancelToken);

      if (response.statusCode == 200 && response.data != null) {
        final Map<String, dynamic> data = response.data as Map<String, dynamic>; // No need for json.decode if Dio parses it
        final List<dynamic> features = data['features'] as List<dynamic>? ?? [];

        if (features.isEmpty) return null;

        // Use the first feature as the most relevant result
        final Map<String, dynamic> feature = features[0] as Map<String, dynamic>;
        return GeocodingResult.fromGeoapifyFeatureJson(feature);
      } else {
        return null;
      }
    } on DioException catch (_) {
      return null;
    } catch (e) {
      print('Error in forwardGeocode for address $address: $e');
      return null;
    }
  }

  @override
  Future<GeocodingResult?> geocodeWithGoogleMaps(String address, {CancelToken? cancelToken}) async {
    try {
      final encodedAddress = Uri.encodeComponent(address);
      final url = 'https://www.google.com/maps/search/$encodedAddress';

      final headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 'Accept-Language': 'en-US,en;q=0.9,es;q=0.8'};

      final response = await _dio.get(url, options: Options(headers: headers), cancelToken: cancelToken);

      if (response.statusCode == 200) {
        final String responseBody = response.data as String;
        final RegExp latLongRegex = RegExp(r'@(-?\d+\.\d+),(-?\d+\.\d+)');
        final match = latLongRegex.firstMatch(responseBody);

        if (match != null && match.groupCount >= 2) {
          final latitude = double.parse(match.group(1)!);
          final longitude = double.parse(match.group(2)!);

          return GeocodingResult(latitude: latitude, longitude: longitude, displayName: address, placeId: 'google_${DateTime.now().millisecondsSinceEpoch}', rawData: {'source': 'google_maps', 'address': address});
        }
      }
      return null;
    } catch (e) {
      print('Error geocoding with Google Maps: $e');
      return null;
    }
  }

  @override
  Future<GeocodingLocationDetails?> reverseGeocode({required double latitude, required double longitude}) async {
    try {
      final url = '$_geoapifyBaseUrl/reverse?lat=$latitude&lon=$longitude&apiKey=$_geoapifyApiKey';

      final response = await _dio.get(url);

      if (response.statusCode == 200 && response.data != null) {
        final Map<String, dynamic> data = response.data as Map<String, dynamic>;
        final List<dynamic> features = data['features'] as List<dynamic>? ?? [];

        if (features.isEmpty) return null;

        // Use the properties of the first feature for reverse geocoding details
        final Map<String, dynamic> properties = features[0]['properties'] as Map<String, dynamic>? ?? {};
        return GeocodingLocationDetails.fromGeoapifyJson(properties);
      } else {
        return null;
      }
    } catch (e) {
      print('Error in reverseGeocode: $e');
      return null;
    }
  }
}

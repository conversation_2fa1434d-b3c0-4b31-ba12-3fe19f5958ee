// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'geocoding_detail_models.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$GeocodingLocationDetails implements DiagnosticableTreeMixin {

 String? get district; String? get province; String? get department; String? get neighborhood; String? get suburb; String? get postcode; String? get country; String? get fullAddress;
/// Create a copy of GeocodingLocationDetails
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GeocodingLocationDetailsCopyWith<GeocodingLocationDetails> get copyWith => _$GeocodingLocationDetailsCopyWithImpl<GeocodingLocationDetails>(this as GeocodingLocationDetails, _$identity);

  /// Serializes this GeocodingLocationDetails to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'GeocodingLocationDetails'))
    ..add(DiagnosticsProperty('district', district))..add(DiagnosticsProperty('province', province))..add(DiagnosticsProperty('department', department))..add(DiagnosticsProperty('neighborhood', neighborhood))..add(DiagnosticsProperty('suburb', suburb))..add(DiagnosticsProperty('postcode', postcode))..add(DiagnosticsProperty('country', country))..add(DiagnosticsProperty('fullAddress', fullAddress));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GeocodingLocationDetails&&(identical(other.district, district) || other.district == district)&&(identical(other.province, province) || other.province == province)&&(identical(other.department, department) || other.department == department)&&(identical(other.neighborhood, neighborhood) || other.neighborhood == neighborhood)&&(identical(other.suburb, suburb) || other.suburb == suburb)&&(identical(other.postcode, postcode) || other.postcode == postcode)&&(identical(other.country, country) || other.country == country)&&(identical(other.fullAddress, fullAddress) || other.fullAddress == fullAddress));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,district,province,department,neighborhood,suburb,postcode,country,fullAddress);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'GeocodingLocationDetails(district: $district, province: $province, department: $department, neighborhood: $neighborhood, suburb: $suburb, postcode: $postcode, country: $country, fullAddress: $fullAddress)';
}


}

/// @nodoc
abstract mixin class $GeocodingLocationDetailsCopyWith<$Res>  {
  factory $GeocodingLocationDetailsCopyWith(GeocodingLocationDetails value, $Res Function(GeocodingLocationDetails) _then) = _$GeocodingLocationDetailsCopyWithImpl;
@useResult
$Res call({
 String? district, String? province, String? department, String? neighborhood, String? suburb, String? postcode, String? country, String? fullAddress
});




}
/// @nodoc
class _$GeocodingLocationDetailsCopyWithImpl<$Res>
    implements $GeocodingLocationDetailsCopyWith<$Res> {
  _$GeocodingLocationDetailsCopyWithImpl(this._self, this._then);

  final GeocodingLocationDetails _self;
  final $Res Function(GeocodingLocationDetails) _then;

/// Create a copy of GeocodingLocationDetails
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? district = freezed,Object? province = freezed,Object? department = freezed,Object? neighborhood = freezed,Object? suburb = freezed,Object? postcode = freezed,Object? country = freezed,Object? fullAddress = freezed,}) {
  return _then(_self.copyWith(
district: freezed == district ? _self.district : district // ignore: cast_nullable_to_non_nullable
as String?,province: freezed == province ? _self.province : province // ignore: cast_nullable_to_non_nullable
as String?,department: freezed == department ? _self.department : department // ignore: cast_nullable_to_non_nullable
as String?,neighborhood: freezed == neighborhood ? _self.neighborhood : neighborhood // ignore: cast_nullable_to_non_nullable
as String?,suburb: freezed == suburb ? _self.suburb : suburb // ignore: cast_nullable_to_non_nullable
as String?,postcode: freezed == postcode ? _self.postcode : postcode // ignore: cast_nullable_to_non_nullable
as String?,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,fullAddress: freezed == fullAddress ? _self.fullAddress : fullAddress // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _GeocodingLocationDetails with DiagnosticableTreeMixin implements GeocodingLocationDetails {
  const _GeocodingLocationDetails({this.district, this.province, this.department, this.neighborhood, this.suburb, this.postcode, this.country, this.fullAddress});
  factory _GeocodingLocationDetails.fromJson(Map<String, dynamic> json) => _$GeocodingLocationDetailsFromJson(json);

@override final  String? district;
@override final  String? province;
@override final  String? department;
@override final  String? neighborhood;
@override final  String? suburb;
@override final  String? postcode;
@override final  String? country;
@override final  String? fullAddress;

/// Create a copy of GeocodingLocationDetails
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GeocodingLocationDetailsCopyWith<_GeocodingLocationDetails> get copyWith => __$GeocodingLocationDetailsCopyWithImpl<_GeocodingLocationDetails>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GeocodingLocationDetailsToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'GeocodingLocationDetails'))
    ..add(DiagnosticsProperty('district', district))..add(DiagnosticsProperty('province', province))..add(DiagnosticsProperty('department', department))..add(DiagnosticsProperty('neighborhood', neighborhood))..add(DiagnosticsProperty('suburb', suburb))..add(DiagnosticsProperty('postcode', postcode))..add(DiagnosticsProperty('country', country))..add(DiagnosticsProperty('fullAddress', fullAddress));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GeocodingLocationDetails&&(identical(other.district, district) || other.district == district)&&(identical(other.province, province) || other.province == province)&&(identical(other.department, department) || other.department == department)&&(identical(other.neighborhood, neighborhood) || other.neighborhood == neighborhood)&&(identical(other.suburb, suburb) || other.suburb == suburb)&&(identical(other.postcode, postcode) || other.postcode == postcode)&&(identical(other.country, country) || other.country == country)&&(identical(other.fullAddress, fullAddress) || other.fullAddress == fullAddress));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,district,province,department,neighborhood,suburb,postcode,country,fullAddress);

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'GeocodingLocationDetails(district: $district, province: $province, department: $department, neighborhood: $neighborhood, suburb: $suburb, postcode: $postcode, country: $country, fullAddress: $fullAddress)';
}


}

/// @nodoc
abstract mixin class _$GeocodingLocationDetailsCopyWith<$Res> implements $GeocodingLocationDetailsCopyWith<$Res> {
  factory _$GeocodingLocationDetailsCopyWith(_GeocodingLocationDetails value, $Res Function(_GeocodingLocationDetails) _then) = __$GeocodingLocationDetailsCopyWithImpl;
@override @useResult
$Res call({
 String? district, String? province, String? department, String? neighborhood, String? suburb, String? postcode, String? country, String? fullAddress
});




}
/// @nodoc
class __$GeocodingLocationDetailsCopyWithImpl<$Res>
    implements _$GeocodingLocationDetailsCopyWith<$Res> {
  __$GeocodingLocationDetailsCopyWithImpl(this._self, this._then);

  final _GeocodingLocationDetails _self;
  final $Res Function(_GeocodingLocationDetails) _then;

/// Create a copy of GeocodingLocationDetails
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? district = freezed,Object? province = freezed,Object? department = freezed,Object? neighborhood = freezed,Object? suburb = freezed,Object? postcode = freezed,Object? country = freezed,Object? fullAddress = freezed,}) {
  return _then(_GeocodingLocationDetails(
district: freezed == district ? _self.district : district // ignore: cast_nullable_to_non_nullable
as String?,province: freezed == province ? _self.province : province // ignore: cast_nullable_to_non_nullable
as String?,department: freezed == department ? _self.department : department // ignore: cast_nullable_to_non_nullable
as String?,neighborhood: freezed == neighborhood ? _self.neighborhood : neighborhood // ignore: cast_nullable_to_non_nullable
as String?,suburb: freezed == suburb ? _self.suburb : suburb // ignore: cast_nullable_to_non_nullable
as String?,postcode: freezed == postcode ? _self.postcode : postcode // ignore: cast_nullable_to_non_nullable
as String?,country: freezed == country ? _self.country : country // ignore: cast_nullable_to_non_nullable
as String?,fullAddress: freezed == fullAddress ? _self.fullAddress : fullAddress // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}


/// @nodoc
mixin _$GeocodingResult implements DiagnosticableTreeMixin {

 double get latitude; double get longitude; String get displayName; String? get placeId; GeocodingLocationDetails? get details; Map<String, dynamic>? get rawData;
/// Create a copy of GeocodingResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$GeocodingResultCopyWith<GeocodingResult> get copyWith => _$GeocodingResultCopyWithImpl<GeocodingResult>(this as GeocodingResult, _$identity);

  /// Serializes this GeocodingResult to a JSON map.
  Map<String, dynamic> toJson();

@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'GeocodingResult'))
    ..add(DiagnosticsProperty('latitude', latitude))..add(DiagnosticsProperty('longitude', longitude))..add(DiagnosticsProperty('displayName', displayName))..add(DiagnosticsProperty('placeId', placeId))..add(DiagnosticsProperty('details', details))..add(DiagnosticsProperty('rawData', rawData));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is GeocodingResult&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.placeId, placeId) || other.placeId == placeId)&&(identical(other.details, details) || other.details == details)&&const DeepCollectionEquality().equals(other.rawData, rawData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,latitude,longitude,displayName,placeId,details,const DeepCollectionEquality().hash(rawData));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'GeocodingResult(latitude: $latitude, longitude: $longitude, displayName: $displayName, placeId: $placeId, details: $details, rawData: $rawData)';
}


}

/// @nodoc
abstract mixin class $GeocodingResultCopyWith<$Res>  {
  factory $GeocodingResultCopyWith(GeocodingResult value, $Res Function(GeocodingResult) _then) = _$GeocodingResultCopyWithImpl;
@useResult
$Res call({
 double latitude, double longitude, String displayName, String? placeId, GeocodingLocationDetails? details, Map<String, dynamic>? rawData
});


$GeocodingLocationDetailsCopyWith<$Res>? get details;

}
/// @nodoc
class _$GeocodingResultCopyWithImpl<$Res>
    implements $GeocodingResultCopyWith<$Res> {
  _$GeocodingResultCopyWithImpl(this._self, this._then);

  final GeocodingResult _self;
  final $Res Function(GeocodingResult) _then;

/// Create a copy of GeocodingResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? latitude = null,Object? longitude = null,Object? displayName = null,Object? placeId = freezed,Object? details = freezed,Object? rawData = freezed,}) {
  return _then(_self.copyWith(
latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,placeId: freezed == placeId ? _self.placeId : placeId // ignore: cast_nullable_to_non_nullable
as String?,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as GeocodingLocationDetails?,rawData: freezed == rawData ? _self.rawData : rawData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}
/// Create a copy of GeocodingResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$GeocodingLocationDetailsCopyWith<$Res>? get details {
    if (_self.details == null) {
    return null;
  }

  return $GeocodingLocationDetailsCopyWith<$Res>(_self.details!, (value) {
    return _then(_self.copyWith(details: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _GeocodingResult with DiagnosticableTreeMixin implements GeocodingResult {
  const _GeocodingResult({required this.latitude, required this.longitude, required this.displayName, this.placeId, this.details, final  Map<String, dynamic>? rawData}): _rawData = rawData;
  factory _GeocodingResult.fromJson(Map<String, dynamic> json) => _$GeocodingResultFromJson(json);

@override final  double latitude;
@override final  double longitude;
@override final  String displayName;
@override final  String? placeId;
@override final  GeocodingLocationDetails? details;
 final  Map<String, dynamic>? _rawData;
@override Map<String, dynamic>? get rawData {
  final value = _rawData;
  if (value == null) return null;
  if (_rawData is EqualUnmodifiableMapView) return _rawData;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of GeocodingResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$GeocodingResultCopyWith<_GeocodingResult> get copyWith => __$GeocodingResultCopyWithImpl<_GeocodingResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$GeocodingResultToJson(this, );
}
@override
void debugFillProperties(DiagnosticPropertiesBuilder properties) {
  properties
    ..add(DiagnosticsProperty('type', 'GeocodingResult'))
    ..add(DiagnosticsProperty('latitude', latitude))..add(DiagnosticsProperty('longitude', longitude))..add(DiagnosticsProperty('displayName', displayName))..add(DiagnosticsProperty('placeId', placeId))..add(DiagnosticsProperty('details', details))..add(DiagnosticsProperty('rawData', rawData));
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _GeocodingResult&&(identical(other.latitude, latitude) || other.latitude == latitude)&&(identical(other.longitude, longitude) || other.longitude == longitude)&&(identical(other.displayName, displayName) || other.displayName == displayName)&&(identical(other.placeId, placeId) || other.placeId == placeId)&&(identical(other.details, details) || other.details == details)&&const DeepCollectionEquality().equals(other._rawData, _rawData));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,latitude,longitude,displayName,placeId,details,const DeepCollectionEquality().hash(_rawData));

@override
String toString({ DiagnosticLevel minLevel = DiagnosticLevel.info }) {
  return 'GeocodingResult(latitude: $latitude, longitude: $longitude, displayName: $displayName, placeId: $placeId, details: $details, rawData: $rawData)';
}


}

/// @nodoc
abstract mixin class _$GeocodingResultCopyWith<$Res> implements $GeocodingResultCopyWith<$Res> {
  factory _$GeocodingResultCopyWith(_GeocodingResult value, $Res Function(_GeocodingResult) _then) = __$GeocodingResultCopyWithImpl;
@override @useResult
$Res call({
 double latitude, double longitude, String displayName, String? placeId, GeocodingLocationDetails? details, Map<String, dynamic>? rawData
});


@override $GeocodingLocationDetailsCopyWith<$Res>? get details;

}
/// @nodoc
class __$GeocodingResultCopyWithImpl<$Res>
    implements _$GeocodingResultCopyWith<$Res> {
  __$GeocodingResultCopyWithImpl(this._self, this._then);

  final _GeocodingResult _self;
  final $Res Function(_GeocodingResult) _then;

/// Create a copy of GeocodingResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? latitude = null,Object? longitude = null,Object? displayName = null,Object? placeId = freezed,Object? details = freezed,Object? rawData = freezed,}) {
  return _then(_GeocodingResult(
latitude: null == latitude ? _self.latitude : latitude // ignore: cast_nullable_to_non_nullable
as double,longitude: null == longitude ? _self.longitude : longitude // ignore: cast_nullable_to_non_nullable
as double,displayName: null == displayName ? _self.displayName : displayName // ignore: cast_nullable_to_non_nullable
as String,placeId: freezed == placeId ? _self.placeId : placeId // ignore: cast_nullable_to_non_nullable
as String?,details: freezed == details ? _self.details : details // ignore: cast_nullable_to_non_nullable
as GeocodingLocationDetails?,rawData: freezed == rawData ? _self._rawData : rawData // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

/// Create a copy of GeocodingResult
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$GeocodingLocationDetailsCopyWith<$Res>? get details {
    if (_self.details == null) {
    return null;
  }

  return $GeocodingLocationDetailsCopyWith<$Res>(_self.details!, (value) {
    return _then(_self.copyWith(details: value));
  });
}
}

// dart format on

// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'geocoding_detail_models.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_GeocodingLocationDetails _$GeocodingLocationDetailsFromJson(
  Map<String, dynamic> json,
) => _GeocodingLocationDetails(
  district: json['district'] as String?,
  province: json['province'] as String?,
  department: json['department'] as String?,
  neighborhood: json['neighborhood'] as String?,
  suburb: json['suburb'] as String?,
  postcode: json['postcode'] as String?,
  country: json['country'] as String?,
  fullAddress: json['fullAddress'] as String?,
);

Map<String, dynamic> _$GeocodingLocationDetailsToJson(
  _GeocodingLocationDetails instance,
) => <String, dynamic>{
  'district': instance.district,
  'province': instance.province,
  'department': instance.department,
  'neighborhood': instance.neighborhood,
  'suburb': instance.suburb,
  'postcode': instance.postcode,
  'country': instance.country,
  'fullAddress': instance.fullAddress,
};

_GeocodingResult _$GeocodingResultFromJson(Map<String, dynamic> json) =>
    _GeocodingResult(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      displayName: json['displayName'] as String,
      placeId: json['placeId'] as String?,
      details:
          json['details'] == null
              ? null
              : GeocodingLocationDetails.fromJson(
                json['details'] as Map<String, dynamic>,
              ),
      rawData: json['rawData'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$GeocodingResultToJson(_GeocodingResult instance) =>
    <String, dynamic>{
      'latitude': instance.latitude,
      'longitude': instance.longitude,
      'displayName': instance.displayName,
      'placeId': instance.placeId,
      'details': instance.details,
      'rawData': instance.rawData,
    };

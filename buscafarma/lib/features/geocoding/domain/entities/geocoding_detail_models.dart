import 'package:flutter/foundation.dart';
import 'package:freezed_annotation/freezed_annotation.dart';

part 'geocoding_detail_models.freezed.dart';
part 'geocoding_detail_models.g.dart';

@freezed
abstract class GeocodingLocationDetails with _$GeocodingLocationDetails {
  const factory GeocodingLocationDetails({String? district, String? province, String? department, String? neighborhood, String? suburb, String? postcode, String? country, String? fullAddress}) = _GeocodingLocationDetails;

  factory GeocodingLocationDetails.fromJson(Map<String, dynamic> json) => _$GeocodingLocationDetailsFromJson(json);

  factory GeocodingLocationDetails.fromGeoapifyJson(Map<String, dynamic> json) {
    // Geoapify's reverse geocoding often returns results in an array,
    // typically we use the first one for the most relevant details.
    // The structure might be under 'properties' of the first feature.
    return GeocodingLocationDetails(
      district: json['district'] as String?,
      province: json['state'] as String?, // Geoapify often uses 'state' for province
      department: json['county'] as String?, // Geoapify often uses 'county' for department
      neighborhood: json['neighbourhood'] as String?,
      suburb: json['suburb'] as String?,
      postcode: json['postcode'] as String?,
      country: json['country'] as String?,
      fullAddress: json['formatted'] as String?,
    );
  }
}

@freezed
abstract class GeocodingResult with _$GeocodingResult {
  const factory GeocodingResult({
    required double latitude,
    required double longitude,
    required String displayName,
    String? placeId,
    GeocodingLocationDetails? details,
    Map<String, dynamic>? rawData, // Can store the original properties or feature
  }) = _GeocodingResult;

  factory GeocodingResult.fromJson(Map<String, dynamic> json) => _$GeocodingResultFromJson(json);

  factory GeocodingResult.fromGeoapifyFeatureJson(Map<String, dynamic> featureJson) {
    final properties = featureJson['properties'] as Map<String, dynamic>? ?? {};
    final geometry = featureJson['geometry'] as Map<String, dynamic>? ?? {};
    final coordinates = geometry['coordinates'] as List<dynamic>? ?? [0.0, 0.0];

    return GeocodingResult(
      latitude: (coordinates[1] as num?)?.toDouble() ?? 0.0,
      longitude: (coordinates[0] as num?)?.toDouble() ?? 0.0,
      displayName: properties['formatted'] as String? ?? '',
      placeId: properties['place_id'] as String?,
      details: GeocodingLocationDetails.fromGeoapifyJson(properties),
      rawData: properties,
    );
  }
}

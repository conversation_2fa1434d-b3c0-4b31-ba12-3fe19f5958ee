import 'package:buscafarma/features/geocoding/domain/entities/geocoding_detail_models.dart';
import 'package:dio/dio.dart'; // Import for CancelToken

abstract class GeocodingRepository {
  /// Geocodes an address string to latitude, longitude, and other details.
  Future<GeocodingResult?> forwardGeocode(String address, {CancelToken? cancelToken});

  /// Geocodes an address using Google Maps URL parsing
  Future<GeocodingResult?> geocodeWithGoogleMaps(String address, {CancelToken? cancelToken});

  /// Reverse geocodes latitude and longitude to get address and location details.
  Future<GeocodingLocationDetails?> reverseGeocode({required double latitude, required double longitude});
}

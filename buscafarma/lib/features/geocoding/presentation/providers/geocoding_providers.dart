import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/providers/dio_provider.dart';
import 'package:buscafarma/features/geocoding/data/repositories/geocoding_repository_impl.dart';
import 'package:buscafarma/features/geocoding/domain/entities/geocoding_detail_models.dart';
import 'package:buscafarma/features/geocoding/domain/repositories/geocoding_repository.dart';

// Provider for GeocodingRepository
final geocodingRepositoryProvider = Provider<GeocodingRepository>((ref) {
  final dio = ref.watch(dioProvider);
  return GeocodingRepositoryImpl(dio);
});

// Provider for forward geocoding an address
final forwardGeocodeProvider = FutureProvider.autoDispose.family<GeocodingResult?, String>((ref, address) async {
  if (address.trim().isEmpty) {
    return null;
  }
  final repository = ref.watch(geocodingRepositoryProvider);
  return repository.forwardGeocode(address);
});

// Provider for reverse geocoding coordinates
// Using a record for the family parameter: ({double latitude, double longitude})
final reverseGeocodeProvider = FutureProvider.autoDispose.family<GeocodingLocationDetails?, ({double latitude, double longitude})>((ref, coords) async {
  final repository = ref.watch(geocodingRepositoryProvider);
  return repository.reverseGeocode(latitude: coords.latitude, longitude: coords.longitude);
});

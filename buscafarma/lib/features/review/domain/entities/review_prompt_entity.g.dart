// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'review_prompt_entity.dart';

// **************************************************************************
// JsonSerializableGenerator
// **************************************************************************

_ReviewPromptState _$ReviewPromptStateFromJson(Map<String, dynamic> json) =>
    _ReviewPromptState(
      sessionCount: (json['sessionCount'] as num?)?.toInt() ?? 0,
      positiveInteractionCount:
          (json['positiveInteractionCount'] as num?)?.toInt() ?? 0,
      interactions:
          (json['interactions'] as List<dynamic>?)
              ?.map(
                (e) => ReviewInteraction.fromJson(e as Map<String, dynamic>),
              )
              .toList() ??
          const [],
      firstInstallDate:
          json['firstInstallDate'] == null
              ? null
              : DateTime.parse(json['firstInstallDate'] as String),
      lastPromptDate:
          json['lastPromptDate'] == null
              ? null
              : DateTime.parse(json['lastPromptDate'] as String),
      lastDismissalDate:
          json['lastDismissalDate'] == null
              ? null
              : DateTime.parse(json['lastDismissalDate'] as String),
      preference:
          $enumDecodeNullable(
            _$ReviewPromptPreferenceEnumMap,
            json['preference'],
          ) ??
          ReviewPromptPreference.enabled,
      hasReviewed: json['hasReviewed'] as bool? ?? false,
    );

Map<String, dynamic> _$ReviewPromptStateToJson(_ReviewPromptState instance) =>
    <String, dynamic>{
      'sessionCount': instance.sessionCount,
      'positiveInteractionCount': instance.positiveInteractionCount,
      'interactions': instance.interactions,
      'firstInstallDate': instance.firstInstallDate?.toIso8601String(),
      'lastPromptDate': instance.lastPromptDate?.toIso8601String(),
      'lastDismissalDate': instance.lastDismissalDate?.toIso8601String(),
      'preference': _$ReviewPromptPreferenceEnumMap[instance.preference]!,
      'hasReviewed': instance.hasReviewed,
    };

const _$ReviewPromptPreferenceEnumMap = {
  ReviewPromptPreference.enabled: 'enabled',
  ReviewPromptPreference.remindLater: 'remind_later',
  ReviewPromptPreference.neverAsk: 'never_ask',
};

_ReviewInteraction _$ReviewInteractionFromJson(Map<String, dynamic> json) =>
    _ReviewInteraction(
      type: $enumDecode(_$ReviewInteractionTypeEnumMap, json['type']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );

Map<String, dynamic> _$ReviewInteractionToJson(_ReviewInteraction instance) =>
    <String, dynamic>{
      'type': _$ReviewInteractionTypeEnumMap[instance.type]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'metadata': instance.metadata,
    };

const _$ReviewInteractionTypeEnumMap = {
  ReviewInteractionType.successfulAuth: 'successful_auth',
  ReviewInteractionType.favoriteAdded: 'favorite_added',
  ReviewInteractionType.successfulSearch: 'successful_search',
  ReviewInteractionType.profileUpdated: 'profile_updated',
  ReviewInteractionType.appSession: 'app_session',
};

_ReviewPromptConfig _$ReviewPromptConfigFromJson(Map<String, dynamic> json) =>
    _ReviewPromptConfig(
      minimumSessionCount: (json['minimumSessionCount'] as num?)?.toInt() ?? 3,
      minimumPositiveInteractions:
          (json['minimumPositiveInteractions'] as num?)?.toInt() ?? 2,
      minimumDaysSinceInstall:
          (json['minimumDaysSinceInstall'] as num?)?.toInt() ?? 2,
      daysBetweenPrompts: (json['daysBetweenPrompts'] as num?)?.toInt() ?? 30,
      daysAfterDismissal: (json['daysAfterDismissal'] as num?)?.toInt() ?? 90,
      daysAfterRemindLater:
          (json['daysAfterRemindLater'] as num?)?.toInt() ?? 7,
    );

Map<String, dynamic> _$ReviewPromptConfigToJson(_ReviewPromptConfig instance) =>
    <String, dynamic>{
      'minimumSessionCount': instance.minimumSessionCount,
      'minimumPositiveInteractions': instance.minimumPositiveInteractions,
      'minimumDaysSinceInstall': instance.minimumDaysSinceInstall,
      'daysBetweenPrompts': instance.daysBetweenPrompts,
      'daysAfterDismissal': instance.daysAfterDismissal,
      'daysAfterRemindLater': instance.daysAfterRemindLater,
    };

_ReviewPromptEligibility _$ReviewPromptEligibilityFromJson(
  Map<String, dynamic> json,
) => _ReviewPromptEligibility(
  isEligible: json['isEligible'] as bool,
  reason: json['reason'] as String,
  config:
      json['config'] == null
          ? null
          : ReviewPromptConfig.fromJson(json['config'] as Map<String, dynamic>),
  currentState:
      json['currentState'] == null
          ? null
          : ReviewPromptState.fromJson(
            json['currentState'] as Map<String, dynamic>,
          ),
);

Map<String, dynamic> _$ReviewPromptEligibilityToJson(
  _ReviewPromptEligibility instance,
) => <String, dynamic>{
  'isEligible': instance.isEligible,
  'reason': instance.reason,
  'config': instance.config,
  'currentState': instance.currentState,
};

_ReviewPromptResult _$ReviewPromptResultFromJson(Map<String, dynamic> json) =>
    _ReviewPromptResult(
      action: $enumDecode(_$ReviewPromptActionEnumMap, json['action']),
      timestamp: DateTime.parse(json['timestamp'] as String),
      errorMessage: json['errorMessage'] as String?,
    );

Map<String, dynamic> _$ReviewPromptResultToJson(_ReviewPromptResult instance) =>
    <String, dynamic>{
      'action': _$ReviewPromptActionEnumMap[instance.action]!,
      'timestamp': instance.timestamp.toIso8601String(),
      'errorMessage': instance.errorMessage,
    };

const _$ReviewPromptActionEnumMap = {
  ReviewPromptAction.reviewed: 'reviewed',
  ReviewPromptAction.dismissed: 'dismissed',
  ReviewPromptAction.remindLater: 'remind_later',
  ReviewPromptAction.neverAsk: 'never_ask',
  ReviewPromptAction.error: 'error',
};

// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'review_prompt_entity.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;

/// @nodoc
mixin _$ReviewPromptState {

 int get sessionCount; int get positiveInteractionCount; List<ReviewInteraction> get interactions; DateTime? get firstInstallDate; DateTime? get lastPromptDate; DateTime? get lastDismissalDate; ReviewPromptPreference get preference; bool get hasReviewed;
/// Create a copy of ReviewPromptState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ReviewPromptStateCopyWith<ReviewPromptState> get copyWith => _$ReviewPromptStateCopyWithImpl<ReviewPromptState>(this as ReviewPromptState, _$identity);

  /// Serializes this ReviewPromptState to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ReviewPromptState&&(identical(other.sessionCount, sessionCount) || other.sessionCount == sessionCount)&&(identical(other.positiveInteractionCount, positiveInteractionCount) || other.positiveInteractionCount == positiveInteractionCount)&&const DeepCollectionEquality().equals(other.interactions, interactions)&&(identical(other.firstInstallDate, firstInstallDate) || other.firstInstallDate == firstInstallDate)&&(identical(other.lastPromptDate, lastPromptDate) || other.lastPromptDate == lastPromptDate)&&(identical(other.lastDismissalDate, lastDismissalDate) || other.lastDismissalDate == lastDismissalDate)&&(identical(other.preference, preference) || other.preference == preference)&&(identical(other.hasReviewed, hasReviewed) || other.hasReviewed == hasReviewed));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sessionCount,positiveInteractionCount,const DeepCollectionEquality().hash(interactions),firstInstallDate,lastPromptDate,lastDismissalDate,preference,hasReviewed);

@override
String toString() {
  return 'ReviewPromptState(sessionCount: $sessionCount, positiveInteractionCount: $positiveInteractionCount, interactions: $interactions, firstInstallDate: $firstInstallDate, lastPromptDate: $lastPromptDate, lastDismissalDate: $lastDismissalDate, preference: $preference, hasReviewed: $hasReviewed)';
}


}

/// @nodoc
abstract mixin class $ReviewPromptStateCopyWith<$Res>  {
  factory $ReviewPromptStateCopyWith(ReviewPromptState value, $Res Function(ReviewPromptState) _then) = _$ReviewPromptStateCopyWithImpl;
@useResult
$Res call({
 int sessionCount, int positiveInteractionCount, List<ReviewInteraction> interactions, DateTime? firstInstallDate, DateTime? lastPromptDate, DateTime? lastDismissalDate, ReviewPromptPreference preference, bool hasReviewed
});




}
/// @nodoc
class _$ReviewPromptStateCopyWithImpl<$Res>
    implements $ReviewPromptStateCopyWith<$Res> {
  _$ReviewPromptStateCopyWithImpl(this._self, this._then);

  final ReviewPromptState _self;
  final $Res Function(ReviewPromptState) _then;

/// Create a copy of ReviewPromptState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? sessionCount = null,Object? positiveInteractionCount = null,Object? interactions = null,Object? firstInstallDate = freezed,Object? lastPromptDate = freezed,Object? lastDismissalDate = freezed,Object? preference = null,Object? hasReviewed = null,}) {
  return _then(_self.copyWith(
sessionCount: null == sessionCount ? _self.sessionCount : sessionCount // ignore: cast_nullable_to_non_nullable
as int,positiveInteractionCount: null == positiveInteractionCount ? _self.positiveInteractionCount : positiveInteractionCount // ignore: cast_nullable_to_non_nullable
as int,interactions: null == interactions ? _self.interactions : interactions // ignore: cast_nullable_to_non_nullable
as List<ReviewInteraction>,firstInstallDate: freezed == firstInstallDate ? _self.firstInstallDate : firstInstallDate // ignore: cast_nullable_to_non_nullable
as DateTime?,lastPromptDate: freezed == lastPromptDate ? _self.lastPromptDate : lastPromptDate // ignore: cast_nullable_to_non_nullable
as DateTime?,lastDismissalDate: freezed == lastDismissalDate ? _self.lastDismissalDate : lastDismissalDate // ignore: cast_nullable_to_non_nullable
as DateTime?,preference: null == preference ? _self.preference : preference // ignore: cast_nullable_to_non_nullable
as ReviewPromptPreference,hasReviewed: null == hasReviewed ? _self.hasReviewed : hasReviewed // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ReviewPromptState implements ReviewPromptState {
  const _ReviewPromptState({this.sessionCount = 0, this.positiveInteractionCount = 0, final  List<ReviewInteraction> interactions = const [], this.firstInstallDate, this.lastPromptDate, this.lastDismissalDate, this.preference = ReviewPromptPreference.enabled, this.hasReviewed = false}): _interactions = interactions;
  factory _ReviewPromptState.fromJson(Map<String, dynamic> json) => _$ReviewPromptStateFromJson(json);

@override@JsonKey() final  int sessionCount;
@override@JsonKey() final  int positiveInteractionCount;
 final  List<ReviewInteraction> _interactions;
@override@JsonKey() List<ReviewInteraction> get interactions {
  if (_interactions is EqualUnmodifiableListView) return _interactions;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableListView(_interactions);
}

@override final  DateTime? firstInstallDate;
@override final  DateTime? lastPromptDate;
@override final  DateTime? lastDismissalDate;
@override@JsonKey() final  ReviewPromptPreference preference;
@override@JsonKey() final  bool hasReviewed;

/// Create a copy of ReviewPromptState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ReviewPromptStateCopyWith<_ReviewPromptState> get copyWith => __$ReviewPromptStateCopyWithImpl<_ReviewPromptState>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ReviewPromptStateToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ReviewPromptState&&(identical(other.sessionCount, sessionCount) || other.sessionCount == sessionCount)&&(identical(other.positiveInteractionCount, positiveInteractionCount) || other.positiveInteractionCount == positiveInteractionCount)&&const DeepCollectionEquality().equals(other._interactions, _interactions)&&(identical(other.firstInstallDate, firstInstallDate) || other.firstInstallDate == firstInstallDate)&&(identical(other.lastPromptDate, lastPromptDate) || other.lastPromptDate == lastPromptDate)&&(identical(other.lastDismissalDate, lastDismissalDate) || other.lastDismissalDate == lastDismissalDate)&&(identical(other.preference, preference) || other.preference == preference)&&(identical(other.hasReviewed, hasReviewed) || other.hasReviewed == hasReviewed));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,sessionCount,positiveInteractionCount,const DeepCollectionEquality().hash(_interactions),firstInstallDate,lastPromptDate,lastDismissalDate,preference,hasReviewed);

@override
String toString() {
  return 'ReviewPromptState(sessionCount: $sessionCount, positiveInteractionCount: $positiveInteractionCount, interactions: $interactions, firstInstallDate: $firstInstallDate, lastPromptDate: $lastPromptDate, lastDismissalDate: $lastDismissalDate, preference: $preference, hasReviewed: $hasReviewed)';
}


}

/// @nodoc
abstract mixin class _$ReviewPromptStateCopyWith<$Res> implements $ReviewPromptStateCopyWith<$Res> {
  factory _$ReviewPromptStateCopyWith(_ReviewPromptState value, $Res Function(_ReviewPromptState) _then) = __$ReviewPromptStateCopyWithImpl;
@override @useResult
$Res call({
 int sessionCount, int positiveInteractionCount, List<ReviewInteraction> interactions, DateTime? firstInstallDate, DateTime? lastPromptDate, DateTime? lastDismissalDate, ReviewPromptPreference preference, bool hasReviewed
});




}
/// @nodoc
class __$ReviewPromptStateCopyWithImpl<$Res>
    implements _$ReviewPromptStateCopyWith<$Res> {
  __$ReviewPromptStateCopyWithImpl(this._self, this._then);

  final _ReviewPromptState _self;
  final $Res Function(_ReviewPromptState) _then;

/// Create a copy of ReviewPromptState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? sessionCount = null,Object? positiveInteractionCount = null,Object? interactions = null,Object? firstInstallDate = freezed,Object? lastPromptDate = freezed,Object? lastDismissalDate = freezed,Object? preference = null,Object? hasReviewed = null,}) {
  return _then(_ReviewPromptState(
sessionCount: null == sessionCount ? _self.sessionCount : sessionCount // ignore: cast_nullable_to_non_nullable
as int,positiveInteractionCount: null == positiveInteractionCount ? _self.positiveInteractionCount : positiveInteractionCount // ignore: cast_nullable_to_non_nullable
as int,interactions: null == interactions ? _self._interactions : interactions // ignore: cast_nullable_to_non_nullable
as List<ReviewInteraction>,firstInstallDate: freezed == firstInstallDate ? _self.firstInstallDate : firstInstallDate // ignore: cast_nullable_to_non_nullable
as DateTime?,lastPromptDate: freezed == lastPromptDate ? _self.lastPromptDate : lastPromptDate // ignore: cast_nullable_to_non_nullable
as DateTime?,lastDismissalDate: freezed == lastDismissalDate ? _self.lastDismissalDate : lastDismissalDate // ignore: cast_nullable_to_non_nullable
as DateTime?,preference: null == preference ? _self.preference : preference // ignore: cast_nullable_to_non_nullable
as ReviewPromptPreference,hasReviewed: null == hasReviewed ? _self.hasReviewed : hasReviewed // ignore: cast_nullable_to_non_nullable
as bool,
  ));
}


}


/// @nodoc
mixin _$ReviewInteraction {

 ReviewInteractionType get type; DateTime get timestamp; Map<String, dynamic>? get metadata;
/// Create a copy of ReviewInteraction
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ReviewInteractionCopyWith<ReviewInteraction> get copyWith => _$ReviewInteractionCopyWithImpl<ReviewInteraction>(this as ReviewInteraction, _$identity);

  /// Serializes this ReviewInteraction to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ReviewInteraction&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,timestamp,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'ReviewInteraction(type: $type, timestamp: $timestamp, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $ReviewInteractionCopyWith<$Res>  {
  factory $ReviewInteractionCopyWith(ReviewInteraction value, $Res Function(ReviewInteraction) _then) = _$ReviewInteractionCopyWithImpl;
@useResult
$Res call({
 ReviewInteractionType type, DateTime timestamp, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$ReviewInteractionCopyWithImpl<$Res>
    implements $ReviewInteractionCopyWith<$Res> {
  _$ReviewInteractionCopyWithImpl(this._self, this._then);

  final ReviewInteraction _self;
  final $Res Function(ReviewInteraction) _then;

/// Create a copy of ReviewInteraction
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? type = null,Object? timestamp = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ReviewInteractionType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ReviewInteraction implements ReviewInteraction {
  const _ReviewInteraction({required this.type, required this.timestamp, final  Map<String, dynamic>? metadata}): _metadata = metadata;
  factory _ReviewInteraction.fromJson(Map<String, dynamic> json) => _$ReviewInteractionFromJson(json);

@override final  ReviewInteractionType type;
@override final  DateTime timestamp;
 final  Map<String, dynamic>? _metadata;
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of ReviewInteraction
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ReviewInteractionCopyWith<_ReviewInteraction> get copyWith => __$ReviewInteractionCopyWithImpl<_ReviewInteraction>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ReviewInteractionToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ReviewInteraction&&(identical(other.type, type) || other.type == type)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,type,timestamp,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'ReviewInteraction(type: $type, timestamp: $timestamp, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$ReviewInteractionCopyWith<$Res> implements $ReviewInteractionCopyWith<$Res> {
  factory _$ReviewInteractionCopyWith(_ReviewInteraction value, $Res Function(_ReviewInteraction) _then) = __$ReviewInteractionCopyWithImpl;
@override @useResult
$Res call({
 ReviewInteractionType type, DateTime timestamp, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$ReviewInteractionCopyWithImpl<$Res>
    implements _$ReviewInteractionCopyWith<$Res> {
  __$ReviewInteractionCopyWithImpl(this._self, this._then);

  final _ReviewInteraction _self;
  final $Res Function(_ReviewInteraction) _then;

/// Create a copy of ReviewInteraction
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? type = null,Object? timestamp = null,Object? metadata = freezed,}) {
  return _then(_ReviewInteraction(
type: null == type ? _self.type : type // ignore: cast_nullable_to_non_nullable
as ReviewInteractionType,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}


/// @nodoc
mixin _$ReviewPromptConfig {

 int get minimumSessionCount; int get minimumPositiveInteractions; int get minimumDaysSinceInstall; int get daysBetweenPrompts; int get daysAfterDismissal; int get daysAfterRemindLater;
/// Create a copy of ReviewPromptConfig
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ReviewPromptConfigCopyWith<ReviewPromptConfig> get copyWith => _$ReviewPromptConfigCopyWithImpl<ReviewPromptConfig>(this as ReviewPromptConfig, _$identity);

  /// Serializes this ReviewPromptConfig to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ReviewPromptConfig&&(identical(other.minimumSessionCount, minimumSessionCount) || other.minimumSessionCount == minimumSessionCount)&&(identical(other.minimumPositiveInteractions, minimumPositiveInteractions) || other.minimumPositiveInteractions == minimumPositiveInteractions)&&(identical(other.minimumDaysSinceInstall, minimumDaysSinceInstall) || other.minimumDaysSinceInstall == minimumDaysSinceInstall)&&(identical(other.daysBetweenPrompts, daysBetweenPrompts) || other.daysBetweenPrompts == daysBetweenPrompts)&&(identical(other.daysAfterDismissal, daysAfterDismissal) || other.daysAfterDismissal == daysAfterDismissal)&&(identical(other.daysAfterRemindLater, daysAfterRemindLater) || other.daysAfterRemindLater == daysAfterRemindLater));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,minimumSessionCount,minimumPositiveInteractions,minimumDaysSinceInstall,daysBetweenPrompts,daysAfterDismissal,daysAfterRemindLater);

@override
String toString() {
  return 'ReviewPromptConfig(minimumSessionCount: $minimumSessionCount, minimumPositiveInteractions: $minimumPositiveInteractions, minimumDaysSinceInstall: $minimumDaysSinceInstall, daysBetweenPrompts: $daysBetweenPrompts, daysAfterDismissal: $daysAfterDismissal, daysAfterRemindLater: $daysAfterRemindLater)';
}


}

/// @nodoc
abstract mixin class $ReviewPromptConfigCopyWith<$Res>  {
  factory $ReviewPromptConfigCopyWith(ReviewPromptConfig value, $Res Function(ReviewPromptConfig) _then) = _$ReviewPromptConfigCopyWithImpl;
@useResult
$Res call({
 int minimumSessionCount, int minimumPositiveInteractions, int minimumDaysSinceInstall, int daysBetweenPrompts, int daysAfterDismissal, int daysAfterRemindLater
});




}
/// @nodoc
class _$ReviewPromptConfigCopyWithImpl<$Res>
    implements $ReviewPromptConfigCopyWith<$Res> {
  _$ReviewPromptConfigCopyWithImpl(this._self, this._then);

  final ReviewPromptConfig _self;
  final $Res Function(ReviewPromptConfig) _then;

/// Create a copy of ReviewPromptConfig
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? minimumSessionCount = null,Object? minimumPositiveInteractions = null,Object? minimumDaysSinceInstall = null,Object? daysBetweenPrompts = null,Object? daysAfterDismissal = null,Object? daysAfterRemindLater = null,}) {
  return _then(_self.copyWith(
minimumSessionCount: null == minimumSessionCount ? _self.minimumSessionCount : minimumSessionCount // ignore: cast_nullable_to_non_nullable
as int,minimumPositiveInteractions: null == minimumPositiveInteractions ? _self.minimumPositiveInteractions : minimumPositiveInteractions // ignore: cast_nullable_to_non_nullable
as int,minimumDaysSinceInstall: null == minimumDaysSinceInstall ? _self.minimumDaysSinceInstall : minimumDaysSinceInstall // ignore: cast_nullable_to_non_nullable
as int,daysBetweenPrompts: null == daysBetweenPrompts ? _self.daysBetweenPrompts : daysBetweenPrompts // ignore: cast_nullable_to_non_nullable
as int,daysAfterDismissal: null == daysAfterDismissal ? _self.daysAfterDismissal : daysAfterDismissal // ignore: cast_nullable_to_non_nullable
as int,daysAfterRemindLater: null == daysAfterRemindLater ? _self.daysAfterRemindLater : daysAfterRemindLater // ignore: cast_nullable_to_non_nullable
as int,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ReviewPromptConfig implements ReviewPromptConfig {
  const _ReviewPromptConfig({this.minimumSessionCount = 3, this.minimumPositiveInteractions = 2, this.minimumDaysSinceInstall = 2, this.daysBetweenPrompts = 30, this.daysAfterDismissal = 90, this.daysAfterRemindLater = 7});
  factory _ReviewPromptConfig.fromJson(Map<String, dynamic> json) => _$ReviewPromptConfigFromJson(json);

@override@JsonKey() final  int minimumSessionCount;
@override@JsonKey() final  int minimumPositiveInteractions;
@override@JsonKey() final  int minimumDaysSinceInstall;
@override@JsonKey() final  int daysBetweenPrompts;
@override@JsonKey() final  int daysAfterDismissal;
@override@JsonKey() final  int daysAfterRemindLater;

/// Create a copy of ReviewPromptConfig
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ReviewPromptConfigCopyWith<_ReviewPromptConfig> get copyWith => __$ReviewPromptConfigCopyWithImpl<_ReviewPromptConfig>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ReviewPromptConfigToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ReviewPromptConfig&&(identical(other.minimumSessionCount, minimumSessionCount) || other.minimumSessionCount == minimumSessionCount)&&(identical(other.minimumPositiveInteractions, minimumPositiveInteractions) || other.minimumPositiveInteractions == minimumPositiveInteractions)&&(identical(other.minimumDaysSinceInstall, minimumDaysSinceInstall) || other.minimumDaysSinceInstall == minimumDaysSinceInstall)&&(identical(other.daysBetweenPrompts, daysBetweenPrompts) || other.daysBetweenPrompts == daysBetweenPrompts)&&(identical(other.daysAfterDismissal, daysAfterDismissal) || other.daysAfterDismissal == daysAfterDismissal)&&(identical(other.daysAfterRemindLater, daysAfterRemindLater) || other.daysAfterRemindLater == daysAfterRemindLater));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,minimumSessionCount,minimumPositiveInteractions,minimumDaysSinceInstall,daysBetweenPrompts,daysAfterDismissal,daysAfterRemindLater);

@override
String toString() {
  return 'ReviewPromptConfig(minimumSessionCount: $minimumSessionCount, minimumPositiveInteractions: $minimumPositiveInteractions, minimumDaysSinceInstall: $minimumDaysSinceInstall, daysBetweenPrompts: $daysBetweenPrompts, daysAfterDismissal: $daysAfterDismissal, daysAfterRemindLater: $daysAfterRemindLater)';
}


}

/// @nodoc
abstract mixin class _$ReviewPromptConfigCopyWith<$Res> implements $ReviewPromptConfigCopyWith<$Res> {
  factory _$ReviewPromptConfigCopyWith(_ReviewPromptConfig value, $Res Function(_ReviewPromptConfig) _then) = __$ReviewPromptConfigCopyWithImpl;
@override @useResult
$Res call({
 int minimumSessionCount, int minimumPositiveInteractions, int minimumDaysSinceInstall, int daysBetweenPrompts, int daysAfterDismissal, int daysAfterRemindLater
});




}
/// @nodoc
class __$ReviewPromptConfigCopyWithImpl<$Res>
    implements _$ReviewPromptConfigCopyWith<$Res> {
  __$ReviewPromptConfigCopyWithImpl(this._self, this._then);

  final _ReviewPromptConfig _self;
  final $Res Function(_ReviewPromptConfig) _then;

/// Create a copy of ReviewPromptConfig
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? minimumSessionCount = null,Object? minimumPositiveInteractions = null,Object? minimumDaysSinceInstall = null,Object? daysBetweenPrompts = null,Object? daysAfterDismissal = null,Object? daysAfterRemindLater = null,}) {
  return _then(_ReviewPromptConfig(
minimumSessionCount: null == minimumSessionCount ? _self.minimumSessionCount : minimumSessionCount // ignore: cast_nullable_to_non_nullable
as int,minimumPositiveInteractions: null == minimumPositiveInteractions ? _self.minimumPositiveInteractions : minimumPositiveInteractions // ignore: cast_nullable_to_non_nullable
as int,minimumDaysSinceInstall: null == minimumDaysSinceInstall ? _self.minimumDaysSinceInstall : minimumDaysSinceInstall // ignore: cast_nullable_to_non_nullable
as int,daysBetweenPrompts: null == daysBetweenPrompts ? _self.daysBetweenPrompts : daysBetweenPrompts // ignore: cast_nullable_to_non_nullable
as int,daysAfterDismissal: null == daysAfterDismissal ? _self.daysAfterDismissal : daysAfterDismissal // ignore: cast_nullable_to_non_nullable
as int,daysAfterRemindLater: null == daysAfterRemindLater ? _self.daysAfterRemindLater : daysAfterRemindLater // ignore: cast_nullable_to_non_nullable
as int,
  ));
}


}


/// @nodoc
mixin _$ReviewPromptEligibility {

 bool get isEligible; String get reason; ReviewPromptConfig? get config; ReviewPromptState? get currentState;
/// Create a copy of ReviewPromptEligibility
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ReviewPromptEligibilityCopyWith<ReviewPromptEligibility> get copyWith => _$ReviewPromptEligibilityCopyWithImpl<ReviewPromptEligibility>(this as ReviewPromptEligibility, _$identity);

  /// Serializes this ReviewPromptEligibility to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ReviewPromptEligibility&&(identical(other.isEligible, isEligible) || other.isEligible == isEligible)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.config, config) || other.config == config)&&(identical(other.currentState, currentState) || other.currentState == currentState));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isEligible,reason,config,currentState);

@override
String toString() {
  return 'ReviewPromptEligibility(isEligible: $isEligible, reason: $reason, config: $config, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class $ReviewPromptEligibilityCopyWith<$Res>  {
  factory $ReviewPromptEligibilityCopyWith(ReviewPromptEligibility value, $Res Function(ReviewPromptEligibility) _then) = _$ReviewPromptEligibilityCopyWithImpl;
@useResult
$Res call({
 bool isEligible, String reason, ReviewPromptConfig? config, ReviewPromptState? currentState
});


$ReviewPromptConfigCopyWith<$Res>? get config;$ReviewPromptStateCopyWith<$Res>? get currentState;

}
/// @nodoc
class _$ReviewPromptEligibilityCopyWithImpl<$Res>
    implements $ReviewPromptEligibilityCopyWith<$Res> {
  _$ReviewPromptEligibilityCopyWithImpl(this._self, this._then);

  final ReviewPromptEligibility _self;
  final $Res Function(ReviewPromptEligibility) _then;

/// Create a copy of ReviewPromptEligibility
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isEligible = null,Object? reason = null,Object? config = freezed,Object? currentState = freezed,}) {
  return _then(_self.copyWith(
isEligible: null == isEligible ? _self.isEligible : isEligible // ignore: cast_nullable_to_non_nullable
as bool,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,config: freezed == config ? _self.config : config // ignore: cast_nullable_to_non_nullable
as ReviewPromptConfig?,currentState: freezed == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as ReviewPromptState?,
  ));
}
/// Create a copy of ReviewPromptEligibility
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ReviewPromptConfigCopyWith<$Res>? get config {
    if (_self.config == null) {
    return null;
  }

  return $ReviewPromptConfigCopyWith<$Res>(_self.config!, (value) {
    return _then(_self.copyWith(config: value));
  });
}/// Create a copy of ReviewPromptEligibility
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ReviewPromptStateCopyWith<$Res>? get currentState {
    if (_self.currentState == null) {
    return null;
  }

  return $ReviewPromptStateCopyWith<$Res>(_self.currentState!, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}


/// @nodoc
@JsonSerializable()

class _ReviewPromptEligibility implements ReviewPromptEligibility {
  const _ReviewPromptEligibility({required this.isEligible, required this.reason, this.config, this.currentState});
  factory _ReviewPromptEligibility.fromJson(Map<String, dynamic> json) => _$ReviewPromptEligibilityFromJson(json);

@override final  bool isEligible;
@override final  String reason;
@override final  ReviewPromptConfig? config;
@override final  ReviewPromptState? currentState;

/// Create a copy of ReviewPromptEligibility
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ReviewPromptEligibilityCopyWith<_ReviewPromptEligibility> get copyWith => __$ReviewPromptEligibilityCopyWithImpl<_ReviewPromptEligibility>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ReviewPromptEligibilityToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ReviewPromptEligibility&&(identical(other.isEligible, isEligible) || other.isEligible == isEligible)&&(identical(other.reason, reason) || other.reason == reason)&&(identical(other.config, config) || other.config == config)&&(identical(other.currentState, currentState) || other.currentState == currentState));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,isEligible,reason,config,currentState);

@override
String toString() {
  return 'ReviewPromptEligibility(isEligible: $isEligible, reason: $reason, config: $config, currentState: $currentState)';
}


}

/// @nodoc
abstract mixin class _$ReviewPromptEligibilityCopyWith<$Res> implements $ReviewPromptEligibilityCopyWith<$Res> {
  factory _$ReviewPromptEligibilityCopyWith(_ReviewPromptEligibility value, $Res Function(_ReviewPromptEligibility) _then) = __$ReviewPromptEligibilityCopyWithImpl;
@override @useResult
$Res call({
 bool isEligible, String reason, ReviewPromptConfig? config, ReviewPromptState? currentState
});


@override $ReviewPromptConfigCopyWith<$Res>? get config;@override $ReviewPromptStateCopyWith<$Res>? get currentState;

}
/// @nodoc
class __$ReviewPromptEligibilityCopyWithImpl<$Res>
    implements _$ReviewPromptEligibilityCopyWith<$Res> {
  __$ReviewPromptEligibilityCopyWithImpl(this._self, this._then);

  final _ReviewPromptEligibility _self;
  final $Res Function(_ReviewPromptEligibility) _then;

/// Create a copy of ReviewPromptEligibility
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isEligible = null,Object? reason = null,Object? config = freezed,Object? currentState = freezed,}) {
  return _then(_ReviewPromptEligibility(
isEligible: null == isEligible ? _self.isEligible : isEligible // ignore: cast_nullable_to_non_nullable
as bool,reason: null == reason ? _self.reason : reason // ignore: cast_nullable_to_non_nullable
as String,config: freezed == config ? _self.config : config // ignore: cast_nullable_to_non_nullable
as ReviewPromptConfig?,currentState: freezed == currentState ? _self.currentState : currentState // ignore: cast_nullable_to_non_nullable
as ReviewPromptState?,
  ));
}

/// Create a copy of ReviewPromptEligibility
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ReviewPromptConfigCopyWith<$Res>? get config {
    if (_self.config == null) {
    return null;
  }

  return $ReviewPromptConfigCopyWith<$Res>(_self.config!, (value) {
    return _then(_self.copyWith(config: value));
  });
}/// Create a copy of ReviewPromptEligibility
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$ReviewPromptStateCopyWith<$Res>? get currentState {
    if (_self.currentState == null) {
    return null;
  }

  return $ReviewPromptStateCopyWith<$Res>(_self.currentState!, (value) {
    return _then(_self.copyWith(currentState: value));
  });
}
}


/// @nodoc
mixin _$ReviewPromptResult {

 ReviewPromptAction get action; DateTime get timestamp; String? get errorMessage;
/// Create a copy of ReviewPromptResult
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ReviewPromptResultCopyWith<ReviewPromptResult> get copyWith => _$ReviewPromptResultCopyWithImpl<ReviewPromptResult>(this as ReviewPromptResult, _$identity);

  /// Serializes this ReviewPromptResult to a JSON map.
  Map<String, dynamic> toJson();


@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ReviewPromptResult&&(identical(other.action, action) || other.action == action)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,action,timestamp,errorMessage);

@override
String toString() {
  return 'ReviewPromptResult(action: $action, timestamp: $timestamp, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class $ReviewPromptResultCopyWith<$Res>  {
  factory $ReviewPromptResultCopyWith(ReviewPromptResult value, $Res Function(ReviewPromptResult) _then) = _$ReviewPromptResultCopyWithImpl;
@useResult
$Res call({
 ReviewPromptAction action, DateTime timestamp, String? errorMessage
});




}
/// @nodoc
class _$ReviewPromptResultCopyWithImpl<$Res>
    implements $ReviewPromptResultCopyWith<$Res> {
  _$ReviewPromptResultCopyWithImpl(this._self, this._then);

  final ReviewPromptResult _self;
  final $Res Function(ReviewPromptResult) _then;

/// Create a copy of ReviewPromptResult
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? action = null,Object? timestamp = null,Object? errorMessage = freezed,}) {
  return _then(_self.copyWith(
action: null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as ReviewPromptAction,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}

}


/// @nodoc
@JsonSerializable()

class _ReviewPromptResult implements ReviewPromptResult {
  const _ReviewPromptResult({required this.action, required this.timestamp, this.errorMessage});
  factory _ReviewPromptResult.fromJson(Map<String, dynamic> json) => _$ReviewPromptResultFromJson(json);

@override final  ReviewPromptAction action;
@override final  DateTime timestamp;
@override final  String? errorMessage;

/// Create a copy of ReviewPromptResult
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$ReviewPromptResultCopyWith<_ReviewPromptResult> get copyWith => __$ReviewPromptResultCopyWithImpl<_ReviewPromptResult>(this, _$identity);

@override
Map<String, dynamic> toJson() {
  return _$ReviewPromptResultToJson(this, );
}

@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _ReviewPromptResult&&(identical(other.action, action) || other.action == action)&&(identical(other.timestamp, timestamp) || other.timestamp == timestamp)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage));
}

@JsonKey(includeFromJson: false, includeToJson: false)
@override
int get hashCode => Object.hash(runtimeType,action,timestamp,errorMessage);

@override
String toString() {
  return 'ReviewPromptResult(action: $action, timestamp: $timestamp, errorMessage: $errorMessage)';
}


}

/// @nodoc
abstract mixin class _$ReviewPromptResultCopyWith<$Res> implements $ReviewPromptResultCopyWith<$Res> {
  factory _$ReviewPromptResultCopyWith(_ReviewPromptResult value, $Res Function(_ReviewPromptResult) _then) = __$ReviewPromptResultCopyWithImpl;
@override @useResult
$Res call({
 ReviewPromptAction action, DateTime timestamp, String? errorMessage
});




}
/// @nodoc
class __$ReviewPromptResultCopyWithImpl<$Res>
    implements _$ReviewPromptResultCopyWith<$Res> {
  __$ReviewPromptResultCopyWithImpl(this._self, this._then);

  final _ReviewPromptResult _self;
  final $Res Function(_ReviewPromptResult) _then;

/// Create a copy of ReviewPromptResult
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? action = null,Object? timestamp = null,Object? errorMessage = freezed,}) {
  return _then(_ReviewPromptResult(
action: null == action ? _self.action : action // ignore: cast_nullable_to_non_nullable
as ReviewPromptAction,timestamp: null == timestamp ? _self.timestamp : timestamp // ignore: cast_nullable_to_non_nullable
as DateTime,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on

import 'package:freezed_annotation/freezed_annotation.dart';

part 'review_prompt_entity.freezed.dart';
part 'review_prompt_entity.g.dart';

/// Represents the current state of review prompts for the user
@freezed
abstract class ReviewPromptState with _$ReviewPromptState {
  const factory ReviewPromptState({
    @Default(0) int sessionCount,
    @Default(0) int positiveInteractionCount,
    @Default([]) List<ReviewInteraction> interactions,
    DateTime? firstInstallDate,
    DateTime? lastPromptDate,
    DateTime? lastDismissalDate,
    @Default(ReviewPromptPreference.enabled) ReviewPromptPreference preference,
    @Default(false) bool hasReviewed,
  }) = _ReviewPromptState;

  factory ReviewPromptState.fromJson(Map<String, dynamic> json) => _$ReviewPromptStateFromJson(json);
}

/// Represents a user interaction that could contribute to review prompt eligibility
@freezed
abstract class ReviewInteraction with _$ReviewInteraction {
  const factory ReviewInteraction({
    required ReviewInteractionType type,
    required DateTime timestamp,
    Map<String, dynamic>? metadata,
  }) = _ReviewInteraction;

  factory ReviewInteraction.fromJson(Map<String, dynamic> json) => _$ReviewInteractionFromJson(json);
}

/// Types of user interactions that are considered positive for review prompts
enum ReviewInteractionType {
  @JsonValue('successful_auth')
  successfulAuth,
  @JsonValue('favorite_added')
  favoriteAdded,
  @JsonValue('successful_search')
  successfulSearch,
  @JsonValue('profile_updated')
  profileUpdated,
  @JsonValue('app_session')
  appSession,
}

/// User preference for review prompts
enum ReviewPromptPreference {
  @JsonValue('enabled')
  enabled,
  @JsonValue('remind_later')
  remindLater,
  @JsonValue('never_ask')
  neverAsk,
}

/// Configuration for review prompt timing and thresholds
@freezed
abstract class ReviewPromptConfig with _$ReviewPromptConfig {
  const factory ReviewPromptConfig({
    @Default(3) int minimumSessionCount,
    @Default(2) int minimumPositiveInteractions,
    @Default(2) int minimumDaysSinceInstall,
    @Default(30) int daysBetweenPrompts,
    @Default(90) int daysAfterDismissal,
    @Default(7) int daysAfterRemindLater,
  }) = _ReviewPromptConfig;

  factory ReviewPromptConfig.fromJson(Map<String, dynamic> json) => _$ReviewPromptConfigFromJson(json);
}

/// Result of a review prompt eligibility check
@freezed
abstract class ReviewPromptEligibility with _$ReviewPromptEligibility {
  const factory ReviewPromptEligibility({
    required bool isEligible,
    required String reason,
    ReviewPromptConfig? config,
    ReviewPromptState? currentState,
  }) = _ReviewPromptEligibility;

  factory ReviewPromptEligibility.fromJson(Map<String, dynamic> json) => _$ReviewPromptEligibilityFromJson(json);
}

/// Result of showing a review prompt to the user
@freezed
abstract class ReviewPromptResult with _$ReviewPromptResult {
  const factory ReviewPromptResult({
    required ReviewPromptAction action,
    required DateTime timestamp,
    String? errorMessage,
  }) = _ReviewPromptResult;

  factory ReviewPromptResult.fromJson(Map<String, dynamic> json) => _$ReviewPromptResultFromJson(json);
}

/// Actions that can be taken on a review prompt
enum ReviewPromptAction {
  @JsonValue('reviewed')
  reviewed,
  @JsonValue('dismissed')
  dismissed,
  @JsonValue('remind_later')
  remindLater,
  @JsonValue('never_ask')
  neverAsk,
  @JsonValue('error')
  error,
}

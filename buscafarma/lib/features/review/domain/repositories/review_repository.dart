import 'package:buscafarma/features/review/domain/entities/review_prompt_entity.dart';

/// Repository interface for managing review prompt data and user interactions
abstract class ReviewRepository {
  /// Gets the current review prompt state for the user
  Future<ReviewPromptState> getReviewPromptState();

  /// Saves the review prompt state
  Future<void> saveReviewPromptState(ReviewPromptState state);

  /// Records a positive user interaction
  Future<void> recordInteraction(ReviewInteractionType type, {Map<String, dynamic>? metadata});

  /// Increments the session count
  Future<void> incrementSessionCount();

  /// Checks if the user is eligible for a review prompt
  Future<ReviewPromptEligibility> checkEligibility();

  /// Records that a review prompt was shown to the user
  Future<void> recordPromptShown();

  /// Records the user's response to a review prompt
  Future<void> recordPromptResult(ReviewPromptResult result);

  /// Sets the user's review prompt preference
  Future<void> setReviewPromptPreference(ReviewPromptPreference preference);

  /// Marks that the user has already reviewed the app
  Future<void> markAsReviewed();

  /// Clears all review prompt data (for testing or reset purposes)
  Future<void> clearReviewData();

  /// Gets the review prompt configuration
  Future<ReviewPromptConfig> getConfig();

  /// Updates the review prompt configuration
  Future<void> updateConfig(ReviewPromptConfig config);
}

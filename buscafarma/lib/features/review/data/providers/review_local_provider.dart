import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:buscafarma/features/review/domain/entities/review_prompt_entity.dart';

/// Provider for handling review-related local storage operations
class ReviewLocalProvider {
  static const String _reviewPromptStateKey = 'review_prompt_state';
  static const String _reviewConfigKey = 'review_config';
  static const String _firstInstallDateKey = 'first_install_date';

  /// Saves the review prompt state to local storage
  Future<void> saveReviewPromptState(ReviewPromptState state) async {
    final prefs = await SharedPreferences.getInstance();
    final stateJson = jsonEncode(state.toJson());
    await prefs.setString(_reviewPromptStateKey, stateJson);
  }

  /// Gets the review prompt state from local storage
  Future<ReviewPromptState?> getReviewPromptState() async {
    final prefs = await SharedPreferences.getInstance();
    final stateString = prefs.getString(_reviewPromptStateKey);

    if (stateString == null) {
      return null;
    }

    try {
      final stateJson = jsonDecode(stateString) as Map<String, dynamic>;
      return ReviewPromptState.fromJson(stateJson);
    } catch (e) {
      // If there's an error parsing, return null to use default state
      return null;
    }
  }

  /// Saves the review prompt configuration to local storage
  Future<void> saveReviewConfig(ReviewPromptConfig config) async {
    final prefs = await SharedPreferences.getInstance();
    final configJson = jsonEncode(config.toJson());
    await prefs.setString(_reviewConfigKey, configJson);
  }

  /// Gets the review prompt configuration from local storage
  Future<ReviewPromptConfig?> getReviewConfig() async {
    final prefs = await SharedPreferences.getInstance();
    final configString = prefs.getString(_reviewConfigKey);

    if (configString == null) {
      return null;
    }

    try {
      final configJson = jsonDecode(configString) as Map<String, dynamic>;
      return ReviewPromptConfig.fromJson(configJson);
    } catch (e) {
      // If there's an error parsing, return null to use default config
      return null;
    }
  }

  /// Sets the first install date if not already set
  Future<void> setFirstInstallDateIfNotExists() async {
    final prefs = await SharedPreferences.getInstance();
    final existingDate = prefs.getString(_firstInstallDateKey);

    if (existingDate == null) {
      final now = DateTime.now();
      await prefs.setString(_firstInstallDateKey, now.toIso8601String());
    }
  }

  /// Gets the first install date
  Future<DateTime?> getFirstInstallDate() async {
    final prefs = await SharedPreferences.getInstance();
    final dateString = prefs.getString(_firstInstallDateKey);

    if (dateString == null) {
      return null;
    }

    try {
      return DateTime.parse(dateString);
    } catch (e) {
      return null;
    }
  }

  /// Clears all review-related data from local storage
  Future<void> clearReviewData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_reviewPromptStateKey);
    await prefs.remove(_reviewConfigKey);
    // Note: We don't remove first install date as it should persist
  }

  /// Increments the session count in the stored state
  Future<void> incrementSessionCount() async {
    final currentState = await getReviewPromptState() ?? const ReviewPromptState();
    final updatedState = currentState.copyWith(
      sessionCount: currentState.sessionCount + 1,
    );
    await saveReviewPromptState(updatedState);
  }

  /// Adds a new interaction to the stored state
  Future<void> addInteraction(ReviewInteraction interaction) async {
    final currentState = await getReviewPromptState() ?? const ReviewPromptState();
    final updatedInteractions = [...currentState.interactions, interaction];
    
    // Keep only the last 50 interactions to prevent unlimited growth
    final limitedInteractions = updatedInteractions.length > 50
        ? updatedInteractions.sublist(updatedInteractions.length - 50)
        : updatedInteractions;

    // Count positive interactions
    final positiveCount = limitedInteractions
        .where((interaction) => _isPositiveInteraction(interaction.type))
        .length;

    final updatedState = currentState.copyWith(
      interactions: limitedInteractions,
      positiveInteractionCount: positiveCount,
    );
    await saveReviewPromptState(updatedState);
  }

  /// Updates the review prompt preference
  Future<void> updateReviewPromptPreference(ReviewPromptPreference preference) async {
    final currentState = await getReviewPromptState() ?? const ReviewPromptState();
    final updatedState = currentState.copyWith(preference: preference);
    await saveReviewPromptState(updatedState);
  }

  /// Records that a prompt was shown
  Future<void> recordPromptShown() async {
    final currentState = await getReviewPromptState() ?? const ReviewPromptState();
    final updatedState = currentState.copyWith(lastPromptDate: DateTime.now());
    await saveReviewPromptState(updatedState);
  }

  /// Records that a prompt was dismissed
  Future<void> recordPromptDismissed() async {
    final currentState = await getReviewPromptState() ?? const ReviewPromptState();
    final updatedState = currentState.copyWith(lastDismissalDate: DateTime.now());
    await saveReviewPromptState(updatedState);
  }

  /// Marks that the user has reviewed the app
  Future<void> markAsReviewed() async {
    final currentState = await getReviewPromptState() ?? const ReviewPromptState();
    final updatedState = currentState.copyWith(hasReviewed: true);
    await saveReviewPromptState(updatedState);
  }

  /// Helper method to determine if an interaction type is considered positive
  bool _isPositiveInteraction(ReviewInteractionType type) {
    switch (type) {
      case ReviewInteractionType.successfulAuth:
      case ReviewInteractionType.favoriteAdded:
      case ReviewInteractionType.successfulSearch:
      case ReviewInteractionType.profileUpdated:
        return true;
      case ReviewInteractionType.appSession:
        return false; // Sessions are tracked separately
    }
  }
}

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/providers/remote_config_provider.dart';
import 'package:buscafarma/features/review/domain/entities/review_prompt_entity.dart';
import 'package:buscafarma/features/review/domain/repositories/review_repository.dart';
import 'package:buscafarma/features/review/data/providers/review_local_provider.dart';

/// Implementation of ReviewRepository using local storage
class ReviewRepositoryImpl implements ReviewRepository {
  final ReviewLocalProvider _localProvider;
  final Ref? _ref;

  ReviewRepositoryImpl(this._localProvider, {Ref? ref}) : _ref = ref;

  @override
  Future<ReviewPromptState> getReviewPromptState() async {
    try {
      // Ensure first install date is set
      await _localProvider.setFirstInstallDateIfNotExists();

      final state = await _localProvider.getReviewPromptState();
      if (state != null) {
        return state;
      }

      // If no state exists, create a new one with first install date
      final firstInstallDate = await _localProvider.getFirstInstallDate();
      final newState = ReviewPromptState(firstInstallDate: firstInstallDate);
      await _localProvider.saveReviewPromptState(newState);
      return newState;
    } catch (e) {
      // If there's any error, return a default state
      return ReviewPromptState(firstInstallDate: DateTime.now());
    }
  }

  @override
  Future<void> saveReviewPromptState(ReviewPromptState state) async {
    try {
      await _localProvider.saveReviewPromptState(state);
    } catch (e) {
      // Log error but don't throw to avoid breaking app functionality
      // In a production app, you might want to log this to analytics
      debugPrint('Error saving review prompt state: $e');
    }
  }

  @override
  Future<void> recordInteraction(ReviewInteractionType type, {Map<String, dynamic>? metadata}) async {
    try {
      final interaction = ReviewInteraction(type: type, timestamp: DateTime.now(), metadata: metadata);
      await _localProvider.addInteraction(interaction);
    } catch (e) {
      debugPrint('Error recording interaction: $e');
    }
  }

  @override
  Future<void> incrementSessionCount() async {
    try {
      await _localProvider.incrementSessionCount();
    } catch (e) {
      debugPrint('Error incrementing session count: $e');
    }
  }

  @override
  Future<ReviewPromptEligibility> checkEligibility() async {
    try {
      final state = await getReviewPromptState();
      final config = await getConfig();

      // If user has already reviewed or set preference to never ask
      if (state.hasReviewed || state.preference == ReviewPromptPreference.neverAsk) {
        return const ReviewPromptEligibility(isEligible: false, reason: 'User has already reviewed or opted out');
      }

      // Check minimum session count
      if (state.sessionCount < config.minimumSessionCount) {
        return ReviewPromptEligibility(isEligible: false, reason: 'Insufficient sessions: ${state.sessionCount}/${config.minimumSessionCount}', config: config, currentState: state);
      }

      // Check minimum positive interactions
      if (state.positiveInteractionCount < config.minimumPositiveInteractions) {
        return ReviewPromptEligibility(isEligible: false, reason: 'Insufficient positive interactions: ${state.positiveInteractionCount}/${config.minimumPositiveInteractions}', config: config, currentState: state);
      }

      // Check minimum days since install
      if (state.firstInstallDate != null) {
        final daysSinceInstall = DateTime.now().difference(state.firstInstallDate!).inDays;
        if (daysSinceInstall < config.minimumDaysSinceInstall) {
          return ReviewPromptEligibility(isEligible: false, reason: 'Too soon since install: $daysSinceInstall/${config.minimumDaysSinceInstall} days', config: config, currentState: state);
        }
      }

      // Check cooldown periods
      final now = DateTime.now();

      // Check remind later preference first (has shorter cooldown)
      if (state.preference == ReviewPromptPreference.remindLater && state.lastPromptDate != null) {
        final daysSinceRemindLater = now.difference(state.lastPromptDate!).inDays;
        if (daysSinceRemindLater < config.daysAfterRemindLater) {
          return ReviewPromptEligibility(isEligible: false, reason: 'Remind later period active: $daysSinceRemindLater/${config.daysAfterRemindLater} days', config: config, currentState: state);
        }
      }

      // Check if enough time has passed since last prompt (only if not remind later)
      if (state.preference != ReviewPromptPreference.remindLater && state.lastPromptDate != null) {
        final daysSinceLastPrompt = now.difference(state.lastPromptDate!).inDays;
        if (daysSinceLastPrompt < config.daysBetweenPrompts) {
          return ReviewPromptEligibility(isEligible: false, reason: 'Too soon since last prompt: $daysSinceLastPrompt/${config.daysBetweenPrompts} days', config: config, currentState: state);
        }
      }

      // Check if enough time has passed since dismissal
      if (state.lastDismissalDate != null) {
        final daysSinceDismissal = now.difference(state.lastDismissalDate!).inDays;
        if (daysSinceDismissal < config.daysAfterDismissal) {
          return ReviewPromptEligibility(isEligible: false, reason: 'Too soon since dismissal: $daysSinceDismissal/${config.daysAfterDismissal} days', config: config, currentState: state);
        }
      }

      // All checks passed
      return ReviewPromptEligibility(isEligible: true, reason: 'All eligibility criteria met', config: config, currentState: state);
    } catch (e) {
      return ReviewPromptEligibility(isEligible: false, reason: 'Error checking eligibility: $e');
    }
  }

  @override
  Future<void> recordPromptShown() async {
    try {
      await _localProvider.recordPromptShown();
    } catch (e) {
      debugPrint('Error recording prompt shown: $e');
    }
  }

  @override
  Future<void> recordPromptResult(ReviewPromptResult result) async {
    try {
      switch (result.action) {
        case ReviewPromptAction.reviewed:
          await markAsReviewed();
          break;
        case ReviewPromptAction.dismissed:
          await _localProvider.recordPromptDismissed();
          break;
        case ReviewPromptAction.remindLater:
          await setReviewPromptPreference(ReviewPromptPreference.remindLater);
          break;
        case ReviewPromptAction.neverAsk:
          await setReviewPromptPreference(ReviewPromptPreference.neverAsk);
          break;
        case ReviewPromptAction.error:
          // Just log the error, don't change state
          debugPrint('Review prompt error: ${result.errorMessage}');
          break;
      }
    } catch (e) {
      debugPrint('Error recording prompt result: $e');
    }
  }

  @override
  Future<void> setReviewPromptPreference(ReviewPromptPreference preference) async {
    try {
      await _localProvider.updateReviewPromptPreference(preference);
    } catch (e) {
      debugPrint('Error setting review prompt preference: $e');
    }
  }

  @override
  Future<void> markAsReviewed() async {
    try {
      await _localProvider.markAsReviewed();
    } catch (e) {
      debugPrint('Error marking as reviewed: $e');
    }
  }

  @override
  Future<void> clearReviewData() async {
    try {
      await _localProvider.clearReviewData();
    } catch (e) {
      debugPrint('Error clearing review data: $e');
    }
  }

  @override
  Future<ReviewPromptConfig> getConfig() async {
    try {
      // Primero intentar usar Remote Config si está disponible
      if (_ref != null) {
        try {
          // Acceder al proveedor de Remote Config
          final remoteConfigState = _ref.read(remoteConfigProvider);

          // Solo usar Remote Config si está listo (no en estado de carga o error)
          if (remoteConfigState is! AsyncLoading && remoteConfigState is! AsyncError) {
            final remoteConfig = _ref.read(remoteConfigServiceProvider);
            if (remoteConfig != null) {
              final configMap = remoteConfig.getReviewConfig();

              // Crear la configuración desde los valores remotos
              return ReviewPromptConfig(
                minimumSessionCount: configMap['minimumSessionCount'],
                minimumPositiveInteractions: configMap['minimumPositiveInteractions'],
                minimumDaysSinceInstall: configMap['minimumDaysSinceInstall'],
                daysBetweenPrompts: configMap['daysBetweenPrompts'],
                daysAfterDismissal: configMap['daysAfterDismissal'],
                daysAfterRemindLater: configMap['daysAfterRemindLater'],
              );
            } else {
              debugPrint('RemoteConfig service is null, using local config');
            }
          }
        } catch (e) {
          debugPrint('Error obteniendo configuración de Remote Config: $e');
          // Continuar y usar configuración local si hay un error
        }
      }

      // Si no se pudo usar Remote Config, intentar usar la configuración almacenada localmente
      final config = await _localProvider.getReviewConfig();
      return config ?? const ReviewPromptConfig();
    } catch (e) {
      // Return default config if there's an error
      return const ReviewPromptConfig();
    }
  }

  @override
  Future<void> updateConfig(ReviewPromptConfig config) async {
    try {
      // Guardar la configuración localmente
      await _localProvider.saveReviewConfig(config);
    } catch (e) {
      debugPrint('Error updating review config: $e');
    }
  }
}

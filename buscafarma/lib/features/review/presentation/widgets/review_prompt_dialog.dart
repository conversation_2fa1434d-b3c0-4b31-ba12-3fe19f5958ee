import 'package:flutter/material.dart';
import 'package:buscafarma/features/review/domain/entities/review_prompt_entity.dart';

/// Material Design 3 compliant dialog for review prompts
class ReviewPromptDialog extends StatelessWidget {
  const ReviewPromptDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AlertDialog(
      icon: Icon(Icons.star_rounded, size: 32, color: colorScheme.primary),
      title: Text('¿Te gusta BuscaFarma?', style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600), textAlign: TextAlign.center),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text('Tu opinión es muy importante para nosotros. ¿Te gustaría calificar nuestra aplicación en Google Play Store?', style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.onSurfaceVariant), textAlign: TextAlign.center),
          const SizedBox(height: 16),
          // Star rating visual (decorative)
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: List.generate(5, (index) {
              return Padding(padding: const EdgeInsets.symmetric(horizontal: 2), child: Icon(Icons.star_rounded, size: 24, color: colorScheme.primary.withAlpha((0.7 * 255).round())));
            }),
          ),
        ],
      ),
      actions: [
        // Never ask again button
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(ReviewPromptAction.neverAsk);
          },
          child: Text('No, gracias', style: TextStyle(color: colorScheme.onSurfaceVariant)),
        ),

        // Remind later button
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(ReviewPromptAction.remindLater);
          },
          child: Text('Recordar después', style: TextStyle(color: colorScheme.primary)),
        ),

        // Review button (primary action)
        FilledButton(
          onPressed: () {
            Navigator.of(context).pop(ReviewPromptAction.reviewed);
          },
          child: const Text('Calificar'),
        ),
      ],
      actionsPadding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      actionsAlignment: MainAxisAlignment.spaceEvenly,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(28)),
      backgroundColor: colorScheme.surface,
      surfaceTintColor: colorScheme.surfaceTint,
      elevation: 6,
    );
  }
}

/// Alternative compact review prompt dialog
class CompactReviewPromptDialog extends StatelessWidget {
  const CompactReviewPromptDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return AlertDialog(
      title: Row(children: [Icon(Icons.star_rounded, size: 24, color: colorScheme.primary), const SizedBox(width: 8), Expanded(child: Text('Califica BuscaFarma', style: theme.textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600)))]),
      content: Text('¿Te gustaría calificar nuestra aplicación en Google Play Store?', style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.onSurfaceVariant)),
      actions: [
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(ReviewPromptAction.neverAsk);
          },
          child: Text('No', style: TextStyle(color: colorScheme.onSurfaceVariant)),
        ),
        TextButton(
          onPressed: () {
            Navigator.of(context).pop(ReviewPromptAction.remindLater);
          },
          child: Text('Después', style: TextStyle(color: colorScheme.primary)),
        ),
        FilledButton(
          onPressed: () {
            Navigator.of(context).pop(ReviewPromptAction.reviewed);
          },
          child: const Text('Calificar'),
        ),
      ],
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(20)),
      backgroundColor: colorScheme.surface,
      surfaceTintColor: colorScheme.surfaceTint,
    );
  }
}

/// Bottom sheet style review prompt (alternative presentation)
class ReviewPromptBottomSheet extends StatelessWidget {
  const ReviewPromptBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(color: colorScheme.surface, borderRadius: const BorderRadius.vertical(top: Radius.circular(28))),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Handle bar
          Container(width: 32, height: 4, decoration: BoxDecoration(color: colorScheme.onSurfaceVariant.withAlpha((0.4 * 255).round()), borderRadius: BorderRadius.circular(2))),
          const SizedBox(height: 24),

          // Icon and title
          Icon(Icons.star_rounded, size: 48, color: colorScheme.primary),
          const SizedBox(height: 16),

          Text('¿Te gusta BuscaFarma?', style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600), textAlign: TextAlign.center),
          const SizedBox(height: 8),

          Text('Tu opinión nos ayuda a mejorar la aplicación para todos los usuarios.', style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.onSurfaceVariant), textAlign: TextAlign.center),
          const SizedBox(height: 32),

          // Action buttons
          Column(
            children: [
              SizedBox(
                width: double.infinity,
                child: FilledButton(
                  onPressed: () {
                    Navigator.of(context).pop(ReviewPromptAction.reviewed);
                  },
                  child: const Text('Calificar en Google Play'),
                ),
              ),
              const SizedBox(height: 8),

              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () {
                    Navigator.of(context).pop(ReviewPromptAction.remindLater);
                  },
                  child: const Text('Recordar después'),
                ),
              ),

              TextButton(
                onPressed: () {
                  Navigator.of(context).pop(ReviewPromptAction.neverAsk);
                },
                child: Text('No, gracias', style: TextStyle(color: colorScheme.onSurfaceVariant, fontSize: 12)),
              ),
            ],
          ),

          // Safe area padding
          SizedBox(height: MediaQuery.of(context).padding.bottom),
        ],
      ),
    );
  }

  /// Shows the bottom sheet
  static Future<ReviewPromptAction?> show(BuildContext context) {
    return showModalBottomSheet<ReviewPromptAction>(context: context, isScrollControlled: true, backgroundColor: Colors.transparent, builder: (context) => const ReviewPromptBottomSheet());
  }
}

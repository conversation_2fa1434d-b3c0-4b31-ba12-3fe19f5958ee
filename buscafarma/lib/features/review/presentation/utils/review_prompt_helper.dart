import 'package:buscafarma/features/review/domain/entities/review_prompt_entity.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/review/presentation/providers/review_manager_provider.dart';

/// Helper class for managing review prompts in the app
class ReviewPromptHelper {
  /// Attempts to show a review prompt after a successful authentication
  /// This is typically called after a user successfully signs in
  static Future<void> maybeShowAfterAuth(BuildContext context, WidgetRef ref) async {
    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);

      // Check if we should show a review prompt
      final eligibility = await reviewManager.checkEligibility();

      if (eligibility.isEligible) {
        // Wait a bit to let the user settle in after authentication
        await Future.delayed(const Duration(seconds: 2));

        if (context.mounted) {
          await reviewManager.maybeShowReviewPrompt(context);
        }
      }
    } catch (e) {
      debugPrint('Error showing review prompt after auth: $e');
    }
  }

  /// Attempts to show a review prompt after adding a favorite
  /// This is called after a user successfully adds a medication to favorites
  static Future<void> maybeShowAfterFavoriteAdded(BuildContext context, WidgetRef ref) async {
    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);

      // Check if we should show a review prompt
      final eligibility = await reviewManager.checkEligibility();

      if (eligibility.isEligible) {
        // Show prompt after a short delay to not interrupt the user flow
        await Future.delayed(const Duration(milliseconds: 1500));

        if (context.mounted) {
          await reviewManager.maybeShowReviewPrompt(context);
        }
      }
    } catch (e) {
      debugPrint('Error showing review prompt after favorite added: $e');
    }
  }

  /// Attempts to show a review prompt after a successful search
  /// This is called after a user performs a successful medication search
  static Future<void> maybeShowAfterSuccessfulSearch(BuildContext context, WidgetRef ref) async {
    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);

      // Check if we should show a review prompt
      final eligibility = await reviewManager.checkEligibility();

      if (eligibility.isEligible) {
        // Wait longer for search results to avoid interrupting user flow
        await Future.delayed(const Duration(seconds: 3));

        if (context.mounted) {
          await reviewManager.maybeShowReviewPrompt(context);
        }
      }
    } catch (e) {
      debugPrint('Error showing review prompt after successful search: $e');
    }
  }

  /// Attempts to show a review prompt during app idle time
  /// This can be called when the user is browsing but not actively performing actions
  static Future<void> maybeShowDuringIdleTime(BuildContext context, WidgetRef ref) async {
    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);

      // Check if we should show a review prompt
      final eligibility = await reviewManager.checkEligibility();

      if (eligibility.isEligible) {
        // Show prompt during idle time
        if (context.mounted) {
          await reviewManager.maybeShowReviewPrompt(context);
        }
      }
    } catch (e) {
      debugPrint('Error showing review prompt during idle time: $e');
    }
  }

  /// Forces a review prompt for testing purposes
  /// This should only be used in debug builds or for testing
  static Future<void> forceShowForTesting(BuildContext context, WidgetRef ref) async {
    assert(() {
      // Only allow in debug mode
      return true;
    }());

    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);
      await reviewManager.forceShowReviewPrompt(context);
    } catch (e) {
      debugPrint('Error forcing review prompt for testing: $e');
    }
  }

  /// Checks if the user is eligible for a review prompt without showing it
  /// Useful for UI decisions (e.g., showing a "Rate App" button)
  static Future<bool> isEligibleForReview(WidgetRef ref) async {
    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);
      final eligibility = await reviewManager.checkEligibility();
      return eligibility.isEligible;
    } catch (e) {
      debugPrint('Error checking review eligibility: $e');
      return false;
    }
  }

  /// Gets the current review state for debugging purposes
  static Future<String> getDebugInfo(WidgetRef ref) async {
    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);
      final state = ref.read(reviewManagerProvider);
      final eligibility = await reviewManager.checkEligibility();

      return '''
Review Debug Info:
- Sessions: ${state.promptState.sessionCount}
- Positive Interactions: ${state.promptState.positiveInteractionCount}
- Has Reviewed: ${state.promptState.hasReviewed}
- Preference: ${state.promptState.preference}
- Eligible: ${eligibility.isEligible}
- Reason: ${eligibility.reason}
- Last Prompt: ${state.promptState.lastPromptDate}
- Install Date: ${state.promptState.firstInstallDate}
''';
    } catch (e) {
      return 'Error getting debug info: $e';
    }
  }

  /// Resets all review data (for testing purposes only)
  static Future<void> resetForTesting(WidgetRef ref) async {
    assert(() {
      // Only allow in debug mode
      return true;
    }());

    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);
      await reviewManager.resetReviewData();
    } catch (e) {
      debugPrint('Error resetting review data: $e');
    }
  }

  /// Marks the user as having reviewed the app (useful for manual review flows)
  static Future<void> markAsReviewed(WidgetRef ref) async {
    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);
      await reviewManager.markAsReviewed();
    } catch (e) {
      debugPrint('Error marking as reviewed: $e');
    }
  }

  /// Sets the user's review preference
  static Future<void> setReviewPreference(WidgetRef ref, ReviewPromptPreference preference) async {
    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);
      await reviewManager.setReviewPreference(preference);
    } catch (e) {
      debugPrint('Error setting review preference: $e');
    }
  }
}

/// Extension to make it easier to use review prompts in widgets
extension ReviewPromptExtension on WidgetRef {
  /// Convenient method to maybe show review prompt after auth
  Future<void> maybeShowReviewAfterAuth(BuildContext context) async {
    await ReviewPromptHelper.maybeShowAfterAuth(context, this);
  }

  /// Convenient method to maybe show review prompt after favorite added
  Future<void> maybeShowReviewAfterFavorite(BuildContext context) async {
    await ReviewPromptHelper.maybeShowAfterFavoriteAdded(context, this);
  }

  /// Convenient method to maybe show review prompt after successful search
  Future<void> maybeShowReviewAfterSearch(BuildContext context) async {
    await ReviewPromptHelper.maybeShowAfterSuccessfulSearch(context, this);
  }

  /// Convenient method to check review eligibility
  Future<bool> isEligibleForReview() async {
    return await ReviewPromptHelper.isEligibleForReview(this);
  }
}

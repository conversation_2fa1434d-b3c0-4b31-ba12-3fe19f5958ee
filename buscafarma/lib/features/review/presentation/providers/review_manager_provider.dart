import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/review/domain/entities/review_prompt_entity.dart';
import 'package:buscafarma/features/review/domain/repositories/review_repository.dart';
import 'package:buscafarma/features/review/data/repositories/review_repository_impl.dart';
import 'package:buscafarma/features/review/data/providers/review_local_provider.dart';
import 'package:buscafarma/features/review/presentation/services/review_service.dart';

/// Provider for the review local storage
final reviewLocalProvider = Provider<ReviewLocalProvider>((ref) {
  return ReviewLocalProvider();
});

/// Provider for the review repository
final reviewRepositoryProvider = Provider<ReviewRepository>((ref) {
  final localProvider = ref.read(reviewLocalProvider);
  return ReviewRepositoryImpl(localProvider, ref: ref);
});

/// Provider for the review service
final reviewServiceProvider = Provider<ReviewService>((ref) {
  final repository = ref.read(reviewRepositoryProvider);
  return ReviewService(repository);
});

/// State for the review manager
@immutable
class ReviewManagerState {
  final ReviewPromptState promptState;
  final ReviewPromptConfig config;
  final bool isLoading;
  final String? errorMessage;
  final ReviewPromptEligibility? lastEligibilityCheck;

  const ReviewManagerState({required this.promptState, required this.config, this.isLoading = false, this.errorMessage, this.lastEligibilityCheck});

  ReviewManagerState copyWith({ReviewPromptState? promptState, ReviewPromptConfig? config, bool? isLoading, String? errorMessage, ReviewPromptEligibility? lastEligibilityCheck}) {
    return ReviewManagerState(promptState: promptState ?? this.promptState, config: config ?? this.config, isLoading: isLoading ?? this.isLoading, errorMessage: errorMessage, lastEligibilityCheck: lastEligibilityCheck ?? this.lastEligibilityCheck);
  }
}

/// Provider for the review manager
final reviewManagerProvider = StateNotifierProvider<ReviewManagerNotifier, ReviewManagerState>((ref) {
  final reviewService = ref.read(reviewServiceProvider);
  return ReviewManagerNotifier(reviewService);
});

/// Notifier for managing review functionality
class ReviewManagerNotifier extends StateNotifier<ReviewManagerState> {
  final ReviewService _reviewService;

  ReviewManagerNotifier(this._reviewService) : super(const ReviewManagerState(promptState: ReviewPromptState(), config: ReviewPromptConfig())) {
    _initialize();
  }

  /// Initialize the review manager
  Future<void> _initialize() async {
    state = state.copyWith(isLoading: true, errorMessage: null);

    try {
      final promptState = await _reviewService.getReviewState();
      final config = await _reviewService.getConfig();

      state = state.copyWith(promptState: promptState, config: config, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: 'Failed to initialize review manager: $e');
    }
  }

  /// Records a user interaction
  Future<void> recordInteraction(ReviewInteractionType type, {Map<String, dynamic>? metadata}) async {
    try {
      await _reviewService.recordInteraction(type, metadata: metadata);
      await _refreshState();
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to record interaction: $e');
    }
  }

  /// Increments the session count
  Future<void> incrementSessionCount() async {
    try {
      await _reviewService.incrementSessionCount();
      await _refreshState();
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to increment session count: $e');
    }
  }

  /// Checks if the user is eligible for a review prompt
  Future<ReviewPromptEligibility> checkEligibility() async {
    try {
      final eligibility = await _reviewService.checkEligibility();
      state = state.copyWith(lastEligibilityCheck: eligibility);
      return eligibility;
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to check eligibility: $e');
      return const ReviewPromptEligibility(isEligible: false, reason: 'Error checking eligibility');
    }
  }

  /// Attempts to show a review prompt
  Future<bool> maybeShowReviewPrompt(BuildContext context) async {
    try {
      final result = await _reviewService.maybeShowReviewPrompt(context);
      await _refreshState();
      return result;
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to show review prompt: $e');
      return false;
    }
  }

  /// Forces a review prompt to be shown (for testing)
  Future<bool> forceShowReviewPrompt(BuildContext context) async {
    try {
      final result = await _reviewService.forceShowReviewPrompt(context);
      await _refreshState();
      return result;
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to force review prompt: $e');
      return false;
    }
  }

  /// Sets the user's review preference
  Future<void> setReviewPreference(ReviewPromptPreference preference) async {
    try {
      await _reviewService.setReviewPreference(preference);
      await _refreshState();
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to set review preference: $e');
    }
  }

  /// Marks that the user has reviewed the app
  Future<void> markAsReviewed() async {
    try {
      await _reviewService.markAsReviewed();
      await _refreshState();
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to mark as reviewed: $e');
    }
  }

  /// Resets all review data (for testing)
  Future<void> resetReviewData() async {
    try {
      await _reviewService.resetReviewData();
      await _refreshState();
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to reset review data: $e');
    }
  }

  /// Updates the review configuration
  Future<void> updateConfig(ReviewPromptConfig config) async {
    try {
      await _reviewService.updateConfig(config);
      state = state.copyWith(config: config);
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to update config: $e');
    }
  }

  /// Checks if native in-app review is available
  Future<bool> isNativeReviewAvailable() async {
    try {
      return await _reviewService.isNativeReviewAvailable();
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to check native review availability: $e');
      return false;
    }
  }

  /// Refreshes the internal state from the repository
  Future<void> _refreshState() async {
    try {
      final promptState = await _reviewService.getReviewState();
      final config = await _reviewService.getConfig();
      state = state.copyWith(promptState: promptState, config: config);
    } catch (e) {
      state = state.copyWith(errorMessage: 'Failed to refresh state: $e');
    }
  }

  /// Convenience methods for recording specific interactions
  Future<void> recordSuccessfulAuth({Map<String, dynamic>? metadata}) async {
    await recordInteraction(ReviewInteractionType.successfulAuth, metadata: metadata);
  }

  Future<void> recordFavoriteAdded({Map<String, dynamic>? metadata}) async {
    await recordInteraction(ReviewInteractionType.favoriteAdded, metadata: metadata);
  }

  Future<void> recordSuccessfulSearch({Map<String, dynamic>? metadata}) async {
    await recordInteraction(ReviewInteractionType.successfulSearch, metadata: metadata);
  }

  Future<void> recordProfileUpdated({Map<String, dynamic>? metadata}) async {
    await recordInteraction(ReviewInteractionType.profileUpdated, metadata: metadata);
  }

  Future<void> recordAppSession({Map<String, dynamic>? metadata}) async {
    await recordInteraction(ReviewInteractionType.appSession, metadata: metadata);
  }

  /// Clears any error messages
  void clearError() {
    state = state.copyWith(errorMessage: null);
  }
}

/// Provider for checking review eligibility (auto-refresh)
final reviewEligibilityProvider = FutureProvider<ReviewPromptEligibility>((ref) async {
  final reviewManager = ref.read(reviewManagerProvider.notifier);
  return await reviewManager.checkEligibility();
});

/// Provider for checking if native review is available
final nativeReviewAvailableProvider = FutureProvider<bool>((ref) async {
  final reviewManager = ref.read(reviewManagerProvider.notifier);
  return await reviewManager.isNativeReviewAvailable();
});

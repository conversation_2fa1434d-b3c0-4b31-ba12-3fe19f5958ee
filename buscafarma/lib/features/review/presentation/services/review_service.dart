import 'package:flutter/material.dart';
import 'package:in_app_review/in_app_review.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:buscafarma/features/review/domain/entities/review_prompt_entity.dart';
import 'package:buscafarma/features/review/domain/repositories/review_repository.dart';
import 'package:buscafarma/features/review/presentation/widgets/review_prompt_dialog.dart';

/// Service for managing in-app review functionality
class ReviewService {
  final ReviewRepository _repository;
  final InAppReview _inAppReview;

  ReviewService(this._repository) : _inAppReview = InAppReview.instance;

  /// Records a positive user interaction
  Future<void> recordInteraction(ReviewInteractionType type, {Map<String, dynamic>? metadata}) async {
    await _repository.recordInteraction(type, metadata: metadata);
  }

  /// Increments the session count (should be called on app start)
  Future<void> incrementSessionCount() async {
    await _repository.incrementSessionCount();
  }

  /// Checks if the user is eligible for a review prompt
  Future<ReviewPromptEligibility> checkEligibility() async {
    return await _repository.checkEligibility();
  }

  /// Attempts to show a review prompt if the user is eligible
  /// Returns true if a prompt was shown, false otherwise
  Future<bool> maybeShowReviewPrompt(BuildContext context) async {
    try {
      final eligibility = await checkEligibility();

      if (!eligibility.isEligible) {
        debugPrint('Review prompt not eligible: ${eligibility.reason}');
        return false;
      }

      // Record that we're showing a prompt
      await _repository.recordPromptShown();

      // Try to show native in-app review first
      final isAvailable = await _inAppReview.isAvailable();

      if (isAvailable) {
        debugPrint('Showing native in-app review');
        await _inAppReview.requestReview();

        // Record successful review
        final result = ReviewPromptResult(action: ReviewPromptAction.reviewed, timestamp: DateTime.now());
        await _repository.recordPromptResult(result);

        return true;
      } else {
        // Fallback to custom dialog
        debugPrint('Native review not available, showing custom dialog');
        // Check if context is still valid before showing dialog
        if (context.mounted) {
          return await _showCustomReviewDialog(context);
        }
        return false;
      }
    } catch (e) {
      debugPrint('Error showing review prompt: $e');

      // Record error
      final result = ReviewPromptResult(action: ReviewPromptAction.error, timestamp: DateTime.now(), errorMessage: e.toString());
      await _repository.recordPromptResult(result);

      return false;
    }
  }

  /// Shows a custom review dialog as fallback
  Future<bool> _showCustomReviewDialog(BuildContext context) async {
    final result = await showDialog<ReviewPromptAction>(context: context, barrierDismissible: false, builder: (context) => const ReviewPromptDialog());

    if (result != null) {
      final promptResult = ReviewPromptResult(action: result, timestamp: DateTime.now());
      await _repository.recordPromptResult(promptResult);

      // If user chose to review, open Play Store
      if (result == ReviewPromptAction.reviewed) {
        await _openPlayStore();
      }

      return true;
    }

    return false;
  }

  /// Opens the Play Store for the app
  Future<void> _openPlayStore() async {
    try {
      // Try to open in Play Store app first
      const playStoreUrl = 'market://details?id=com.buscafarma.app';
      if (await canLaunchUrl(Uri.parse(playStoreUrl))) {
        await launchUrl(Uri.parse(playStoreUrl));
      } else {
        // Fallback to web version
        const webUrl = 'https://play.google.com/store/apps/details?id=com.buscafarma.app';
        await launchUrl(Uri.parse(webUrl), mode: LaunchMode.externalApplication);
      }
    } catch (e) {
      debugPrint('Error opening Play Store: $e');
    }
  }

  /// Forces a review prompt to be shown (for testing purposes)
  Future<bool> forceShowReviewPrompt(BuildContext context) async {
    try {
      await _repository.recordPromptShown();

      final isAvailable = await _inAppReview.isAvailable();

      if (isAvailable) {
        await _inAppReview.requestReview();
        return true;
      } else {
        // Check if context is still valid before showing dialog
        if (context.mounted) {
          return await _showCustomReviewDialog(context);
        }
        return false;
      }
    } catch (e) {
      debugPrint('Error forcing review prompt: $e');
      return false;
    }
  }

  /// Gets the current review prompt state (for debugging/admin purposes)
  Future<ReviewPromptState> getReviewState() async {
    return await _repository.getReviewPromptState();
  }

  /// Resets all review data (for testing purposes)
  Future<void> resetReviewData() async {
    await _repository.clearReviewData();
  }

  /// Sets the user's review preference
  Future<void> setReviewPreference(ReviewPromptPreference preference) async {
    await _repository.setReviewPromptPreference(preference);
  }

  /// Marks that the user has already reviewed the app
  Future<void> markAsReviewed() async {
    await _repository.markAsReviewed();
  }

  /// Checks if native in-app review is available on this device
  Future<bool> isNativeReviewAvailable() async {
    try {
      return await _inAppReview.isAvailable();
    } catch (e) {
      debugPrint('Error checking native review availability: $e');
      return false;
    }
  }

  /// Gets review prompt configuration
  Future<ReviewPromptConfig> getConfig() async {
    return await _repository.getConfig();
  }

  /// Updates review prompt configuration
  Future<void> updateConfig(ReviewPromptConfig config) async {
    await _repository.updateConfig(config);
  }

  /// Convenience method to record successful authentication
  Future<void> recordSuccessfulAuth({Map<String, dynamic>? metadata}) async {
    await recordInteraction(ReviewInteractionType.successfulAuth, metadata: metadata);
  }

  /// Convenience method to record favorite added
  Future<void> recordFavoriteAdded({Map<String, dynamic>? metadata}) async {
    await recordInteraction(ReviewInteractionType.favoriteAdded, metadata: metadata);
  }

  /// Convenience method to record successful search
  Future<void> recordSuccessfulSearch({Map<String, dynamic>? metadata}) async {
    await recordInteraction(ReviewInteractionType.successfulSearch, metadata: metadata);
  }

  /// Convenience method to record profile update
  Future<void> recordProfileUpdated({Map<String, dynamic>? metadata}) async {
    await recordInteraction(ReviewInteractionType.profileUpdated, metadata: metadata);
  }

  /// Convenience method to record app session
  Future<void> recordAppSession({Map<String, dynamic>? metadata}) async {
    await recordInteraction(ReviewInteractionType.appSession, metadata: metadata);
  }
}

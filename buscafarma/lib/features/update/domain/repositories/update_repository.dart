import 'package:dartz/dartz.dart';
import 'package:buscafarma/core/error/failures.dart';
import 'package:buscafarma/features/update/domain/entities/update_info.dart';
import 'package:buscafarma/features/update/domain/entities/update_state.dart';

/// Abstract repository interface for app update operations
/// Follows clean architecture principles by defining the contract
/// for update-related operations without implementation details
abstract class UpdateRepository {
  /// Checks if an app update is available from the Play Store
  /// 
  /// Returns [Right(UpdateInfo)] if check succeeds
  /// Returns [Left(Failure)] if check fails
  /// 
  /// Possible failures:
  /// - [UpdateCheckFailed] if unable to check for updates
  /// - [NetworkError] if no internet connection
  /// - [UnexpectedError] for other errors
  Future<Either<Failure, UpdateInfo>> checkForUpdate();

  /// Starts downloading an available update
  /// 
  /// [updateType] specifies whether to use flexible or immediate update flow
  /// 
  /// Returns [Right(void)] if download starts successfully
  /// Returns [Left(Failure)] if download fails to start
  /// 
  /// Possible failures:
  /// - [UpdateNotAvailable] if no update is available
  /// - [UpdateDownloadFailed] if download cannot be started
  /// - [NetworkError] if no internet connection
  Future<Either<Failure, void>> startUpdate(UpdateType updateType);

  /// Gets the current download progress for an ongoing update
  /// 
  /// Returns [Right(double)] with progress value (0.0 to 1.0)
  /// Returns [Left(Failure)] if unable to get progress
  /// 
  /// Possible failures:
  /// - [UpdateNotAvailable] if no update is in progress
  /// - [UnexpectedError] for other errors
  Future<Either<Failure, double>> getDownloadProgress();

  /// Checks if a downloaded update is ready to be installed
  /// 
  /// Returns [Right(bool)] indicating if update is ready
  /// Returns [Left(Failure)] if unable to check status
  /// 
  /// Possible failures:
  /// - [UnexpectedError] for unexpected errors
  Future<Either<Failure, bool>> isUpdateReadyToInstall();

  /// Completes the installation of a downloaded update
  /// 
  /// For immediate updates, this will restart the app automatically
  /// For flexible updates, this will install in the background
  /// 
  /// Returns [Right(void)] if installation starts successfully
  /// Returns [Left(Failure)] if installation fails
  /// 
  /// Possible failures:
  /// - [UpdateInstallationFailed] if installation cannot be completed
  /// - [UpdateNotAvailable] if no update is ready to install
  Future<Either<Failure, void>> completeUpdate();

  /// Cancels an ongoing update download or installation
  /// 
  /// Returns [Right(void)] if cancellation succeeds
  /// Returns [Left(Failure)] if cancellation fails
  /// 
  /// Possible failures:
  /// - [UnexpectedError] if unable to cancel
  Future<Either<Failure, void>> cancelUpdate();

  /// Gets the current app version information
  /// 
  /// Returns [Right(Map)] with version details including:
  /// - 'version': version name (e.g., "1.0.0")
  /// - 'buildNumber': version code (e.g., 1)
  /// 
  /// Returns [Left(Failure)] if unable to get version info
  Future<Either<Failure, Map<String, dynamic>>> getCurrentVersionInfo();

  /// Checks if in-app updates are supported on the current device
  /// 
  /// Returns [Right(bool)] indicating support status
  /// Returns [Left(Failure)] if unable to check support
  Future<Either<Failure, bool>> isInAppUpdateSupported();

  /// Opens the Play Store page for the app as a fallback
  /// when in-app update is not available or fails
  /// 
  /// Returns [Right(void)] if Play Store opens successfully
  /// Returns [Left(Failure)] if unable to open Play Store
  /// 
  /// Possible failures:
  /// - [UnexpectedError] if Play Store cannot be opened
  Future<Either<Failure, void>> openPlayStore();

  /// Stream of update state changes for real-time monitoring
  /// 
  /// Emits [UpdateState] objects as the update process progresses
  /// Useful for updating UI in real-time during download/installation
  Stream<UpdateState> get updateStateStream;

  /// Disposes of any resources used by the repository
  /// Should be called when the repository is no longer needed
  void dispose();
}

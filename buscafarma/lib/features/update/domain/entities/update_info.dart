import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:buscafarma/features/update/domain/entities/update_state.dart';

part 'update_info.freezed.dart';

/// Information about an available app update
@freezed
abstract class UpdateInfo with _$UpdateInfo {
  const factory UpdateInfo({
    /// Whether an update is available
    required bool isAvailable,

    /// Type of update (flexible or immediate)
    required UpdateType updateType,

    /// Available version string from Play Store
    String? availableVersion,

    /// Current installed version
    String? currentVersion,

    /// Version code of available update
    int? availableVersionCode,

    /// Current version code
    int? currentVersionCode,

    /// Size of the update in bytes
    int? updateSizeBytes,

    /// Priority of the update (0-5, where 5 is highest priority)
    @Default(0) int updatePriority,

    /// Whether this update is mandatory
    @Default(false) bool isMandatory,

    /// Release notes or update description
    String? releaseNotes,

    /// Minimum version code that requires immediate update
    int? minimumRequiredVersionCode,

    /// Timestamp when update was published
    DateTime? publishedDate,

    /// Whether the update is compatible with current device
    @Default(true) bool isCompatible,

    /// Additional metadata about the update
    Map<String, dynamic>? metadata,
  }) = _UpdateInfo;

  const UpdateInfo._();

  /// Whether this is a major version update
  bool get isMajorUpdate {
    if (availableVersionCode == null || currentVersionCode == null) {
      return false;
    }

    // Consider it major if version code difference is significant
    return (availableVersionCode! - currentVersionCode!) >= 10;
  }

  /// Whether this update should be treated as critical
  bool get isCritical {
    return isMandatory || updatePriority >= 4;
  }

  /// Whether the current version is below minimum required
  bool get isBelowMinimumRequired {
    if (minimumRequiredVersionCode == null || currentVersionCode == null) {
      return false;
    }
    return currentVersionCode! < minimumRequiredVersionCode!;
  }

  /// Human-readable update size
  String get formattedUpdateSize {
    if (updateSizeBytes == null) return 'Tamaño desconocido';

    final sizeInMB = updateSizeBytes! / (1024 * 1024);
    if (sizeInMB < 1) {
      final sizeInKB = updateSizeBytes! / 1024;
      return '${sizeInKB.toStringAsFixed(0)} KB';
    } else if (sizeInMB < 1024) {
      return '${sizeInMB.toStringAsFixed(1)} MB';
    } else {
      final sizeInGB = sizeInMB / 1024;
      return '${sizeInGB.toStringAsFixed(1)} GB';
    }
  }

  /// Priority description for UI display
  String get priorityDescription {
    switch (updatePriority) {
      case 0:
        return 'Opcional';
      case 1:
        return 'Baja';
      case 2:
        return 'Media';
      case 3:
        return 'Alta';
      case 4:
        return 'Crítica';
      case 5:
        return 'Urgente';
      default:
        return 'Desconocida';
    }
  }

  /// Recommended update type based on priority and mandatory flag
  UpdateType get recommendedUpdateType {
    if (isMandatory || isBelowMinimumRequired || updatePriority >= 4) {
      return UpdateType.immediate;
    }
    return UpdateType.flexible;
  }

  /// User-friendly version comparison string
  String get versionComparisonText {
    if (currentVersion != null && availableVersion != null) {
      return 'Actualizar de v$currentVersion a v$availableVersion';
    } else if (availableVersion != null) {
      return 'Actualizar a v$availableVersion';
    }
    return 'Nueva versión disponible';
  }

  /// Whether the update should block app usage
  bool get shouldBlockAppUsage {
    return isMandatory || isBelowMinimumRequired;
  }

  /// Days since update was published
  int? get daysSincePublished {
    if (publishedDate == null) return null;
    return DateTime.now().difference(publishedDate!).inDays;
  }

  /// Whether this is a recent update (published within last 7 days)
  bool get isRecentUpdate {
    final days = daysSincePublished;
    return days != null && days <= 7;
  }
}

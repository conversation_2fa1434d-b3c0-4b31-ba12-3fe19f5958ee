import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:buscafarma/core/error/failures.dart';

part 'update_state.freezed.dart';

/// Enumeration of possible update statuses
enum UpdateStatus {
  /// Initial state, no update check performed
  initial,

  /// Currently checking for updates
  checking,

  /// No update available
  noUpdateAvailable,

  /// Update is available and ready to download
  updateAvailable,

  /// Update download is in progress
  downloading,

  /// Update download completed, ready to install
  downloadCompleted,

  /// Update installation is in progress
  installing,

  /// Update installation completed successfully
  installCompleted,

  /// Update process failed
  failed,

  /// Update was cancelled by user or system
  cancelled,
}

/// Enumeration of update types
enum UpdateType {
  /// Flexible update - user can continue using the app
  flexible,

  /// Immediate update - user must update to continue
  immediate,
}

/// Immutable state class for update management
@freezed
abstract class UpdateState with _$UpdateState {
  const factory UpdateState({
    /// Current status of the update process
    @Default(UpdateStatus.initial) UpdateStatus status,

    /// Type of update (flexible or immediate)
    UpdateType? updateType,

    /// Download progress (0.0 to 1.0)
    @Default(0.0) double downloadProgress,

    /// Installation progress (0.0 to 1.0)
    @Default(0.0) double installProgress,

    /// Available version string
    String? availableVersion,

    /// Current app version string
    String? currentVersion,

    /// Error information if update fails
    Failure? failure,

    /// Additional error message
    String? errorMessage,

    /// Whether update check is in progress
    @Default(false) bool isChecking,

    /// Whether download is in progress
    @Default(false) bool isDownloading,

    /// Whether installation is in progress
    @Default(false) bool isInstalling,

    /// Whether update is mandatory (blocks app usage)
    @Default(false) bool isMandatory,

    /// Timestamp of last update check
    DateTime? lastCheckTime,
  }) = _UpdateState;

  const UpdateState._();

  /// Whether an update is available
  bool get hasUpdateAvailable => status == UpdateStatus.updateAvailable;

  /// Whether update process is in progress
  bool get isInProgress => status == UpdateStatus.checking || status == UpdateStatus.downloading || status == UpdateStatus.installing;

  /// Whether update process has completed successfully
  bool get isCompleted => status == UpdateStatus.installCompleted;

  /// Whether update process has failed
  bool get hasFailed => status == UpdateStatus.failed;

  /// Whether update process was cancelled
  bool get wasCancelled => status == UpdateStatus.cancelled;

  /// Whether the update is ready to be installed
  bool get isReadyToInstall => status == UpdateStatus.downloadCompleted;

  /// Overall progress combining download and install progress
  double get overallProgress {
    switch (status) {
      case UpdateStatus.downloading:
        return downloadProgress * 0.7; // Download takes 70% of total progress
      case UpdateStatus.downloadCompleted:
        return 0.7;
      case UpdateStatus.installing:
        return 0.7 + (installProgress * 0.3); // Install takes 30% of total progress
      case UpdateStatus.installCompleted:
        return 1.0;
      default:
        return 0.0;
    }
  }

  /// User-friendly status message
  String get statusMessage {
    switch (status) {
      case UpdateStatus.initial:
        return 'Listo para verificar actualizaciones';
      case UpdateStatus.checking:
        return 'Verificando actualizaciones...';
      case UpdateStatus.noUpdateAvailable:
        return 'La aplicación está actualizada';
      case UpdateStatus.updateAvailable:
        return 'Nueva actualización disponible';
      case UpdateStatus.downloading:
        return 'Descargando actualización...';
      case UpdateStatus.downloadCompleted:
        return 'Descarga completada';
      case UpdateStatus.installing:
        return 'Instalando actualización...';
      case UpdateStatus.installCompleted:
        return 'Actualización completada';
      case UpdateStatus.failed:
        return errorMessage ?? 'Error en la actualización';
      case UpdateStatus.cancelled:
        return 'Actualización cancelada';
    }
  }

  /// Whether the current state allows starting a new update check
  bool get canCheckForUpdate => status == UpdateStatus.initial || status == UpdateStatus.noUpdateAvailable || status == UpdateStatus.failed || status == UpdateStatus.cancelled;

  /// Whether the current state allows starting download
  bool get canStartDownload => status == UpdateStatus.updateAvailable;

  /// Whether the current state allows starting installation
  bool get canStartInstall => status == UpdateStatus.downloadCompleted;
}

// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$UpdateState {

/// Current status of the update process
 UpdateStatus get status;/// Type of update (flexible or immediate)
 UpdateType? get updateType;/// Download progress (0.0 to 1.0)
 double get downloadProgress;/// Installation progress (0.0 to 1.0)
 double get installProgress;/// Available version string
 String? get availableVersion;/// Current app version string
 String? get currentVersion;/// Error information if update fails
 Failure? get failure;/// Additional error message
 String? get errorMessage;/// Whether update check is in progress
 bool get isChecking;/// Whether download is in progress
 bool get isDownloading;/// Whether installation is in progress
 bool get isInstalling;/// Whether update is mandatory (blocks app usage)
 bool get isMandatory;/// Timestamp of last update check
 DateTime? get lastCheckTime;
/// Create a copy of UpdateState
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateStateCopyWith<UpdateState> get copyWith => _$UpdateStateCopyWithImpl<UpdateState>(this as UpdateState, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateState&&(identical(other.status, status) || other.status == status)&&(identical(other.updateType, updateType) || other.updateType == updateType)&&(identical(other.downloadProgress, downloadProgress) || other.downloadProgress == downloadProgress)&&(identical(other.installProgress, installProgress) || other.installProgress == installProgress)&&(identical(other.availableVersion, availableVersion) || other.availableVersion == availableVersion)&&(identical(other.currentVersion, currentVersion) || other.currentVersion == currentVersion)&&(identical(other.failure, failure) || other.failure == failure)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isChecking, isChecking) || other.isChecking == isChecking)&&(identical(other.isDownloading, isDownloading) || other.isDownloading == isDownloading)&&(identical(other.isInstalling, isInstalling) || other.isInstalling == isInstalling)&&(identical(other.isMandatory, isMandatory) || other.isMandatory == isMandatory)&&(identical(other.lastCheckTime, lastCheckTime) || other.lastCheckTime == lastCheckTime));
}


@override
int get hashCode => Object.hash(runtimeType,status,updateType,downloadProgress,installProgress,availableVersion,currentVersion,failure,errorMessage,isChecking,isDownloading,isInstalling,isMandatory,lastCheckTime);

@override
String toString() {
  return 'UpdateState(status: $status, updateType: $updateType, downloadProgress: $downloadProgress, installProgress: $installProgress, availableVersion: $availableVersion, currentVersion: $currentVersion, failure: $failure, errorMessage: $errorMessage, isChecking: $isChecking, isDownloading: $isDownloading, isInstalling: $isInstalling, isMandatory: $isMandatory, lastCheckTime: $lastCheckTime)';
}


}

/// @nodoc
abstract mixin class $UpdateStateCopyWith<$Res>  {
  factory $UpdateStateCopyWith(UpdateState value, $Res Function(UpdateState) _then) = _$UpdateStateCopyWithImpl;
@useResult
$Res call({
 UpdateStatus status, UpdateType? updateType, double downloadProgress, double installProgress, String? availableVersion, String? currentVersion, Failure? failure, String? errorMessage, bool isChecking, bool isDownloading, bool isInstalling, bool isMandatory, DateTime? lastCheckTime
});


$FailureCopyWith<$Res>? get failure;

}
/// @nodoc
class _$UpdateStateCopyWithImpl<$Res>
    implements $UpdateStateCopyWith<$Res> {
  _$UpdateStateCopyWithImpl(this._self, this._then);

  final UpdateState _self;
  final $Res Function(UpdateState) _then;

/// Create a copy of UpdateState
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? status = null,Object? updateType = freezed,Object? downloadProgress = null,Object? installProgress = null,Object? availableVersion = freezed,Object? currentVersion = freezed,Object? failure = freezed,Object? errorMessage = freezed,Object? isChecking = null,Object? isDownloading = null,Object? isInstalling = null,Object? isMandatory = null,Object? lastCheckTime = freezed,}) {
  return _then(_self.copyWith(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as UpdateStatus,updateType: freezed == updateType ? _self.updateType : updateType // ignore: cast_nullable_to_non_nullable
as UpdateType?,downloadProgress: null == downloadProgress ? _self.downloadProgress : downloadProgress // ignore: cast_nullable_to_non_nullable
as double,installProgress: null == installProgress ? _self.installProgress : installProgress // ignore: cast_nullable_to_non_nullable
as double,availableVersion: freezed == availableVersion ? _self.availableVersion : availableVersion // ignore: cast_nullable_to_non_nullable
as String?,currentVersion: freezed == currentVersion ? _self.currentVersion : currentVersion // ignore: cast_nullable_to_non_nullable
as String?,failure: freezed == failure ? _self.failure : failure // ignore: cast_nullable_to_non_nullable
as Failure?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isChecking: null == isChecking ? _self.isChecking : isChecking // ignore: cast_nullable_to_non_nullable
as bool,isDownloading: null == isDownloading ? _self.isDownloading : isDownloading // ignore: cast_nullable_to_non_nullable
as bool,isInstalling: null == isInstalling ? _self.isInstalling : isInstalling // ignore: cast_nullable_to_non_nullable
as bool,isMandatory: null == isMandatory ? _self.isMandatory : isMandatory // ignore: cast_nullable_to_non_nullable
as bool,lastCheckTime: freezed == lastCheckTime ? _self.lastCheckTime : lastCheckTime // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}
/// Create a copy of UpdateState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FailureCopyWith<$Res>? get failure {
    if (_self.failure == null) {
    return null;
  }

  return $FailureCopyWith<$Res>(_self.failure!, (value) {
    return _then(_self.copyWith(failure: value));
  });
}
}


/// @nodoc


class _UpdateState extends UpdateState {
  const _UpdateState({this.status = UpdateStatus.initial, this.updateType, this.downloadProgress = 0.0, this.installProgress = 0.0, this.availableVersion, this.currentVersion, this.failure, this.errorMessage, this.isChecking = false, this.isDownloading = false, this.isInstalling = false, this.isMandatory = false, this.lastCheckTime}): super._();
  

/// Current status of the update process
@override@JsonKey() final  UpdateStatus status;
/// Type of update (flexible or immediate)
@override final  UpdateType? updateType;
/// Download progress (0.0 to 1.0)
@override@JsonKey() final  double downloadProgress;
/// Installation progress (0.0 to 1.0)
@override@JsonKey() final  double installProgress;
/// Available version string
@override final  String? availableVersion;
/// Current app version string
@override final  String? currentVersion;
/// Error information if update fails
@override final  Failure? failure;
/// Additional error message
@override final  String? errorMessage;
/// Whether update check is in progress
@override@JsonKey() final  bool isChecking;
/// Whether download is in progress
@override@JsonKey() final  bool isDownloading;
/// Whether installation is in progress
@override@JsonKey() final  bool isInstalling;
/// Whether update is mandatory (blocks app usage)
@override@JsonKey() final  bool isMandatory;
/// Timestamp of last update check
@override final  DateTime? lastCheckTime;

/// Create a copy of UpdateState
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateStateCopyWith<_UpdateState> get copyWith => __$UpdateStateCopyWithImpl<_UpdateState>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateState&&(identical(other.status, status) || other.status == status)&&(identical(other.updateType, updateType) || other.updateType == updateType)&&(identical(other.downloadProgress, downloadProgress) || other.downloadProgress == downloadProgress)&&(identical(other.installProgress, installProgress) || other.installProgress == installProgress)&&(identical(other.availableVersion, availableVersion) || other.availableVersion == availableVersion)&&(identical(other.currentVersion, currentVersion) || other.currentVersion == currentVersion)&&(identical(other.failure, failure) || other.failure == failure)&&(identical(other.errorMessage, errorMessage) || other.errorMessage == errorMessage)&&(identical(other.isChecking, isChecking) || other.isChecking == isChecking)&&(identical(other.isDownloading, isDownloading) || other.isDownloading == isDownloading)&&(identical(other.isInstalling, isInstalling) || other.isInstalling == isInstalling)&&(identical(other.isMandatory, isMandatory) || other.isMandatory == isMandatory)&&(identical(other.lastCheckTime, lastCheckTime) || other.lastCheckTime == lastCheckTime));
}


@override
int get hashCode => Object.hash(runtimeType,status,updateType,downloadProgress,installProgress,availableVersion,currentVersion,failure,errorMessage,isChecking,isDownloading,isInstalling,isMandatory,lastCheckTime);

@override
String toString() {
  return 'UpdateState(status: $status, updateType: $updateType, downloadProgress: $downloadProgress, installProgress: $installProgress, availableVersion: $availableVersion, currentVersion: $currentVersion, failure: $failure, errorMessage: $errorMessage, isChecking: $isChecking, isDownloading: $isDownloading, isInstalling: $isInstalling, isMandatory: $isMandatory, lastCheckTime: $lastCheckTime)';
}


}

/// @nodoc
abstract mixin class _$UpdateStateCopyWith<$Res> implements $UpdateStateCopyWith<$Res> {
  factory _$UpdateStateCopyWith(_UpdateState value, $Res Function(_UpdateState) _then) = __$UpdateStateCopyWithImpl;
@override @useResult
$Res call({
 UpdateStatus status, UpdateType? updateType, double downloadProgress, double installProgress, String? availableVersion, String? currentVersion, Failure? failure, String? errorMessage, bool isChecking, bool isDownloading, bool isInstalling, bool isMandatory, DateTime? lastCheckTime
});


@override $FailureCopyWith<$Res>? get failure;

}
/// @nodoc
class __$UpdateStateCopyWithImpl<$Res>
    implements _$UpdateStateCopyWith<$Res> {
  __$UpdateStateCopyWithImpl(this._self, this._then);

  final _UpdateState _self;
  final $Res Function(_UpdateState) _then;

/// Create a copy of UpdateState
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? status = null,Object? updateType = freezed,Object? downloadProgress = null,Object? installProgress = null,Object? availableVersion = freezed,Object? currentVersion = freezed,Object? failure = freezed,Object? errorMessage = freezed,Object? isChecking = null,Object? isDownloading = null,Object? isInstalling = null,Object? isMandatory = null,Object? lastCheckTime = freezed,}) {
  return _then(_UpdateState(
status: null == status ? _self.status : status // ignore: cast_nullable_to_non_nullable
as UpdateStatus,updateType: freezed == updateType ? _self.updateType : updateType // ignore: cast_nullable_to_non_nullable
as UpdateType?,downloadProgress: null == downloadProgress ? _self.downloadProgress : downloadProgress // ignore: cast_nullable_to_non_nullable
as double,installProgress: null == installProgress ? _self.installProgress : installProgress // ignore: cast_nullable_to_non_nullable
as double,availableVersion: freezed == availableVersion ? _self.availableVersion : availableVersion // ignore: cast_nullable_to_non_nullable
as String?,currentVersion: freezed == currentVersion ? _self.currentVersion : currentVersion // ignore: cast_nullable_to_non_nullable
as String?,failure: freezed == failure ? _self.failure : failure // ignore: cast_nullable_to_non_nullable
as Failure?,errorMessage: freezed == errorMessage ? _self.errorMessage : errorMessage // ignore: cast_nullable_to_non_nullable
as String?,isChecking: null == isChecking ? _self.isChecking : isChecking // ignore: cast_nullable_to_non_nullable
as bool,isDownloading: null == isDownloading ? _self.isDownloading : isDownloading // ignore: cast_nullable_to_non_nullable
as bool,isInstalling: null == isInstalling ? _self.isInstalling : isInstalling // ignore: cast_nullable_to_non_nullable
as bool,isMandatory: null == isMandatory ? _self.isMandatory : isMandatory // ignore: cast_nullable_to_non_nullable
as bool,lastCheckTime: freezed == lastCheckTime ? _self.lastCheckTime : lastCheckTime // ignore: cast_nullable_to_non_nullable
as DateTime?,
  ));
}

/// Create a copy of UpdateState
/// with the given fields replaced by the non-null parameter values.
@override
@pragma('vm:prefer-inline')
$FailureCopyWith<$Res>? get failure {
    if (_self.failure == null) {
    return null;
  }

  return $FailureCopyWith<$Res>(_self.failure!, (value) {
    return _then(_self.copyWith(failure: value));
  });
}
}

// dart format on

// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'update_info.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$UpdateInfo {

/// Whether an update is available
 bool get isAvailable;/// Type of update (flexible or immediate)
 UpdateType get updateType;/// Available version string from Play Store
 String? get availableVersion;/// Current installed version
 String? get currentVersion;/// Version code of available update
 int? get availableVersionCode;/// Current version code
 int? get currentVersionCode;/// Size of the update in bytes
 int? get updateSizeBytes;/// Priority of the update (0-5, where 5 is highest priority)
 int get updatePriority;/// Whether this update is mandatory
 bool get isMandatory;/// Release notes or update description
 String? get releaseNotes;/// Minimum version code that requires immediate update
 int? get minimumRequiredVersionCode;/// Timestamp when update was published
 DateTime? get publishedDate;/// Whether the update is compatible with current device
 bool get isCompatible;/// Additional metadata about the update
 Map<String, dynamic>? get metadata;
/// Create a copy of UpdateInfo
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateInfoCopyWith<UpdateInfo> get copyWith => _$UpdateInfoCopyWithImpl<UpdateInfo>(this as UpdateInfo, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateInfo&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.updateType, updateType) || other.updateType == updateType)&&(identical(other.availableVersion, availableVersion) || other.availableVersion == availableVersion)&&(identical(other.currentVersion, currentVersion) || other.currentVersion == currentVersion)&&(identical(other.availableVersionCode, availableVersionCode) || other.availableVersionCode == availableVersionCode)&&(identical(other.currentVersionCode, currentVersionCode) || other.currentVersionCode == currentVersionCode)&&(identical(other.updateSizeBytes, updateSizeBytes) || other.updateSizeBytes == updateSizeBytes)&&(identical(other.updatePriority, updatePriority) || other.updatePriority == updatePriority)&&(identical(other.isMandatory, isMandatory) || other.isMandatory == isMandatory)&&(identical(other.releaseNotes, releaseNotes) || other.releaseNotes == releaseNotes)&&(identical(other.minimumRequiredVersionCode, minimumRequiredVersionCode) || other.minimumRequiredVersionCode == minimumRequiredVersionCode)&&(identical(other.publishedDate, publishedDate) || other.publishedDate == publishedDate)&&(identical(other.isCompatible, isCompatible) || other.isCompatible == isCompatible)&&const DeepCollectionEquality().equals(other.metadata, metadata));
}


@override
int get hashCode => Object.hash(runtimeType,isAvailable,updateType,availableVersion,currentVersion,availableVersionCode,currentVersionCode,updateSizeBytes,updatePriority,isMandatory,releaseNotes,minimumRequiredVersionCode,publishedDate,isCompatible,const DeepCollectionEquality().hash(metadata));

@override
String toString() {
  return 'UpdateInfo(isAvailable: $isAvailable, updateType: $updateType, availableVersion: $availableVersion, currentVersion: $currentVersion, availableVersionCode: $availableVersionCode, currentVersionCode: $currentVersionCode, updateSizeBytes: $updateSizeBytes, updatePriority: $updatePriority, isMandatory: $isMandatory, releaseNotes: $releaseNotes, minimumRequiredVersionCode: $minimumRequiredVersionCode, publishedDate: $publishedDate, isCompatible: $isCompatible, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class $UpdateInfoCopyWith<$Res>  {
  factory $UpdateInfoCopyWith(UpdateInfo value, $Res Function(UpdateInfo) _then) = _$UpdateInfoCopyWithImpl;
@useResult
$Res call({
 bool isAvailable, UpdateType updateType, String? availableVersion, String? currentVersion, int? availableVersionCode, int? currentVersionCode, int? updateSizeBytes, int updatePriority, bool isMandatory, String? releaseNotes, int? minimumRequiredVersionCode, DateTime? publishedDate, bool isCompatible, Map<String, dynamic>? metadata
});




}
/// @nodoc
class _$UpdateInfoCopyWithImpl<$Res>
    implements $UpdateInfoCopyWith<$Res> {
  _$UpdateInfoCopyWithImpl(this._self, this._then);

  final UpdateInfo _self;
  final $Res Function(UpdateInfo) _then;

/// Create a copy of UpdateInfo
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') @override $Res call({Object? isAvailable = null,Object? updateType = null,Object? availableVersion = freezed,Object? currentVersion = freezed,Object? availableVersionCode = freezed,Object? currentVersionCode = freezed,Object? updateSizeBytes = freezed,Object? updatePriority = null,Object? isMandatory = null,Object? releaseNotes = freezed,Object? minimumRequiredVersionCode = freezed,Object? publishedDate = freezed,Object? isCompatible = null,Object? metadata = freezed,}) {
  return _then(_self.copyWith(
isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,updateType: null == updateType ? _self.updateType : updateType // ignore: cast_nullable_to_non_nullable
as UpdateType,availableVersion: freezed == availableVersion ? _self.availableVersion : availableVersion // ignore: cast_nullable_to_non_nullable
as String?,currentVersion: freezed == currentVersion ? _self.currentVersion : currentVersion // ignore: cast_nullable_to_non_nullable
as String?,availableVersionCode: freezed == availableVersionCode ? _self.availableVersionCode : availableVersionCode // ignore: cast_nullable_to_non_nullable
as int?,currentVersionCode: freezed == currentVersionCode ? _self.currentVersionCode : currentVersionCode // ignore: cast_nullable_to_non_nullable
as int?,updateSizeBytes: freezed == updateSizeBytes ? _self.updateSizeBytes : updateSizeBytes // ignore: cast_nullable_to_non_nullable
as int?,updatePriority: null == updatePriority ? _self.updatePriority : updatePriority // ignore: cast_nullable_to_non_nullable
as int,isMandatory: null == isMandatory ? _self.isMandatory : isMandatory // ignore: cast_nullable_to_non_nullable
as bool,releaseNotes: freezed == releaseNotes ? _self.releaseNotes : releaseNotes // ignore: cast_nullable_to_non_nullable
as String?,minimumRequiredVersionCode: freezed == minimumRequiredVersionCode ? _self.minimumRequiredVersionCode : minimumRequiredVersionCode // ignore: cast_nullable_to_non_nullable
as int?,publishedDate: freezed == publishedDate ? _self.publishedDate : publishedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isCompatible: null == isCompatible ? _self.isCompatible : isCompatible // ignore: cast_nullable_to_non_nullable
as bool,metadata: freezed == metadata ? _self.metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}

}


/// @nodoc


class _UpdateInfo extends UpdateInfo {
  const _UpdateInfo({required this.isAvailable, required this.updateType, this.availableVersion, this.currentVersion, this.availableVersionCode, this.currentVersionCode, this.updateSizeBytes, this.updatePriority = 0, this.isMandatory = false, this.releaseNotes, this.minimumRequiredVersionCode, this.publishedDate, this.isCompatible = true, final  Map<String, dynamic>? metadata}): _metadata = metadata,super._();
  

/// Whether an update is available
@override final  bool isAvailable;
/// Type of update (flexible or immediate)
@override final  UpdateType updateType;
/// Available version string from Play Store
@override final  String? availableVersion;
/// Current installed version
@override final  String? currentVersion;
/// Version code of available update
@override final  int? availableVersionCode;
/// Current version code
@override final  int? currentVersionCode;
/// Size of the update in bytes
@override final  int? updateSizeBytes;
/// Priority of the update (0-5, where 5 is highest priority)
@override@JsonKey() final  int updatePriority;
/// Whether this update is mandatory
@override@JsonKey() final  bool isMandatory;
/// Release notes or update description
@override final  String? releaseNotes;
/// Minimum version code that requires immediate update
@override final  int? minimumRequiredVersionCode;
/// Timestamp when update was published
@override final  DateTime? publishedDate;
/// Whether the update is compatible with current device
@override@JsonKey() final  bool isCompatible;
/// Additional metadata about the update
 final  Map<String, dynamic>? _metadata;
/// Additional metadata about the update
@override Map<String, dynamic>? get metadata {
  final value = _metadata;
  if (value == null) return null;
  if (_metadata is EqualUnmodifiableMapView) return _metadata;
  // ignore: implicit_dynamic_type
  return EqualUnmodifiableMapView(value);
}


/// Create a copy of UpdateInfo
/// with the given fields replaced by the non-null parameter values.
@override @JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
_$UpdateInfoCopyWith<_UpdateInfo> get copyWith => __$UpdateInfoCopyWithImpl<_UpdateInfo>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is _UpdateInfo&&(identical(other.isAvailable, isAvailable) || other.isAvailable == isAvailable)&&(identical(other.updateType, updateType) || other.updateType == updateType)&&(identical(other.availableVersion, availableVersion) || other.availableVersion == availableVersion)&&(identical(other.currentVersion, currentVersion) || other.currentVersion == currentVersion)&&(identical(other.availableVersionCode, availableVersionCode) || other.availableVersionCode == availableVersionCode)&&(identical(other.currentVersionCode, currentVersionCode) || other.currentVersionCode == currentVersionCode)&&(identical(other.updateSizeBytes, updateSizeBytes) || other.updateSizeBytes == updateSizeBytes)&&(identical(other.updatePriority, updatePriority) || other.updatePriority == updatePriority)&&(identical(other.isMandatory, isMandatory) || other.isMandatory == isMandatory)&&(identical(other.releaseNotes, releaseNotes) || other.releaseNotes == releaseNotes)&&(identical(other.minimumRequiredVersionCode, minimumRequiredVersionCode) || other.minimumRequiredVersionCode == minimumRequiredVersionCode)&&(identical(other.publishedDate, publishedDate) || other.publishedDate == publishedDate)&&(identical(other.isCompatible, isCompatible) || other.isCompatible == isCompatible)&&const DeepCollectionEquality().equals(other._metadata, _metadata));
}


@override
int get hashCode => Object.hash(runtimeType,isAvailable,updateType,availableVersion,currentVersion,availableVersionCode,currentVersionCode,updateSizeBytes,updatePriority,isMandatory,releaseNotes,minimumRequiredVersionCode,publishedDate,isCompatible,const DeepCollectionEquality().hash(_metadata));

@override
String toString() {
  return 'UpdateInfo(isAvailable: $isAvailable, updateType: $updateType, availableVersion: $availableVersion, currentVersion: $currentVersion, availableVersionCode: $availableVersionCode, currentVersionCode: $currentVersionCode, updateSizeBytes: $updateSizeBytes, updatePriority: $updatePriority, isMandatory: $isMandatory, releaseNotes: $releaseNotes, minimumRequiredVersionCode: $minimumRequiredVersionCode, publishedDate: $publishedDate, isCompatible: $isCompatible, metadata: $metadata)';
}


}

/// @nodoc
abstract mixin class _$UpdateInfoCopyWith<$Res> implements $UpdateInfoCopyWith<$Res> {
  factory _$UpdateInfoCopyWith(_UpdateInfo value, $Res Function(_UpdateInfo) _then) = __$UpdateInfoCopyWithImpl;
@override @useResult
$Res call({
 bool isAvailable, UpdateType updateType, String? availableVersion, String? currentVersion, int? availableVersionCode, int? currentVersionCode, int? updateSizeBytes, int updatePriority, bool isMandatory, String? releaseNotes, int? minimumRequiredVersionCode, DateTime? publishedDate, bool isCompatible, Map<String, dynamic>? metadata
});




}
/// @nodoc
class __$UpdateInfoCopyWithImpl<$Res>
    implements _$UpdateInfoCopyWith<$Res> {
  __$UpdateInfoCopyWithImpl(this._self, this._then);

  final _UpdateInfo _self;
  final $Res Function(_UpdateInfo) _then;

/// Create a copy of UpdateInfo
/// with the given fields replaced by the non-null parameter values.
@override @pragma('vm:prefer-inline') $Res call({Object? isAvailable = null,Object? updateType = null,Object? availableVersion = freezed,Object? currentVersion = freezed,Object? availableVersionCode = freezed,Object? currentVersionCode = freezed,Object? updateSizeBytes = freezed,Object? updatePriority = null,Object? isMandatory = null,Object? releaseNotes = freezed,Object? minimumRequiredVersionCode = freezed,Object? publishedDate = freezed,Object? isCompatible = null,Object? metadata = freezed,}) {
  return _then(_UpdateInfo(
isAvailable: null == isAvailable ? _self.isAvailable : isAvailable // ignore: cast_nullable_to_non_nullable
as bool,updateType: null == updateType ? _self.updateType : updateType // ignore: cast_nullable_to_non_nullable
as UpdateType,availableVersion: freezed == availableVersion ? _self.availableVersion : availableVersion // ignore: cast_nullable_to_non_nullable
as String?,currentVersion: freezed == currentVersion ? _self.currentVersion : currentVersion // ignore: cast_nullable_to_non_nullable
as String?,availableVersionCode: freezed == availableVersionCode ? _self.availableVersionCode : availableVersionCode // ignore: cast_nullable_to_non_nullable
as int?,currentVersionCode: freezed == currentVersionCode ? _self.currentVersionCode : currentVersionCode // ignore: cast_nullable_to_non_nullable
as int?,updateSizeBytes: freezed == updateSizeBytes ? _self.updateSizeBytes : updateSizeBytes // ignore: cast_nullable_to_non_nullable
as int?,updatePriority: null == updatePriority ? _self.updatePriority : updatePriority // ignore: cast_nullable_to_non_nullable
as int,isMandatory: null == isMandatory ? _self.isMandatory : isMandatory // ignore: cast_nullable_to_non_nullable
as bool,releaseNotes: freezed == releaseNotes ? _self.releaseNotes : releaseNotes // ignore: cast_nullable_to_non_nullable
as String?,minimumRequiredVersionCode: freezed == minimumRequiredVersionCode ? _self.minimumRequiredVersionCode : minimumRequiredVersionCode // ignore: cast_nullable_to_non_nullable
as int?,publishedDate: freezed == publishedDate ? _self.publishedDate : publishedDate // ignore: cast_nullable_to_non_nullable
as DateTime?,isCompatible: null == isCompatible ? _self.isCompatible : isCompatible // ignore: cast_nullable_to_non_nullable
as bool,metadata: freezed == metadata ? _self._metadata : metadata // ignore: cast_nullable_to_non_nullable
as Map<String, dynamic>?,
  ));
}


}

// dart format on

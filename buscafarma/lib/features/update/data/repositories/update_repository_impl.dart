import 'dart:async';
import 'dart:io';
import 'package:dartz/dartz.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:in_app_update/in_app_update.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:buscafarma/core/error/failures.dart';
import 'package:buscafarma/features/update/domain/entities/update_info.dart';
import 'package:buscafarma/features/update/domain/entities/update_state.dart';
import 'package:buscafarma/features/update/domain/repositories/update_repository.dart';

/// Concrete implementation of UpdateRepository using in_app_update package
/// Fixed to work with the actual in_app_update API
class UpdateRepositoryImpl implements UpdateRepository {
  // Stream controller for update state changes
  final StreamController<UpdateState> _updateStateController = StreamController<UpdateState>.broadcast();

  // Current update state
  UpdateState _currentState = const UpdateState();

  @override
  Stream<UpdateState> get updateStateStream => _updateStateController.stream;

  @override
  Future<Either<Failure, UpdateInfo>> checkForUpdate() async {
    try {
      _emitState(_currentState.copyWith(status: UpdateStatus.checking, isChecking: true, lastCheckTime: DateTime.now()));

      // Check if platform supports in-app updates
      if (!Platform.isAndroid) {
        _emitState(_currentState.copyWith(status: UpdateStatus.noUpdateAvailable, isChecking: false));
        return Right(const UpdateInfo(isAvailable: false, updateType: UpdateType.flexible));
      }

      // Get current app version info
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      final currentVersionCode = int.tryParse(packageInfo.buildNumber) ?? 0;

      // Check for update availability using correct API
      final appUpdateInfo = await InAppUpdate.checkForUpdate();

      if (appUpdateInfo.updateAvailability == UpdateAvailability.updateAvailable) {
        // Determine update type based on priority and staleness
        final updateType = _determineUpdateType(appUpdateInfo);
        final isMandatory = updateType == UpdateType.immediate;

        final info = UpdateInfo(
          isAvailable: true,
          updateType: updateType,
          availableVersion: appUpdateInfo.availableVersionCode?.toString(),
          currentVersion: currentVersion,
          availableVersionCode: appUpdateInfo.availableVersionCode,
          currentVersionCode: currentVersionCode,
          updatePriority: appUpdateInfo.updatePriority,
          isMandatory: isMandatory,
          publishedDate: DateTime.now(),
        );

        _emitState(_currentState.copyWith(status: UpdateStatus.updateAvailable, updateType: updateType, availableVersion: info.availableVersion, currentVersion: currentVersion, isMandatory: isMandatory, isChecking: false));

        return Right(info);
      } else {
        _emitState(_currentState.copyWith(status: UpdateStatus.noUpdateAvailable, currentVersion: currentVersion, isChecking: false));

        return Right(UpdateInfo(isAvailable: false, updateType: UpdateType.flexible, currentVersion: currentVersion, currentVersionCode: currentVersionCode));
      }
    } catch (e) {
      debugPrint('Error checking for update: $e');

      final failure = _mapExceptionToFailure(e);
      _emitState(_currentState.copyWith(status: UpdateStatus.failed, failure: failure, errorMessage: e.toString(), isChecking: false));

      return Left(failure);
    }
  }

  @override
  Future<Either<Failure, void>> startUpdate(UpdateType updateType) async {
    try {
      if (!Platform.isAndroid) {
        return Left(const Failure.updateNotAvailable('In-app updates not supported on this platform'));
      }

      _emitState(_currentState.copyWith(status: UpdateStatus.downloading, updateType: updateType, isDownloading: true, downloadProgress: 0.0));

      // Start the appropriate update flow
      if (updateType == UpdateType.immediate) {
        // For immediate updates, the app will restart automatically
        final result = await InAppUpdate.performImmediateUpdate();

        // If we reach here, check the result
        if (result == AppUpdateResult.success) {
          _emitState(_currentState.copyWith(status: UpdateStatus.installCompleted, isDownloading: false, isInstalling: false, downloadProgress: 1.0, installProgress: 1.0));
        } else {
          _emitState(_currentState.copyWith(status: UpdateStatus.failed, errorMessage: 'Immediate update failed', isDownloading: false));
          return Left(const Failure.updateInstallationFailed('Immediate update failed'));
        }
      } else {
        // For flexible updates, start the download
        final result = await InAppUpdate.startFlexibleUpdate();

        if (result == AppUpdateResult.success) {
          // Update started successfully
          _emitState(_currentState.copyWith(status: UpdateStatus.downloadCompleted, downloadProgress: 1.0, isDownloading: false));
        } else {
          _emitState(_currentState.copyWith(status: UpdateStatus.failed, errorMessage: 'Failed to start flexible update', isDownloading: false));
          return Left(const Failure.updateDownloadFailed('Failed to start flexible update'));
        }
      }

      return const Right(null);
    } catch (e) {
      debugPrint('Error starting update: $e');

      final failure = _mapExceptionToFailure(e);
      _emitState(_currentState.copyWith(status: UpdateStatus.failed, failure: failure, errorMessage: e.toString(), isDownloading: false));

      return Left(failure);
    }
  }

  @override
  Future<Either<Failure, double>> getDownloadProgress() async {
    try {
      // Return current progress from state
      return Right(_currentState.downloadProgress);
    } catch (e) {
      debugPrint('Error getting download progress: $e');
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, bool>> isUpdateReadyToInstall() async {
    try {
      // Check if we have a flexible update ready
      if (_currentState.updateType == UpdateType.flexible) {
        return Right(_currentState.status == UpdateStatus.downloadCompleted);
      }

      return Right(false);
    } catch (e) {
      debugPrint('Error checking if update is ready: $e');
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> completeUpdate() async {
    try {
      if (_currentState.updateType == UpdateType.flexible) {
        _emitState(_currentState.copyWith(status: UpdateStatus.installing, isInstalling: true));

        await InAppUpdate.completeFlexibleUpdate();

        // If we reach here, the update completed successfully
        _emitState(_currentState.copyWith(status: UpdateStatus.installCompleted, isInstalling: false, installProgress: 1.0));
      }

      return const Right(null);
    } catch (e) {
      debugPrint('Error completing update: $e');

      final failure = _mapExceptionToFailure(e);
      _emitState(_currentState.copyWith(status: UpdateStatus.failed, failure: failure, errorMessage: e.toString(), isInstalling: false));

      return Left(failure);
    }
  }

  @override
  Future<Either<Failure, void>> cancelUpdate() async {
    try {
      _emitState(_currentState.copyWith(status: UpdateStatus.cancelled, isDownloading: false, isInstalling: false));

      return const Right(null);
    } catch (e) {
      debugPrint('Error cancelling update: $e');
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, Map<String, dynamic>>> getCurrentVersionInfo() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();

      return Right({'version': packageInfo.version, 'buildNumber': packageInfo.buildNumber, 'appName': packageInfo.appName, 'packageName': packageInfo.packageName});
    } catch (e) {
      debugPrint('Error getting version info: $e');
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, bool>> isInAppUpdateSupported() async {
    try {
      // In-app updates are only supported on Android
      return Right(Platform.isAndroid);
    } catch (e) {
      debugPrint('Error checking in-app update support: $e');
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  Future<Either<Failure, void>> openPlayStore() async {
    try {
      const playStoreUrl = 'market://details?id=com.buscafarma.app';
      const webUrl = 'https://play.google.com/store/apps/details?id=com.buscafarma.app';

      if (await canLaunchUrl(Uri.parse(playStoreUrl))) {
        await launchUrl(Uri.parse(playStoreUrl));
      } else {
        await launchUrl(Uri.parse(webUrl), mode: LaunchMode.externalApplication);
      }

      return const Right(null);
    } catch (e) {
      debugPrint('Error opening Play Store: $e');
      return Left(_mapExceptionToFailure(e));
    }
  }

  @override
  void dispose() {
    _updateStateController.close();
  }

  // Private helper methods

  void _emitState(UpdateState newState) {
    _currentState = newState;
    _updateStateController.add(newState);
  }

  UpdateType _determineUpdateType(AppUpdateInfo updateInfo) {
    // Determine if update should be immediate based on priority and staleness
    final priority = updateInfo.updatePriority;
    final staleness = updateInfo.clientVersionStalenessDays ?? 0;

    // Force immediate update for high priority or very stale versions
    if (priority >= 4 || staleness >= 30) {
      return UpdateType.immediate;
    }

    return UpdateType.flexible;
  }

  Failure _mapExceptionToFailure(dynamic exception) {
    if (exception is PlatformException) {
      switch (exception.code) {
        case 'UPDATE_NOT_AVAILABLE':
          return const Failure.updateNotAvailable('No update available');
        case 'UPDATE_FAILED':
          return Failure.updateDownloadFailed(exception.message);
        case 'INSTALL_FAILED':
          return Failure.updateInstallationFailed(exception.message);
        default:
          return Failure.updateCheckFailed(exception.message);
      }
    }

    if (exception is SocketException) {
      return const Failure.networkError();
    }

    return Failure.unexpectedError(exception.toString());
  }
}

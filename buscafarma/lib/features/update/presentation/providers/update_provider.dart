import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/update/domain/entities/update_state.dart';
import 'package:buscafarma/features/update/domain/repositories/update_repository.dart';
import 'package:buscafarma/features/update/data/repositories/update_repository_impl.dart';

/// Provider for the update repository
final updateRepositoryProvider = Provider<UpdateRepository>((ref) {
  return UpdateRepositoryImpl();
});

/// StateNotifier for managing update lifecycle and state
class UpdateNotifier extends StateNotifier<UpdateState> {
  final UpdateRepository _repository;
  StreamSubscription<UpdateState>? _stateSubscription;

  UpdateNotifier(this._repository) : super(const UpdateState()) {
    // Listen to repository state changes
    _stateSubscription = _repository.updateStateStream.listen((newState) {
      state = newState;
    });
  }

  /// Check for available app updates
  Future<void> checkForUpdate() async {
    if (!state.canCheckForUpdate) {
      debugPrint('Cannot check for update in current state: ${state.status}');
      return;
    }

    final result = await _repository.checkForUpdate();

    result.fold(
      (failure) {
        debugPrint('Update check failed: $failure');
        // State is already updated by repository
      },
      (updateInfo) {
        debugPrint('Update check completed. Available: ${updateInfo.isAvailable}');
        // State is already updated by repository
      },
    );
  }

  /// Start downloading and installing an update
  Future<void> startUpdate({UpdateType? updateType}) async {
    if (!state.canStartDownload) {
      debugPrint('Cannot start update in current state: ${state.status}');
      return;
    }

    final type = updateType ?? state.updateType ?? UpdateType.flexible;

    final result = await _repository.startUpdate(type);

    result.fold(
      (failure) {
        debugPrint('Failed to start update: $failure');
        // State is already updated by repository
      },
      (_) {
        debugPrint('Update started successfully with type: $type');
        // State is already updated by repository
      },
    );
  }

  /// Complete a flexible update installation
  Future<void> completeUpdate() async {
    if (!state.canStartInstall) {
      debugPrint('Cannot complete update in current state: ${state.status}');
      return;
    }

    final result = await _repository.completeUpdate();

    result.fold(
      (failure) {
        debugPrint('Failed to complete update: $failure');
        // State is already updated by repository
      },
      (_) {
        debugPrint('Update completed successfully');
        // State is already updated by repository
      },
    );
  }

  /// Cancel an ongoing update
  Future<void> cancelUpdate() async {
    if (!state.isInProgress) {
      debugPrint('No update in progress to cancel');
      return;
    }

    final result = await _repository.cancelUpdate();

    result.fold(
      (failure) {
        debugPrint('Failed to cancel update: $failure');
      },
      (_) {
        debugPrint('Update cancelled successfully');
        // State is already updated by repository
      },
    );
  }

  /// Open Play Store as fallback when in-app update fails
  Future<void> openPlayStore() async {
    final result = await _repository.openPlayStore();

    result.fold(
      (failure) {
        debugPrint('Failed to open Play Store: $failure');
      },
      (_) {
        debugPrint('Play Store opened successfully');
      },
    );
  }

  /// Get current app version information
  Future<Map<String, dynamic>?> getCurrentVersionInfo() async {
    final result = await _repository.getCurrentVersionInfo();

    return result.fold((failure) {
      debugPrint('Failed to get version info: $failure');
      return null;
    }, (versionInfo) => versionInfo);
  }

  /// Check if in-app updates are supported on current device
  Future<bool> isInAppUpdateSupported() async {
    final result = await _repository.isInAppUpdateSupported();

    return result.fold((failure) {
      debugPrint('Failed to check in-app update support: $failure');
      return false;
    }, (isSupported) => isSupported);
  }

  /// Reset update state to initial
  void resetState() {
    state = const UpdateState();
  }

  /// Clear any error state
  void clearError() {
    if (state.hasFailed) {
      state = state.copyWith(status: UpdateStatus.initial, failure: null, errorMessage: null);
    }
  }

  /// Force refresh of update availability
  Future<void> refreshUpdateStatus() async {
    resetState();
    await checkForUpdate();
  }

  @override
  void dispose() {
    _stateSubscription?.cancel();
    _repository.dispose();
    super.dispose();
  }
}

/// Provider for update state management
final updateProvider = StateNotifierProvider<UpdateNotifier, UpdateState>((ref) {
  final repository = ref.watch(updateRepositoryProvider);
  return UpdateNotifier(repository);
});

/// Convenience provider to check if update is available
final hasUpdateAvailableProvider = Provider<bool>((ref) {
  final updateState = ref.watch(updateProvider);
  return updateState.hasUpdateAvailable;
});

/// Convenience provider to check if update is mandatory
final isMandatoryUpdateProvider = Provider<bool>((ref) {
  final updateState = ref.watch(updateProvider);
  return updateState.isMandatory;
});

/// Convenience provider to get update progress
final updateProgressProvider = Provider<double>((ref) {
  final updateState = ref.watch(updateProvider);
  return updateState.overallProgress;
});

/// Convenience provider to check if update is in progress
final isUpdateInProgressProvider = Provider<bool>((ref) {
  final updateState = ref.watch(updateProvider);
  return updateState.isInProgress;
});

/// Provider for update status message
final updateStatusMessageProvider = Provider<String>((ref) {
  final updateState = ref.watch(updateProvider);
  return updateState.statusMessage;
});

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/update/domain/entities/update_state.dart';
import 'package:buscafarma/features/update/presentation/providers/update_provider.dart';
import 'package:buscafarma/features/update/presentation/services/update_service.dart';
import 'package:buscafarma/core/widgets/animated_loading_indicator.dart';
import 'package:buscafarma/core/animations/animation_constants.dart';

/// Material Design 3 compliant mandatory update dialog
/// This dialog cannot be dismissed and blocks app usage until update is completed
class MandatoryUpdateDialog extends ConsumerStatefulWidget {
  const MandatoryUpdateDialog({super.key});

  @override
  ConsumerState<MandatoryUpdateDialog> createState() => _MandatoryUpdateDialogState();
}

class _MandatoryUpdateDialogState extends ConsumerState<MandatoryUpdateDialog> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animations
    _animationController = AnimationController(duration: AnimationConstants.stateTransitionDuration, vsync: this);

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(CurvedAnimation(parent: _animationController, curve: AnimationConstants.entranceCurve));

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(parent: _animationController, curve: AnimationConstants.entranceCurve));

    // Start entrance animation
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final updateState = ref.watch(updateProvider);

    return PopScope(
      canPop: false, // Prevent dismissing the dialog
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: AlertDialog(
            icon: Icon(Icons.system_update_rounded, size: 48, color: colorScheme.primary),
            title: Text('Actualización Requerida', style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600, color: colorScheme.onSurface), textAlign: TextAlign.center),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Update description
                  Text(_getUpdateDescription(updateState), style: theme.textTheme.bodyMedium?.copyWith(color: colorScheme.onSurfaceVariant), textAlign: TextAlign.center),

                  const SizedBox(height: 24),

                  // Progress section
                  if (updateState.isInProgress) ...[_buildProgressSection(context, updateState), const SizedBox(height: 16)],

                  // Status message
                  Text(updateState.statusMessage, style: theme.textTheme.bodySmall?.copyWith(color: colorScheme.onSurfaceVariant, fontWeight: FontWeight.w500), textAlign: TextAlign.center),

                  // Error message if any
                  if (updateState.hasFailed && updateState.errorMessage != null) ...[
                    const SizedBox(height: 12),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(color: colorScheme.errorContainer, borderRadius: BorderRadius.circular(8)),
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, size: 20, color: colorScheme.onErrorContainer),
                          const SizedBox(width: 8),
                          Expanded(child: Text(updateState.errorMessage!, style: theme.textTheme.bodySmall?.copyWith(color: colorScheme.onErrorContainer))),
                        ],
                      ),
                    ),
                  ],
                ],
              ),
            ),
            actions: _buildActions(context, updateState),
            actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
            actionsAlignment: MainAxisAlignment.spaceEvenly,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(28)),
            backgroundColor: colorScheme.surface,
            surfaceTintColor: colorScheme.surfaceTint,
          ),
        ),
      ),
    );
  }

  Widget _buildProgressSection(BuildContext context, UpdateState updateState) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Column(
      children: [
        // Progress indicator
        AnimatedLoadingIndicator(size: 40, color: colorScheme.primary, progress: updateState.overallProgress > 0 ? updateState.overallProgress : null, state: _getLoadingState(updateState)),

        const SizedBox(height: 12),

        // Progress bar for download/install
        if (updateState.overallProgress > 0) ...[
          LinearProgressIndicator(value: updateState.overallProgress, backgroundColor: colorScheme.surfaceContainerHighest, valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary)),
          const SizedBox(height: 8),
          Text('${(updateState.overallProgress * 100).toInt()}%', style: theme.textTheme.bodySmall?.copyWith(color: colorScheme.onSurfaceVariant, fontWeight: FontWeight.w500)),
        ],
      ],
    );
  }

  List<Widget> _buildActions(BuildContext context, UpdateState updateState) {
    final updateService = ref.read(updateServiceProvider);

    if (updateState.hasFailed) {
      return [
        // Retry button
        FilledButton.icon(onPressed: () => updateService.startImmediateUpdate(), icon: const Icon(Icons.refresh), label: const Text('Reintentar')),

        // Play Store fallback button
        TextButton.icon(onPressed: () => updateService.openPlayStoreForUpdate(), icon: const Icon(Icons.open_in_new), label: const Text('Abrir Play Store')),
      ];
    }

    if (updateState.hasUpdateAvailable && !updateState.isInProgress) {
      return [
        // Update now button
        FilledButton.icon(onPressed: () => updateService.startImmediateUpdate(), icon: const Icon(Icons.download), label: const Text('Actualizar Ahora')),
      ];
    }

    // No actions during progress or other states
    return [];
  }

  String _getUpdateDescription(UpdateState updateState) {
    if (updateState.hasUpdateAvailable) {
      final version = updateState.availableVersion;
      if (version != null) {
        return 'Una nueva versión ($version) está disponible. Esta actualización es obligatoria para continuar usando la aplicación.';
      }
      return 'Una nueva versión está disponible. Esta actualización es obligatoria para continuar usando la aplicación.';
    }

    if (updateState.isInProgress) {
      return 'Por favor espera mientras se descarga e instala la actualización. No cierres la aplicación durante este proceso.';
    }

    if (updateState.hasFailed) {
      return 'Ocurrió un error durante la actualización. Por favor intenta nuevamente o actualiza desde Play Store.';
    }

    return 'Verificando actualizaciones disponibles...';
  }

  LoadingState _getLoadingState(UpdateState updateState) {
    if (updateState.hasFailed) {
      return LoadingState.error;
    }

    if (updateState.isCompleted) {
      return LoadingState.success;
    }

    return LoadingState.loading;
  }
}

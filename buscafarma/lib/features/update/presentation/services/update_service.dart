import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/features/update/domain/entities/update_state.dart';
import 'package:buscafarma/features/update/presentation/providers/update_provider.dart';

/// High-level service for managing app updates
/// Provides simplified interface for common update operations
class UpdateService {
  final Ref _ref;

  UpdateService(this._ref);

  /// Check for updates and handle the result automatically
  /// Returns true if an update is available, false otherwise
  Future<bool> checkAndHandleUpdate({
    bool autoStartMandatory = true,
    bool showOptionalPrompt = false,
  }) async {
    try {
      final updateNotifier = _ref.read(updateProvider.notifier);
      
      // Check for updates
      await updateNotifier.checkForUpdate();
      
      final updateState = _ref.read(updateProvider);
      
      if (updateState.hasUpdateAvailable) {
        debugPrint('Update available: ${updateState.availableVersion}');
        
        // Handle mandatory updates automatically
        if (updateState.isMandatory && autoStartMandatory) {
          debugPrint('Starting mandatory update automatically');
          await updateNotifier.startUpdate(updateType: UpdateType.immediate);
          return true;
        }
        
        // For optional updates, just return true to let caller handle UI
        if (!updateState.isMandatory && showOptionalPrompt) {
          debugPrint('Optional update available, showing prompt');
          return true;
        }
        
        return true;
      }
      
      debugPrint('No update available');
      return false;
    } catch (e) {
      debugPrint('Error in checkAndHandleUpdate: $e');
      return false;
    }
  }

  /// Start an immediate update (mandatory)
  Future<bool> startImmediateUpdate() async {
    try {
      final updateNotifier = _ref.read(updateProvider.notifier);
      await updateNotifier.startUpdate(updateType: UpdateType.immediate);
      return true;
    } catch (e) {
      debugPrint('Error starting immediate update: $e');
      return false;
    }
  }

  /// Start a flexible update (optional)
  Future<bool> startFlexibleUpdate() async {
    try {
      final updateNotifier = _ref.read(updateProvider.notifier);
      await updateNotifier.startUpdate(updateType: UpdateType.flexible);
      return true;
    } catch (e) {
      debugPrint('Error starting flexible update: $e');
      return false;
    }
  }

  /// Complete a flexible update installation
  Future<bool> completeFlexibleUpdate() async {
    try {
      final updateNotifier = _ref.read(updateProvider.notifier);
      await updateNotifier.completeUpdate();
      return true;
    } catch (e) {
      debugPrint('Error completing flexible update: $e');
      return false;
    }
  }

  /// Cancel any ongoing update
  Future<bool> cancelUpdate() async {
    try {
      final updateNotifier = _ref.read(updateProvider.notifier);
      await updateNotifier.cancelUpdate();
      return true;
    } catch (e) {
      debugPrint('Error cancelling update: $e');
      return false;
    }
  }

  /// Open Play Store as fallback
  Future<bool> openPlayStoreForUpdate() async {
    try {
      final updateNotifier = _ref.read(updateProvider.notifier);
      await updateNotifier.openPlayStore();
      return true;
    } catch (e) {
      debugPrint('Error opening Play Store: $e');
      return false;
    }
  }

  /// Check if device supports in-app updates
  Future<bool> isUpdateSupported() async {
    try {
      final updateNotifier = _ref.read(updateProvider.notifier);
      return await updateNotifier.isInAppUpdateSupported();
    } catch (e) {
      debugPrint('Error checking update support: $e');
      return false;
    }
  }

  /// Get current app version information
  Future<Map<String, dynamic>?> getVersionInfo() async {
    try {
      final updateNotifier = _ref.read(updateProvider.notifier);
      return await updateNotifier.getCurrentVersionInfo();
    } catch (e) {
      debugPrint('Error getting version info: $e');
      return null;
    }
  }

  /// Reset update state
  void resetUpdateState() {
    final updateNotifier = _ref.read(updateProvider.notifier);
    updateNotifier.resetState();
  }

  /// Clear any error state
  void clearUpdateError() {
    final updateNotifier = _ref.read(updateProvider.notifier);
    updateNotifier.clearError();
  }

  /// Refresh update status
  Future<bool> refreshUpdateStatus() async {
    try {
      final updateNotifier = _ref.read(updateProvider.notifier);
      await updateNotifier.refreshUpdateStatus();
      
      final updateState = _ref.read(updateProvider);
      return updateState.hasUpdateAvailable;
    } catch (e) {
      debugPrint('Error refreshing update status: $e');
      return false;
    }
  }

  /// Get current update state
  UpdateState get currentState => _ref.read(updateProvider);

  /// Check if update is available
  bool get hasUpdateAvailable => _ref.read(hasUpdateAvailableProvider);

  /// Check if update is mandatory
  bool get isMandatoryUpdate => _ref.read(isMandatoryUpdateProvider);

  /// Get update progress
  double get updateProgress => _ref.read(updateProgressProvider);

  /// Check if update is in progress
  bool get isUpdateInProgress => _ref.read(isUpdateInProgressProvider);

  /// Get status message
  String get statusMessage => _ref.read(updateStatusMessageProvider);
}

/// Provider for UpdateService
final updateServiceProvider = Provider<UpdateService>((ref) {
  return UpdateService(ref);
});

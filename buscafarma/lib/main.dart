import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/router/app_router.dart';
import 'package:buscafarma/core/router/app_routes.dart';
import 'package:buscafarma/core/utils/app_constants.dart';
import 'package:buscafarma/core/providers/theme_provider.dart';
import 'package:buscafarma/core/providers/remote_config_provider.dart';
import 'package:buscafarma/features/map/presentation/providers/auto_location_provider.dart';
import 'package:buscafarma/features/map/presentation/providers/auto_location_initializer_provider.dart';
import 'package:buscafarma/features/review/presentation/providers/review_manager_provider.dart';
import 'package:buscafarma/features/update/presentation/providers/update_provider.dart';
import 'package:buscafarma/core/services/ad_service.dart';
import 'package:buscafarma/core/utils/api_config.dart';
import 'package:buscafarma/core/services/display_mode_service.dart';
import 'package:buscafarma/core/providers/display_mode_provider.dart';
import 'package:dynamic_color/dynamic_color.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:buscafarma/firebase_options.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);

  // Setup router before runApp
  AppRouter.setupRouter();
  runApp(const ProviderScope(child: BuscaFarmaApp()));
}

class BuscaFarmaApp extends ConsumerStatefulWidget {
  const BuscaFarmaApp({super.key});

  @override
  ConsumerState<BuscaFarmaApp> createState() => _BuscaFarmaAppState();
}

class _BuscaFarmaAppState extends ConsumerState<BuscaFarmaApp> {
  @override
  void initState() {
    super.initState();
    // Initialize review manager and increment session count on app start
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _initializeRemoteConfig();
      _initializeReviewManager();
      _initializeAdService();
      _initializeDisplayMode();
    });
  }

  Future<void> _initializeRemoteConfig() async {
    try {
      debugPrint('Starting Remote Config initialization...');
      // Esto iniciará RemoteConfig a través del provider
      await ref.read(remoteConfigProvider.notifier).initialize();

      // Configurar ApiConfig con la instancia de RemoteConfig
      try {
        final remoteConfig = ref.read(remoteConfigServiceProvider);
        if (remoteConfig != null) {
          ApiConfig.setRemoteConfig(remoteConfig);
          debugPrint('ApiConfig configurado con Remote Config');
        } else {
          debugPrint('RemoteConfig service is null, using default ApiConfig values');
        }
      } catch (e) {
        debugPrint('Error al configurar ApiConfig con Remote Config: $e');
      }

      debugPrint('Remote Config initialized successfully');
    } catch (e) {
      debugPrint('Error initializing Remote Config: $e');
    }
  }

  Future<void> _initializeReviewManager() async {
    try {
      final reviewManager = ref.read(reviewManagerProvider.notifier);
      await reviewManager.incrementSessionCount();
    } catch (e) {
      // Don't fail app startup if review tracking fails
      debugPrint('Failed to initialize review manager: $e');
    }
  }

  Future<void> _initializeAdService() async {
    try {
      // Wait for RemoteConfig to be properly initialized
      final remoteConfigState = ref.read(remoteConfigProvider);
      if (remoteConfigState is AsyncLoading) {
        debugPrint('Waiting for Remote Config to initialize before initializing ads...');
        // Wait for RemoteConfig to complete initialization
        await Future.delayed(const Duration(milliseconds: 1000));
      }

      final adService = ref.read(adServiceProvider);
      final success = await adService.initializeSDK();
      if (success) {
        // Reset session counters and preload ads
        adService.resetSessionCounters();
        adService.preloadAllAds();
        debugPrint('Ad service initialized successfully');
      } else {
        debugPrint('Failed to initialize ad service');
      }
    } catch (e) {
      // Don't fail app startup if ad initialization fails
      debugPrint('Failed to initialize ad service: $e');
    }
  }

  Future<void> _initializeDisplayMode() async {
    try {
      // Wait for RemoteConfig to be initialized before initializing DisplayMode
      final remoteConfigState = ref.read(remoteConfigProvider);
      if (remoteConfigState is AsyncLoading) {
        debugPrint('Waiting for Remote Config to initialize before initializing display mode...');
        await Future.delayed(const Duration(milliseconds: 1000));
      }

      // Set RemoteConfig in DisplayModeService
      try {
        final remoteConfig = ref.read(remoteConfigServiceProvider);
        DisplayModeService.setRemoteConfig(remoteConfig);
        if (remoteConfig != null) {
          debugPrint('DisplayModeService configured with Remote Config');
        } else {
          debugPrint('DisplayModeService will use default configuration');
        }
      } catch (e) {
        debugPrint('Error configuring DisplayModeService with Remote Config: $e');
      }

      // Initialize display mode provider (this will trigger service initialization)
      ref.read(displayModeProvider);
      debugPrint('Display mode service initialization triggered');
    } catch (e) {
      // Don't fail app startup if display mode initialization fails
      debugPrint('Failed to initialize display mode service: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // Initialize auto-location provider so it starts loading
    // when the app starts (will be handled by splash screen)
    ref.watch(autoLocationProvider);

    // Initialize the auto location initializer provider
    // This will automatically load the location when GPS data is available
    ref.watch(autoLocationInitializerProvider);

    // Initialize review manager provider
    ref.watch(reviewManagerProvider);

    // Initialize update provider
    ref.watch(updateProvider);

    // Initialize remote config provider
    ref.watch(remoteConfigProvider);

    // Initialize display mode provider
    ref.watch(displayModeProvider);

    // Watch theme provider for user theme preference
    final themeMode = ref.watch(themeProvider);

    return DynamicColorBuilder(
      builder: (ColorScheme? lightDynamic, ColorScheme? darkDynamic) {
        // Define base color schemes
        ColorScheme baseLightScheme = lightDynamic ?? ColorScheme.fromSeed(seedColor: Colors.blue, primary: Colors.blue, secondary: Colors.teal);
        ColorScheme baseDarkScheme = darkDynamic ?? ColorScheme.fromSeed(seedColor: Colors.blue, brightness: Brightness.dark, primary: Colors.blue, secondary: Colors.teal);

        // Customize light scheme for better contrast as per reference image
        ColorScheme customLightScheme = baseLightScheme.copyWith();

        ColorScheme customDarkScheme = baseDarkScheme.copyWith();

        return MaterialApp(
          title: 'BuscaFarma',
          theme: ThemeData(colorScheme: customLightScheme, useMaterial3: true),
          darkTheme: ThemeData(colorScheme: customDarkScheme, useMaterial3: true),
          // Convert AppThemeMode to Flutter's ThemeMode
          themeMode: _getThemeMode(themeMode),
          debugShowCheckedModeBanner: false,
          onGenerateRoute: AppRouter.router.generator,
          initialRoute: AppRoutes.splash,
        );
      },
    );
  }

  // Helper method to convert AppThemeMode to ThemeMode
  ThemeMode _getThemeMode(AppThemeMode appThemeMode) {
    switch (appThemeMode) {
      case AppThemeMode.light:
        return ThemeMode.light;
      case AppThemeMode.dark:
        return ThemeMode.dark;
      case AppThemeMode.system:
        return ThemeMode.system;
    }
  }
}

/// Input validation utilities for the BuscaFarma application
/// Provides centralized validation logic for consistent input handling
class InputValidators {
  // Private constructor to prevent instantiation
  InputValidators._();

  /// Minimum character length for search inputs
  static const int minSearchLength = 2;
  
  /// Maximum character length for search inputs
  static const int maxSearchLength = 100;

  /// Validates search input for laboratory names
  /// Returns null if valid, error message if invalid
  static String? validateLaboratoryName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Empty is allowed for optional search
    }

    final trimmedValue = value.trim();
    
    if (trimmedValue.length < minSearchLength) {
      return 'Ingrese al menos $minSearchLength caracteres para buscar';
    }
    
    if (trimmedValue.length > maxSearchLength) {
      return 'El nombre del laboratorio no puede exceder $maxSearchLength caracteres';
    }

    // Check for invalid characters (only allow letters, numbers, spaces, and common punctuation)
    if (!RegExp(r'^[a-zA-ZáéíóúÁÉÍÓÚñÑ0-9\s\-\.\,\(\)&]+$').hasMatch(trimmedValue)) {
      return 'El nombre del laboratorio contiene caracteres no válidos';
    }

    return null; // Valid
  }

  /// Validates search input for establishment names
  /// Returns null if valid, error message if invalid
  static String? validateEstablishmentName(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Empty is allowed for optional search
    }

    final trimmedValue = value.trim();
    
    if (trimmedValue.length < minSearchLength) {
      return 'Ingrese al menos $minSearchLength caracteres para buscar';
    }
    
    if (trimmedValue.length > maxSearchLength) {
      return 'El nombre del establecimiento no puede exceder $maxSearchLength caracteres';
    }

    // Check for invalid characters (only allow letters, numbers, spaces, and common punctuation)
    if (!RegExp(r'^[a-zA-ZáéíóúÁÉÍÓÚñÑ0-9\s\-\.\,\(\)&]+$').hasMatch(trimmedValue)) {
      return 'El nombre del establecimiento contiene caracteres no válidos';
    }

    return null; // Valid
  }

  /// Sanitizes input text by trimming whitespace and normalizing spaces
  static String sanitizeInput(String input) {
    return input
        .trim()
        .replaceAll(RegExp(r'\s+'), ' '); // Replace multiple spaces with single space
  }

  /// Checks if input meets minimum search requirements
  static bool meetsMinimumSearchRequirements(String? value) {
    if (value == null) return false;
    return sanitizeInput(value).length >= minSearchLength;
  }

  /// Generates user-friendly error message for validation failures
  static String getValidationErrorMessage(String fieldName, String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'El campo $fieldName es requerido';
    }

    final trimmedValue = value.trim();
    
    if (trimmedValue.length < minSearchLength) {
      return 'Ingrese al menos $minSearchLength caracteres en $fieldName';
    }
    
    if (trimmedValue.length > maxSearchLength) {
      return '$fieldName no puede exceder $maxSearchLength caracteres';
    }

    return 'El valor ingresado en $fieldName no es válido';
  }

  /// Validates general text input with customizable parameters
  static String? validateTextInput({
    required String? value,
    required String fieldName,
    int? minLength,
    int? maxLength,
    bool required = false,
    RegExp? pattern,
  }) {
    if (value == null || value.trim().isEmpty) {
      return required ? 'El campo $fieldName es requerido' : null;
    }

    final trimmedValue = value.trim();
    final effectiveMinLength = minLength ?? minSearchLength;
    final effectiveMaxLength = maxLength ?? maxSearchLength;
    
    if (trimmedValue.length < effectiveMinLength) {
      return 'Ingrese al menos $effectiveMinLength caracteres en $fieldName';
    }
    
    if (trimmedValue.length > effectiveMaxLength) {
      return '$fieldName no puede exceder $effectiveMaxLength caracteres';
    }

    if (pattern != null && !pattern.hasMatch(trimmedValue)) {
      return 'El formato de $fieldName no es válido';
    }

    return null; // Valid
  }
}

/// Validation result class for more detailed validation feedback
class ValidationResult {
  final bool isValid;
  final String? errorMessage;
  final String? sanitizedValue;

  const ValidationResult({
    required this.isValid,
    this.errorMessage,
    this.sanitizedValue,
  });

  /// Creates a valid validation result
  factory ValidationResult.valid(String sanitizedValue) {
    return ValidationResult(
      isValid: true,
      sanitizedValue: sanitizedValue,
    );
  }

  /// Creates an invalid validation result with error message
  factory ValidationResult.invalid(String errorMessage) {
    return ValidationResult(
      isValid: false,
      errorMessage: errorMessage,
    );
  }
}

/// Extended input validators with detailed validation results
class DetailedInputValidators {
  /// Validates laboratory name with detailed result
  static ValidationResult validateLaboratoryNameDetailed(String? value) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.valid('');
    }

    final sanitizedValue = InputValidators.sanitizeInput(value);
    final errorMessage = InputValidators.validateLaboratoryName(sanitizedValue);

    if (errorMessage != null) {
      return ValidationResult.invalid(errorMessage);
    }

    return ValidationResult.valid(sanitizedValue);
  }

  /// Validates establishment name with detailed result
  static ValidationResult validateEstablishmentNameDetailed(String? value) {
    if (value == null || value.trim().isEmpty) {
      return ValidationResult.valid('');
    }

    final sanitizedValue = InputValidators.sanitizeInput(value);
    final errorMessage = InputValidators.validateEstablishmentName(sanitizedValue);

    if (errorMessage != null) {
      return ValidationResult.invalid(errorMessage);
    }

    return ValidationResult.valid(sanitizedValue);
  }
}

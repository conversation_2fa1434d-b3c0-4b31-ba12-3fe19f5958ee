import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/services/remote_config_service.dart';
import 'package:flutter/foundation.dart';

/// StateProvider para la instancia de RemoteConfigService
final remoteConfigServiceProvider = StateProvider<RemoteConfigService?>((ref) => null);

/// Provider para manejar el estado de la configuración remota
final remoteConfigProvider = StateNotifierProvider<RemoteConfigNotifier, AsyncValue<void>>((ref) {
  return RemoteConfigNotifier(ref);
});

/// StateNotifier para manejar la inicialización y actualización de Remote Config
class RemoteConfigNotifier extends StateNotifier<AsyncValue<void>> {
  final Ref _ref;

  RemoteConfigNotifier(this._ref) : super(const AsyncValue.loading()) {
    initialize();
  }

  /// Inicializa el servicio de Remote Config
  Future<void> initialize() async {
    try {
      state = const AsyncValue.loading();
      debugPrint('RemoteConfigNotifier: Starting initialization...');

      final remoteConfig = await RemoteConfigService.getInstance();
      debugPrint('RemoteConfigNotifier: RemoteConfigService instance obtained');

      // Actualiza el provider con la instancia inicializada
      _ref.read(remoteConfigServiceProvider.notifier).state = remoteConfig;
      debugPrint('RemoteConfigNotifier: Provider updated with RemoteConfig instance');

      state = const AsyncValue.data(null);
      debugPrint('RemoteConfigNotifier: Initialization completed successfully');
    } catch (e, stackTrace) {
      debugPrint('RemoteConfigNotifier: Initialization failed: $e');
      state = AsyncValue.error(e, stackTrace);
    }
  }

  /// Refresca la configuración manualmente
  Future<void> refreshConfig() async {
    try {
      state = const AsyncValue.loading();

      // Accede al servicio a través del provider
      final remoteConfigService = _ref.read(remoteConfigServiceProvider);
      if (remoteConfigService != null) {
        await remoteConfigService.refreshConfig();
        debugPrint('RemoteConfigNotifier: Configuration refreshed successfully');
      } else {
        debugPrint('RemoteConfigNotifier: Cannot refresh - service not initialized');
      }

      state = const AsyncValue.data(null);
    } catch (e, stackTrace) {
      debugPrint('RemoteConfigNotifier: Refresh failed: $e');
      state = AsyncValue.error(e, stackTrace);
    }
  }
}

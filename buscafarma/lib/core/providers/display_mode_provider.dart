import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';
import 'package:buscafarma/core/services/display_mode_service.dart';

/// Enum for display mode preferences
enum DisplayModePreference {
  /// Automatic mode (high refresh rate when available)
  auto,
  
  /// High refresh rate mode
  high,
  
  /// Low refresh rate mode (battery saving)
  low,
}

/// State class for display mode information
@immutable
class DisplayModeState {
  final bool isInitialized;
  final bool isPlatformSupported;
  final bool isFeatureEnabled;
  final List<DisplayMode> supportedModes;
  final DisplayMode? currentMode;
  final DisplayModePreference preference;
  final bool isLoading;
  final String? errorMessage;

  const DisplayModeState({
    this.isInitialized = false,
    this.isPlatformSupported = false,
    this.isFeatureEnabled = true,
    this.supportedModes = const [],
    this.currentMode,
    this.preference = DisplayModePreference.auto,
    this.isLoading = false,
    this.errorMessage,
  });

  DisplayModeState copyWith({
    bool? isInitialized,
    bool? isPlatformSupported,
    bool? isFeatureEnabled,
    List<DisplayMode>? supportedModes,
    DisplayMode? currentMode,
    DisplayModePreference? preference,
    bool? isLoading,
    String? errorMessage,
  }) {
    return DisplayModeState(
      isInitialized: isInitialized ?? this.isInitialized,
      isPlatformSupported: isPlatformSupported ?? this.isPlatformSupported,
      isFeatureEnabled: isFeatureEnabled ?? this.isFeatureEnabled,
      supportedModes: supportedModes ?? this.supportedModes,
      currentMode: currentMode ?? this.currentMode,
      preference: preference ?? this.preference,
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is DisplayModeState &&
        other.isInitialized == isInitialized &&
        other.isPlatformSupported == isPlatformSupported &&
        other.isFeatureEnabled == isFeatureEnabled &&
        listEquals(other.supportedModes, supportedModes) &&
        other.currentMode == currentMode &&
        other.preference == preference &&
        other.isLoading == isLoading &&
        other.errorMessage == errorMessage;
  }

  @override
  int get hashCode {
    return Object.hash(
      isInitialized,
      isPlatformSupported,
      isFeatureEnabled,
      supportedModes,
      currentMode,
      preference,
      isLoading,
      errorMessage,
    );
  }

  @override
  String toString() {
    return 'DisplayModeState('
        'isInitialized: $isInitialized, '
        'isPlatformSupported: $isPlatformSupported, '
        'isFeatureEnabled: $isFeatureEnabled, '
        'supportedModes: ${supportedModes.length}, '
        'currentMode: $currentMode, '
        'preference: $preference, '
        'isLoading: $isLoading, '
        'errorMessage: $errorMessage)';
  }
}

/// Provider for the display mode service instance
final displayModeServiceProvider = Provider<DisplayModeService>((ref) {
  return DisplayModeService.getInstance();
});

/// StateNotifier for managing display mode state
class DisplayModeNotifier extends StateNotifier<DisplayModeState> {
  final Ref _ref;
  late final DisplayModeService _service;

  DisplayModeNotifier(this._ref) : super(const DisplayModeState(isLoading: true)) {
    _service = _ref.read(displayModeServiceProvider);
    _initialize();
  }

  /// Initialize the display mode service
  Future<void> _initialize() async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);
      
      final success = await _service.initialize();
      
      if (success) {
        await _updateStateFromService();
        await _loadUserPreference();
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Failed to initialize display mode service',
        );
      }
    } catch (e) {
      debugPrint('DisplayModeNotifier: Initialization error: $e');
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
    }
  }

  /// Update state from service information
  Future<void> _updateStateFromService() async {
    state = state.copyWith(
      isInitialized: _service.isInitialized,
      isPlatformSupported: _service.isPlatformSupported,
      isFeatureEnabled: _service.isFeatureEnabled,
      supportedModes: _service.supportedModes,
      currentMode: _service.currentMode,
      isLoading: false,
      errorMessage: null,
    );
  }

  /// Load user preference from storage
  Future<void> _loadUserPreference() async {
    try {
      final preferenceString = await _service.getUserPreference();
      final preference = _stringToPreference(preferenceString);
      state = state.copyWith(preference: preference);
    } catch (e) {
      debugPrint('DisplayModeNotifier: Failed to load user preference: $e');
    }
  }

  /// Set high refresh rate
  Future<bool> setHighRefreshRate() async {
    if (!_canPerformOperation()) return false;

    try {
      state = state.copyWith(isLoading: true);
      final success = await _service.setHighRefreshRate();
      
      if (success) {
        await _updateStateFromService();
        await _savePreference(DisplayModePreference.high);
      } else {
        state = state.copyWith(isLoading: false);
      }
      
      return success;
    } catch (e) {
      debugPrint('DisplayModeNotifier: Error setting high refresh rate: $e');
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      return false;
    }
  }

  /// Set low refresh rate
  Future<bool> setLowRefreshRate() async {
    if (!_canPerformOperation()) return false;

    try {
      state = state.copyWith(isLoading: true);
      final success = await _service.setLowRefreshRate();
      
      if (success) {
        await _updateStateFromService();
        await _savePreference(DisplayModePreference.low);
      } else {
        state = state.copyWith(isLoading: false);
      }
      
      return success;
    } catch (e) {
      debugPrint('DisplayModeNotifier: Error setting low refresh rate: $e');
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      return false;
    }
  }

  /// Set automatic mode (high refresh rate when available)
  Future<bool> setAutoMode() async {
    if (!_canPerformOperation()) return false;

    try {
      state = state.copyWith(isLoading: true);
      final success = await _service.setHighRefreshRate(); // Auto defaults to high
      
      if (success) {
        await _updateStateFromService();
        await _savePreference(DisplayModePreference.auto);
      } else {
        state = state.copyWith(isLoading: false);
      }
      
      return success;
    } catch (e) {
      debugPrint('DisplayModeNotifier: Error setting auto mode: $e');
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      return false;
    }
  }

  /// Set specific display mode
  Future<bool> setDisplayMode(DisplayMode mode) async {
    if (!_canPerformOperation()) return false;

    try {
      state = state.copyWith(isLoading: true);
      final success = await _service.setDisplayMode(mode);
      
      if (success) {
        await _updateStateFromService();
      } else {
        state = state.copyWith(isLoading: false);
      }
      
      return success;
    } catch (e) {
      debugPrint('DisplayModeNotifier: Error setting display mode: $e');
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      return false;
    }
  }

  /// Refresh current state from service
  Future<void> refresh() async {
    try {
      state = state.copyWith(isLoading: true);
      await _updateStateFromService();
    } catch (e) {
      debugPrint('DisplayModeNotifier: Error refreshing state: $e');
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }

  /// Save user preference
  Future<void> _savePreference(DisplayModePreference preference) async {
    try {
      await _service.saveUserPreference(_preferenceToString(preference));
      state = state.copyWith(preference: preference);
    } catch (e) {
      debugPrint('DisplayModeNotifier: Failed to save preference: $e');
    }
  }

  /// Check if operations can be performed
  bool _canPerformOperation() {
    return state.isPlatformSupported && state.isFeatureEnabled && state.isInitialized;
  }

  /// Convert preference enum to string
  String _preferenceToString(DisplayModePreference preference) {
    switch (preference) {
      case DisplayModePreference.auto:
        return 'auto';
      case DisplayModePreference.high:
        return 'high';
      case DisplayModePreference.low:
        return 'low';
    }
  }

  /// Convert string to preference enum
  DisplayModePreference _stringToPreference(String preference) {
    switch (preference) {
      case 'high':
        return DisplayModePreference.high;
      case 'low':
        return DisplayModePreference.low;
      case 'auto':
      default:
        return DisplayModePreference.auto;
    }
  }

  /// Get debug information
  Map<String, dynamic> getDebugInfo() {
    return {
      'state': state.toString(),
      'service': _service.getDebugInfo(),
    };
  }
}

/// Provider for display mode state management
final displayModeProvider = StateNotifierProvider<DisplayModeNotifier, DisplayModeState>((ref) {
  return DisplayModeNotifier(ref);
});

/// Provider for checking if display mode is available
final displayModeAvailableProvider = Provider<bool>((ref) {
  final state = ref.watch(displayModeProvider);
  return state.isPlatformSupported && state.isFeatureEnabled && state.isInitialized;
});

/// Provider for getting current refresh rate (if available)
final currentRefreshRateProvider = Provider<double?>((ref) {
  final state = ref.watch(displayModeProvider);
  return state.currentMode?.refreshRate;
});

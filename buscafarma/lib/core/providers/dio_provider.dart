import 'package:dio/dio.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/utils/api_config.dart';
import 'package:buscafarma/core/utils/token_refresh_interceptor.dart';
import 'package:buscafarma/features/auth/data/providers/auth_local_provider.dart';

// Provider for the local auth storage service
final authLocalProvider = Provider<AuthLocalProvider>((ref) {
  return AuthLocalProvider();
});

// Provider for a configured Dio instance with authentication handling
final dioProvider = Provider<Dio>((ref) {
  final dio = Dio(BaseOptions(baseUrl: ApiConfig.baseUrl, connectTimeout: Duration(seconds: ApiConfig.timeoutSeconds), receiveTimeout: Duration(seconds: ApiConfig.timeoutSeconds), contentType: 'application/json'));

  // Add logging interceptor in debug mode
  dio.interceptors.add(LogInterceptor(request: true, requestHeader: true, requestBody: true, responseHeader: true, responseBody: true, error: true));

  // Add token refresh interceptor
  final authLocal = ref.read(authLocalProvider);
  dio.interceptors.add(TokenRefreshInterceptor(dio: dio, authLocalProvider: authLocal));

  return dio;
});

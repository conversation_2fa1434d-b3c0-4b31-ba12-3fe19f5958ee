import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:buscafarma/core/utils/app_constants.dart';
import 'package:buscafarma/features/auth/presentation/providers/auth_provider.dart';

/// Provider for managing theme settings
class ThemeNotifier extends StateNotifier<AppThemeMode> {
  final Ref _ref;
  static const _themePreferenceKey = 'app_theme_mode';

  ThemeNotifier(this._ref) : super(AppThemeMode.system) {
    _loadTheme();
  }

  /// Load theme from shared preferences or user preferences
  Future<void> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final savedThemeString = prefs.getString(_themePreferenceKey);

      if (savedThemeString != null) {
        state = AppThemeMode.values.firstWhere((e) => e.toString() == savedThemeString, orElse: () => AppThemeMode.system);
      } else {
        // If no theme is saved yet, check if user has preferences
        final authState = _ref.read(authProvider);
        if (authState.user?.preferences?.theme != null) {
          state = authState.user!.preferences!.theme;
        }
      }
    } catch (e) {
      // If there's an error, default to system theme
      state = AppThemeMode.system;
    }
  }

  /// Set a new theme mode
  Future<void> setTheme(AppThemeMode newTheme) async {
    state = newTheme;

    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themePreferenceKey, newTheme.toString());

      // Update in backend if user is authenticated
      final authState = _ref.read(authProvider);
      if (authState.isAuthenticated && authState.user?.preferences != null) {
        final updatedPreferences = authState.user!.preferences!.copyWith(theme: newTheme);

        // Use the AuthNotifier to update the backend first
        final success = await _ref.read(authProvider.notifier).updatePreferences(updatedPreferences);

        // If API call fails, still update locally so UI is consistent
        if (!success) {
          // Just update the local state in AuthNotifier
          _ref.read(authProvider.notifier).updateUserPreferencesLocally(updatedPreferences);
        }
      }
    } catch (e) {
      // Even if saving fails, keep the state updated
      // so the UI reflects the user's choice
    }
  }
}

/// Provider for app theme mode
final themeProvider = StateNotifierProvider<ThemeNotifier, AppThemeMode>((ref) {
  return ThemeNotifier(ref);
});

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/services/announcement_service.dart';

/// Provider for the AnnouncementService singleton
final announcementServiceProvider = Provider<AnnouncementService>((ref) {
  return AnnouncementService();
});

/// Provider for checking if this is the first app launch
final isFirstLaunchProvider = FutureProvider<bool>((ref) async {
  final service = ref.read(announcementServiceProvider);
  return await service.isFirstLaunch();
});

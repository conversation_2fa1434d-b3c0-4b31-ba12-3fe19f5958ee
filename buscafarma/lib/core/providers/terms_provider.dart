import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/services/terms_service.dart';
import 'package:buscafarma/core/services/legal_document_service.dart';

/// Provider for the LegalDocumentService singleton
final legalDocumentServiceProvider = Provider<LegalDocumentService>((ref) {
  return LegalDocumentService();
});

/// Provider for the TermsService singleton
final termsServiceProvider = Provider<TermsService>((ref) {
  final legalDocumentService = ref.read(legalDocumentServiceProvider);
  return TermsService(legalDocumentService: legalDocumentService);
});

/// Provider for checking if user has accepted terms
final hasAcceptedTermsProvider = FutureProvider<bool>((ref) async {
  final service = ref.read(termsServiceProvider);
  return await service.hasAcceptedTerms();
});

/// Provider for checking if terms should be shown
final shouldShowTermsProvider = FutureProvider<bool>((ref) async {
  final service = ref.read(termsServiceProvider);
  return await service.shouldShowTerms();
});

/// Provider for getting terms content
final termsContentProvider = FutureProvider<String>((ref) async {
  final service = ref.read(termsServiceProvider);
  return await service.getTermsContent();
});

import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/services/ad_service.dart';
import 'package:buscafarma/core/widgets/ads/interstitial_ad_manager.dart';
import 'package:buscafarma/core/widgets/ads/app_open_ad_manager.dart';
import 'package:buscafarma/core/config/ad_config.dart';
import 'package:buscafarma/core/providers/remote_config_provider.dart';

/// Centralized providers for ad-related state management
/// Integrates with existing Riverpod architecture in BuscaFarma app

// ============================================================================
// Core Ad Service Providers
// ============================================================================

/// Provider for the main ad service (already defined in ad_service.dart)
/// Re-exported here for convenience
// final adServiceProvider is already defined in ad_service.dart

/// Provider for checking if ads are enabled
final adsEnabledProvider = Provider<bool>((ref) {
  // First check if configuration is valid
  if (!AdConfig.isConfigurationValid) {
    return false;
  }

  // Check debug mode settings
  final isDebugMode = const bool.fromEnvironment('dart.vm.product', defaultValue: false);
  if (isDebugMode && !AdConfig.showAdsInDebug) {
    return false;
  }

  // Check remote config enable_ads parameter
  try {
    final remoteConfigState = ref.watch(remoteConfigProvider);
    final remoteConfig = ref.watch(remoteConfigServiceProvider);

    // If RemoteConfig is loaded and available, use its value
    if (remoteConfig != null && remoteConfigState is AsyncData) {
      final remoteAdsEnabled = remoteConfig.getBool('enable_ads');
      debugPrint('adsEnabledProvider: Using remote config value: $remoteAdsEnabled');
      return remoteAdsEnabled;
    } else if (remoteConfigState is AsyncLoading) {
      // While loading, default to true to allow ads (will update when loaded)
      debugPrint('adsEnabledProvider: RemoteConfig loading, defaulting to enabled');
      return true;
    } else if (remoteConfigState is AsyncError) {
      debugPrint('adsEnabledProvider: RemoteConfig error, defaulting to enabled');
      return true;
    }
  } catch (e) {
    // If remote config fails, fall back to default behavior
    debugPrint('adsEnabledProvider: Failed to get remote config enable_ads: $e');
  }

  // Default fallback: show ads if configuration is valid
  return true;
});

/// Provider for ad configuration validation
final adConfigValidProvider = Provider<bool>((ref) {
  return AdConfig.isConfigurationValid;
});

/// Provider for mock ad detection
final usingMockAdsProvider = Provider<bool>((ref) {
  return AdConfig.isUsingMockIds;
});

// ============================================================================
// Banner Ad Providers
// ============================================================================

/// Provider for banner ad visibility state
final bannerAdVisibilityProvider = StateProvider.family<bool, String>((ref, location) {
  // Default to true for all banner locations
  return true;
});

/// Provider for banner ad loading state
final bannerAdLoadingProvider = StateProvider.family<bool, String>((ref, adUnitId) {
  return false;
});

/// Provider for banner ad error state
final bannerAdErrorProvider = StateProvider.family<String?, String>((ref, adUnitId) {
  return null;
});

// ============================================================================
// Interstitial Ad Providers (from interstitial_ad_manager.dart)
// ============================================================================

/// Provider for interstitial ad readiness
final interstitialAdReadyProvider = FutureProvider<bool>((ref) async {
  try {
    return await InterstitialAdManager.isAdReady();
  } catch (e) {
    return false;
  }
});

/// Provider for checking if interstitial can be shown
final canShowInterstitialProvider = Provider<bool>((ref) {
  final adsEnabled = ref.watch(adsEnabledProvider);
  if (!adsEnabled) return false;

  try {
    final adService = ref.read(adServiceProvider);
    return adService.canShowInterstitial;
  } catch (e) {
    return false;
  }
});

// ============================================================================
// App Open Ad Providers (from app_open_ad_manager.dart)
// ============================================================================

/// Provider for app open ad readiness
final appOpenAdReadyProvider = FutureProvider<bool>((ref) async {
  try {
    return await AppOpenAdManager.isAdReady();
  } catch (e) {
    return false;
  }
});

/// Provider for checking if app open ad can be shown
final canShowAppOpenAdProvider = Provider<bool>((ref) {
  final adsEnabled = ref.watch(adsEnabledProvider);
  if (!adsEnabled) return false;

  try {
    final adService = ref.read(adServiceProvider);
    return adService.canShowAppOpenAd;
  } catch (e) {
    return false;
  }
});

// ============================================================================
// Ad Frequency Management Providers
// ============================================================================

/// Provider for tracking ad impressions per session
final adImpressionCountProvider = StateProvider<Map<String, int>>((ref) {
  return {};
});

/// Provider for tracking last ad show times
final lastAdShowTimeProvider = StateProvider<Map<String, DateTime>>((ref) {
  return {};
});

/// Provider for session start time
final sessionStartTimeProvider = StateProvider<DateTime>((ref) {
  return DateTime.now();
});

// ============================================================================
// Ad Debug Information Providers
// ============================================================================

/// Provider for comprehensive ad debug information
final adDebugInfoProvider = FutureProvider<Map<String, dynamic>>((ref) async {
  try {
    final adService = ref.read(adServiceProvider);
    final interstitialReady = await InterstitialAdManager.isAdReady();
    final appOpenReady = await AppOpenAdManager.isAdReady();

    return {
      'adsEnabled': ref.read(adsEnabledProvider),
      'configValid': ref.read(adConfigValidProvider),
      'usingMockIds': ref.read(usingMockAdsProvider),
      'sessionStartTime': ref.read(sessionStartTimeProvider).toIso8601String(),
      'adService': adService.getDebugInfo(),
      'interstitial': {
        'canShowAd': adService.canShowInterstitial,
        'isAdReady': interstitialReady,
        'adUnitId': AdConfig.interstitialAdUnitId,
        'cooldownSeconds': AdConfig.interstitialCooldownSeconds,
        'maxPerSession': AdConfig.maxInterstitialsPerSession,
      },
      'appOpen': {'canShowAd': adService.canShowAppOpenAd, 'isAdReady': appOpenReady, 'adUnitId': AdConfig.appOpenAdUnitId, 'cooldownSeconds': AdConfig.appOpenCooldownSeconds},
      'impressionCounts': ref.read(adImpressionCountProvider),
      'lastShowTimes': ref.read(lastAdShowTimeProvider).map((key, value) => MapEntry(key, value.toIso8601String())),
    };
  } catch (e) {
    return {'error': e.toString()};
  }
});

// ============================================================================
// Ad Event Tracking Providers
// ============================================================================

/// Provider for tracking ad events (loads, shows, clicks, etc.)
final adEventLogProvider = StateProvider<List<AdEvent>>((ref) {
  return [];
});

/// Class for representing ad events
class AdEvent {
  final String adType;
  final String eventType;
  final String? adUnitId;
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  const AdEvent({required this.adType, required this.eventType, this.adUnitId, required this.timestamp, this.metadata});

  Map<String, dynamic> toJson() {
    return {'adType': adType, 'eventType': eventType, 'adUnitId': adUnitId, 'timestamp': timestamp.toIso8601String(), 'metadata': metadata};
  }
}

/// Notifier for managing ad events
class AdEventNotifier extends StateNotifier<List<AdEvent>> {
  AdEventNotifier() : super([]);

  void logEvent({required String adType, required String eventType, String? adUnitId, Map<String, dynamic>? metadata}) {
    final event = AdEvent(adType: adType, eventType: eventType, adUnitId: adUnitId, timestamp: DateTime.now(), metadata: metadata);

    state = [...state, event];

    // Keep only last 100 events to prevent memory issues
    if (state.length > 100) {
      state = state.sublist(state.length - 100);
    }
  }

  void clearEvents() {
    state = [];
  }

  List<AdEvent> getEventsForAdType(String adType) {
    return state.where((event) => event.adType == adType).toList();
  }

  List<AdEvent> getEventsForEventType(String eventType) {
    return state.where((event) => event.eventType == eventType).toList();
  }
}

/// Provider for ad event notifier
final adEventNotifierProvider = StateNotifierProvider<AdEventNotifier, List<AdEvent>>((ref) {
  return AdEventNotifier();
});

// ============================================================================
// Utility Providers
// ============================================================================

/// Provider for checking if it's safe to show ads (not during critical operations)
final safeToShowAdsProvider = Provider<bool>((ref) {
  // Add logic here to check if app is in a state where ads can be shown
  // For example, not during onboarding, not during critical user flows, etc.
  return true;
});

/// Provider for ad placement configuration
final adPlacementConfigProvider = Provider<Map<String, bool>>((ref) {
  return {'mapPageBanner': true, 'favoritesPageBanner': true, 'navigationPageBanner': true, 'searchCompletionInterstitial': true, 'establishmentDetailsInterstitial': true, 'profileExitInterstitial': true, 'splashAppOpen': true};
});

/// Helper function to log ad events
void logAdEvent(WidgetRef ref, {required String adType, required String eventType, String? adUnitId, Map<String, dynamic>? metadata}) {
  ref.read(adEventNotifierProvider.notifier).logEvent(adType: adType, eventType: eventType, adUnitId: adUnitId, metadata: metadata);
}

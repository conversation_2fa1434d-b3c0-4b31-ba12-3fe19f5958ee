import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/services/navigation_tracking_service.dart';
import 'package:buscafarma/core/providers/remote_config_provider.dart';
import 'package:flutter/foundation.dart';

/// Provider for the NavigationTrackingService
final navigationTrackingServiceProvider = Provider<NavigationTrackingService>((ref) {
  // Get remote config service if available
  final remoteConfig = ref.watch(remoteConfigServiceProvider);
  
  return NavigationTrackingService(remoteConfig: remoteConfig);
});

/// State class for navigation tracking
@immutable
class NavigationTrackingState {
  final int navigationActionCount;
  final DateTime? lastNavigationAdTime;
  final bool adsEnabled;
  final int adsFrequency;
  final bool shouldShowAd;

  const NavigationTrackingState({
    required this.navigationActionCount,
    this.lastNavigationAdTime,
    required this.adsEnabled,
    required this.adsFrequency,
    required this.shouldShowAd,
  });

  NavigationTrackingState copyWith({
    int? navigationActionCount,
    DateTime? lastNavigationAdTime,
    bool? adsEnabled,
    int? adsFrequency,
    bool? shouldShowAd,
  }) {
    return NavigationTrackingState(
      navigationActionCount: navigationActionCount ?? this.navigationActionCount,
      lastNavigationAdTime: lastNavigationAdTime ?? this.lastNavigationAdTime,
      adsEnabled: adsEnabled ?? this.adsEnabled,
      adsFrequency: adsFrequency ?? this.adsFrequency,
      shouldShowAd: shouldShowAd ?? this.shouldShowAd,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NavigationTrackingState &&
        other.navigationActionCount == navigationActionCount &&
        other.lastNavigationAdTime == lastNavigationAdTime &&
        other.adsEnabled == adsEnabled &&
        other.adsFrequency == adsFrequency &&
        other.shouldShowAd == shouldShowAd;
  }

  @override
  int get hashCode {
    return navigationActionCount.hashCode ^
        lastNavigationAdTime.hashCode ^
        adsEnabled.hashCode ^
        adsFrequency.hashCode ^
        shouldShowAd.hashCode;
  }
}

/// StateNotifier for managing navigation tracking state
class NavigationTrackingNotifier extends StateNotifier<NavigationTrackingState> {
  final NavigationTrackingService _service;

  NavigationTrackingNotifier(this._service) : super(_getInitialState(_service));

  static NavigationTrackingState _getInitialState(NavigationTrackingService service) {
    return NavigationTrackingState(
      navigationActionCount: service.navigationActionCount,
      lastNavigationAdTime: service.lastNavigationAdTime,
      adsEnabled: service.adsEnabled,
      adsFrequency: service.adsFrequency,
      shouldShowAd: service.shouldShowAdBasedOnFrequency(),
    );
  }

  /// Increment navigation action counter
  void incrementNavigationAction() {
    _service.incrementNavigationAction();
    _updateState();
    debugPrint('NavigationTrackingNotifier: Navigation action incremented');
  }

  /// Check if an ad should be shown based on current state
  bool shouldShowAd({Duration minimumInterval = const Duration(minutes: 1)}) {
    return _service.shouldShowAd(minimumInterval: minimumInterval);
  }

  /// Record that an ad was shown and reset counters
  void recordAdShown() {
    _service.recordAdShown();
    _updateState();
    debugPrint('NavigationTrackingNotifier: Ad shown recorded');
  }

  /// Reset session counters
  void resetSessionCounters() {
    _service.resetSessionCounters();
    _updateState();
    debugPrint('NavigationTrackingNotifier: Session counters reset');
  }

  /// Update state from service
  void _updateState() {
    state = NavigationTrackingState(
      navigationActionCount: _service.navigationActionCount,
      lastNavigationAdTime: _service.lastNavigationAdTime,
      adsEnabled: _service.adsEnabled,
      adsFrequency: _service.adsFrequency,
      shouldShowAd: _service.shouldShowAdBasedOnFrequency(),
    );
  }

  /// Get debug information
  Map<String, dynamic> getDebugInfo() {
    return _service.getDebugInfo();
  }

  /// Refresh state (useful when remote config changes)
  void refreshState() {
    _updateState();
    debugPrint('NavigationTrackingNotifier: State refreshed');
  }
}

/// Provider for navigation tracking state notifier
final navigationTrackingProvider = StateNotifierProvider<NavigationTrackingNotifier, NavigationTrackingState>((ref) {
  final service = ref.watch(navigationTrackingServiceProvider);
  return NavigationTrackingNotifier(service);
});

/// Convenience provider for checking if ads should be shown based on navigation frequency
final shouldShowNavigationAdProvider = Provider<bool>((ref) {
  final trackingState = ref.watch(navigationTrackingProvider);
  return trackingState.shouldShowAd && trackingState.adsEnabled;
});

/// Provider for navigation tracking debug information
final navigationTrackingDebugProvider = Provider<Map<String, dynamic>>((ref) {
  final notifier = ref.read(navigationTrackingProvider.notifier);
  return notifier.getDebugInfo();
});

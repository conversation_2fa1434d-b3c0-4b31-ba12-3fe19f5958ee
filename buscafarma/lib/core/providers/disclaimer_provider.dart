import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:buscafarma/core/services/disclaimer_service.dart';

/// Provider for the DisclaimerService singleton
final disclaimerServiceProvider = Provider<DisclaimerService>((ref) {
  return DisclaimerService();
});

/// Provider for checking if user has seen data source disclaimer
final hasSeenDataSourceDisclaimerProvider = FutureProvider<bool>((ref) async {
  final service = ref.read(disclaimerServiceProvider);
  return await service.hasSeenDataSourceDisclaimer();
});

/// Provider for checking if disclaimer should be shown
final shouldShowDisclaimerProvider = FutureProvider<bool>((ref) async {
  final service = ref.read(disclaimerServiceProvider);
  return await service.shouldShowDisclaimer();
});

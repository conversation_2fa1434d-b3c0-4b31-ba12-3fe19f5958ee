import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:applovin_max/applovin_max.dart';
import 'package:buscafarma/core/config/ad_config.dart';
import 'package:buscafarma/core/services/ad_service.dart';

/// Manager for app open ads with timing control and lifecycle management
/// Handles showing ads when app opens or comes to foreground
class AppOpenAdManager {
  // Private constructor to prevent instantiation
  AppOpenAdManager._();

  /// Show app open ad after splash screen completion
  /// This is the primary use case for app open ads
  /// Now uses remote config checking
  static Future<bool> showAfterSplashCompletion(WidgetRef ref) async {
    try {
      debugPrint('AppOpenAdManager: Attempting to show ad after splash completion');

      final adService = ref.read(adServiceProvider);
      final success = await adService.showAppOpenAdWithRemoteConfig(ref);

      if (success) {
        debugPrint('AppOpenAdManager: Splash completion ad shown successfully');
      } else {
        debugPrint('AppOpenAdManager: Splash completion ad not shown (frequency cap, remote config, or not ready)');
      }

      return success;
    } catch (e) {
      debugPrint('AppOpenAdManager: Error showing splash completion ad: $e');
      return false;
    }
  }

  /// Show app open ad when app comes to foreground
  /// This can be used for apps that support background/foreground detection
  /// Now uses remote config checking
  static Future<bool> showOnAppForeground(WidgetRef ref) async {
    try {
      debugPrint('AppOpenAdManager: Attempting to show ad on app foreground');

      final adService = ref.read(adServiceProvider);
      final success = await adService.showAppOpenAdWithRemoteConfig(ref);

      if (success) {
        debugPrint('AppOpenAdManager: App foreground ad shown successfully');
      } else {
        debugPrint('AppOpenAdManager: App foreground ad not shown (frequency cap, remote config, or not ready)');
      }

      return success;
    } catch (e) {
      debugPrint('AppOpenAdManager: Error showing app foreground ad: $e');
      return false;
    }
  }

  /// Show app open ad with custom context
  /// Generic method for showing app open ads with custom logging
  /// Now uses remote config checking
  static Future<bool> showWithContext(WidgetRef ref, String context) async {
    try {
      debugPrint('AppOpenAdManager: Attempting to show ad for context: $context');

      final adService = ref.read(adServiceProvider);
      final success = await adService.showAppOpenAdWithRemoteConfig(ref);

      if (success) {
        debugPrint('AppOpenAdManager: Ad shown successfully for context: $context');
      } else {
        debugPrint('AppOpenAdManager: Ad not shown for context: $context (frequency cap, remote config, or not ready)');
      }

      return success;
    } catch (e) {
      debugPrint('AppOpenAdManager: Error showing ad for context $context: $e');
      return false;
    }
  }

  /// Check if app open ad can be shown (respects frequency capping)
  static bool canShowAd(WidgetRef ref) {
    try {
      final adService = ref.read(adServiceProvider);
      return adService.canShowAppOpenAd;
    } catch (e) {
      debugPrint('AppOpenAdManager: Error checking if ad can be shown: $e');
      return false;
    }
  }

  /// Preload app open ad for better performance
  static void preloadAd() {
    try {
      debugPrint('AppOpenAdManager: Preloading app open ad');
      AppLovinMAX.loadAppOpenAd(AdConfig.appOpenAdUnitId);
    } catch (e) {
      debugPrint('AppOpenAdManager: Error preloading ad: $e');
    }
  }

  /// Check if app open ad is ready to be shown
  static Future<bool> isAdReady() async {
    try {
      final isReady = await AppLovinMAX.isAppOpenAdReady(AdConfig.appOpenAdUnitId);
      return isReady == true;
    } catch (e) {
      debugPrint('AppOpenAdManager: Error checking if ad is ready: $e');
      return false;
    }
  }

  /// Get debug information about app open ad state
  static Future<Map<String, dynamic>> getDebugInfo(WidgetRef ref) async {
    try {
      final adService = ref.read(adServiceProvider);
      final isReady = await isAdReady();

      return {'canShowAd': adService.canShowAppOpenAd, 'isAdReady': isReady, 'adUnitId': AdConfig.appOpenAdUnitId, 'cooldownSeconds': AdConfig.appOpenCooldownSeconds, ...adService.getDebugInfo()};
    } catch (e) {
      debugPrint('AppOpenAdManager: Error getting debug info: $e');
      return {'error': e.toString()};
    }
  }
}

/// Provider for app open ad state management
final appOpenAdStateProvider = StateProvider<AppOpenAdState>((ref) {
  return const AppOpenAdState();
});

/// State class for tracking app open ad status
class AppOpenAdState {
  final bool isLoading;
  final bool isReady;
  final String? lastShownContext;
  final DateTime? lastShownTime;
  final String? errorMessage;
  final bool isPreloading;

  const AppOpenAdState({this.isLoading = false, this.isReady = false, this.lastShownContext, this.lastShownTime, this.errorMessage, this.isPreloading = false});

  AppOpenAdState copyWith({bool? isLoading, bool? isReady, String? lastShownContext, DateTime? lastShownTime, String? errorMessage, bool? isPreloading}) {
    return AppOpenAdState(
      isLoading: isLoading ?? this.isLoading,
      isReady: isReady ?? this.isReady,
      lastShownContext: lastShownContext ?? this.lastShownContext,
      lastShownTime: lastShownTime ?? this.lastShownTime,
      errorMessage: errorMessage ?? this.errorMessage,
      isPreloading: isPreloading ?? this.isPreloading,
    );
  }
}

/// Notifier for managing app open ad state
class AppOpenAdNotifier extends StateNotifier<AppOpenAdState> {
  final Ref ref;

  AppOpenAdNotifier(this.ref) : super(const AppOpenAdState()) {
    _initialize();
  }

  void _initialize() {
    // Set up listeners and initial state
    _checkAdReadiness();
  }

  Future<void> _checkAdReadiness() async {
    state = state.copyWith(isLoading: true);

    try {
      final isReady = await AppOpenAdManager.isAdReady();
      state = state.copyWith(isReady: isReady, isLoading: false);
    } catch (e) {
      state = state.copyWith(isReady: false, isLoading: false, errorMessage: e.toString());
    }
  }

  Future<bool> showAd(String context) async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      // Use the remote config-aware ad service method (Ref version)
      final adService = ref.read(adServiceProvider);
      final success = await adService.showAppOpenAdWithRemoteConfigRef(ref);

      if (success) {
        state = state.copyWith(
          isLoading: false,
          lastShownContext: context,
          lastShownTime: DateTime.now(),
          isReady: false, // Ad was consumed
        );
        debugPrint('AppOpenAdNotifier: Ad shown successfully for context: $context');
      } else {
        state = state.copyWith(isLoading: false);
        debugPrint('AppOpenAdNotifier: Ad not shown for context: $context (frequency cap, remote config, or not ready)');
      }

      // Check readiness for next ad
      _checkAdReadiness();

      return success;
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      debugPrint('AppOpenAdNotifier: Error showing ad for context $context: $e');
      return false;
    }
  }

  Future<void> preloadAd() async {
    try {
      state = state.copyWith(isPreloading: true, errorMessage: null);

      AppOpenAdManager.preloadAd();

      state = state.copyWith(isPreloading: false);

      // Check if ad is now ready
      _checkAdReadiness();
    } catch (e) {
      state = state.copyWith(isPreloading: false, errorMessage: e.toString());
    }
  }

  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  void refreshReadiness() {
    _checkAdReadiness();
  }
}

/// Provider for app open ad notifier
final appOpenAdNotifierProvider = StateNotifierProvider<AppOpenAdNotifier, AppOpenAdState>((ref) {
  return AppOpenAdNotifier(ref);
});

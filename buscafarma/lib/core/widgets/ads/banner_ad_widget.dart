import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:applovin_max/applovin_max.dart';
import 'package:buscafarma/core/config/ad_config.dart';
import 'package:buscafarma/core/providers/ad_providers.dart';

/// Reusable banner ad widget that can be placed anywhere in the app
/// Handles ad loading, error states, and provides consistent styling
/// Now respects remote config enable_ads parameter
class BannerAdWidget extends ConsumerStatefulWidget {
  /// The ad unit ID to use for this banner
  final String adUnitId;

  /// Whether to use MREC (Medium Rectangle) format instead of banner
  final bool useMREC;

  /// Whether to use adaptive banner sizing (AppLovin MAX 4.5.0+)
  final bool isAdaptive;

  /// Background color for the ad container
  final Color? backgroundColor;

  /// Margin around the ad
  final EdgeInsets margin;

  /// Whether to show a loading indicator while ad loads
  final bool showLoadingIndicator;

  /// Callback when ad loads successfully
  final VoidCallback? onAdLoaded;

  /// Callback when ad fails to load
  final VoidCallback? onAdLoadFailed;

  BannerAdWidget({super.key, String? adUnitId, this.useMREC = false, this.isAdaptive = true, this.backgroundColor, this.margin = EdgeInsets.zero, this.showLoadingIndicator = false, this.onAdLoaded, this.onAdLoadFailed})
    : adUnitId = adUnitId ?? AdConfig.navigationBannerAdUnitId;

  /// Factory constructor for navigation page banner
  factory BannerAdWidget.navigationPage({VoidCallback? onAdLoaded, VoidCallback? onAdLoadFailed}) {
    return BannerAdWidget(adUnitId: AdConfig.navigationBannerAdUnitId, margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 2.0), showLoadingIndicator: false, onAdLoaded: onAdLoaded, onAdLoadFailed: onAdLoadFailed);
  }

  /// Factory constructor for MREC ads
  factory BannerAdWidget.mrec({EdgeInsets margin = EdgeInsets.zero, VoidCallback? onAdLoaded, VoidCallback? onAdLoadFailed}) {
    return BannerAdWidget(adUnitId: AdConfig.mrecAdUnitId, useMREC: true, margin: margin, showLoadingIndicator: true, onAdLoaded: onAdLoaded, onAdLoadFailed: onAdLoadFailed);
  }

  @override
  ConsumerState<BannerAdWidget> createState() => _BannerAdWidgetState();

  /// Get banner dimensions based on format and adaptive settings
  static Size getBannerSize(bool useMREC, bool isAdaptive) {
    if (useMREC) {
      // MREC dimensions: 300x250
      return const Size(300, 250);
    } else if (isAdaptive) {
      // Adaptive banner: use device width, height will be determined by SDK
      return const Size(double.infinity, 50); // Default height, will adapt
    } else {
      // Standard banner: 320x50
      return const Size(320, 50);
    }
  }
}

class _BannerAdWidgetState extends ConsumerState<BannerAdWidget> {
  bool _isAdLoading = true;
  bool _hasAdError = false;

  @override
  void initState() {
    super.initState();
    _loadAd();
  }

  void _loadAd() {
    // MaxAdView handles everything, just initialize state
    setState(() {
      _isAdLoading = true;
      _hasAdError = false;
    });
    debugPrint('Banner ad widget initialized for: ${widget.adUnitId}');
  }

  @override
  void dispose() {
    // MaxAdView handles its own lifecycle, no need to manually destroy
    debugPrint('Banner ad widget disposed for: ${widget.adUnitId}');
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // Check if ads are enabled via remote config
    final adsEnabled = ref.watch(adsEnabledProvider);
    if (!adsEnabled) {
      debugPrint('BannerAdWidget: Ads disabled by remote config, not showing banner');
      return const SizedBox.shrink();
    }

    // Don't show anything if ad failed to load and we're not showing loading
    if (_hasAdError && !widget.showLoadingIndicator) {
      return const SizedBox.shrink();
    }

    final theme = Theme.of(context);
    final adFormat = widget.useMREC ? AdFormat.mrec : AdFormat.banner;
    final bannerSize = BannerAdWidget.getBannerSize(widget.useMREC, widget.isAdaptive);

    return Container(
      margin: widget.margin,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Loading indicator
          if (_isAdLoading && widget.showLoadingIndicator)
            Container(
              width: bannerSize.width == double.infinity ? MediaQuery.of(context).size.width - 16 : bannerSize.width,
              height: bannerSize.height,
              decoration: BoxDecoration(
                color: widget.backgroundColor ?? theme.colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(color: theme.colorScheme.outline.withAlpha((0.2 * 255).round()), width: 1.0),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SizedBox(width: 20, height: 20, child: CircularProgressIndicator(strokeWidth: 2.0, color: theme.colorScheme.primary)),
                    const SizedBox(height: 8),
                    Text('Cargando anuncio...', style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.onSurfaceVariant)),
                  ],
                ),
              ),
            ),

          // Error state
          if (_hasAdError && widget.showLoadingIndicator)
            Container(
              width: bannerSize.width == double.infinity ? MediaQuery.of(context).size.width - 16 : bannerSize.width,
              height: bannerSize.height,
              decoration: BoxDecoration(
                color: widget.backgroundColor ?? theme.colorScheme.errorContainer.withAlpha((0.1 * 255).round()),
                borderRadius: BorderRadius.circular(8.0),
                border: Border.all(color: theme.colorScheme.error.withAlpha((0.2 * 255).round()), width: 1.0),
              ),
              child: Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.error_outline, color: theme.colorScheme.error, size: 20),
                    const SizedBox(height: 4),
                    Text('Error al cargar anuncio', style: theme.textTheme.bodySmall?.copyWith(color: theme.colorScheme.error), textAlign: TextAlign.center),
                  ],
                ),
              ),
            ),

          // Actual ad view - always show MaxAdView, let it handle loading
          Container(
            width: bannerSize.width == double.infinity ? MediaQuery.of(context).size.width - 16 : bannerSize.width,
            height: bannerSize.height,
            decoration: BoxDecoration(color: widget.backgroundColor, borderRadius: BorderRadius.circular(8.0)),
            child: MaxAdView(
              adUnitId: widget.adUnitId,
              adFormat: adFormat,
              listener: AdViewAdListener(
                onAdLoadedCallback: (ad) {
                  if (mounted && ad.adUnitId == widget.adUnitId) {
                    setState(() {
                      _isAdLoading = false;
                      _hasAdError = false;
                    });
                    widget.onAdLoaded?.call();
                    debugPrint('MaxAdView loaded: ${ad.adUnitId}');
                  }
                },
                onAdLoadFailedCallback: (adUnitId, error) {
                  if (mounted && adUnitId == widget.adUnitId) {
                    setState(() {
                      _isAdLoading = false;
                      _hasAdError = true;
                    });
                    widget.onAdLoadFailed?.call();
                    debugPrint('MaxAdView failed to load: $adUnitId, Error: ${error.message}');
                  }
                },
                onAdClickedCallback: (ad) {
                  debugPrint('MaxAdView clicked: ${ad.adUnitId}');
                },
                onAdExpandedCallback: (ad) {
                  debugPrint('MaxAdView expanded: ${ad.adUnitId}');
                },
                onAdCollapsedCallback: (ad) {
                  debugPrint('MaxAdView collapsed: ${ad.adUnitId}');
                },
              ),
            ),
          ),
        ],
      ),
    );
  }
}

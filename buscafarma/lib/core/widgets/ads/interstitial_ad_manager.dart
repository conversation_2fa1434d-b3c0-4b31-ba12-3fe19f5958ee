import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:applovin_max/applovin_max.dart';
import 'package:buscafarma/core/config/ad_config.dart';
import 'package:buscafarma/core/services/ad_service.dart';
import 'package:buscafarma/core/providers/navigation_tracking_provider.dart';

/// Manager for interstitial ads with frequency capping and smart timing
/// Provides static methods for easy integration throughout the app
class InterstitialAdManager {
  // Private constructor to prevent instantiation
  InterstitialAdManager._();

  /// Show interstitial ad after successful search completion
  /// This is triggered when user completes a medicine search and results are loaded
  /// Now uses remote config and navigation tracking
  static Future<void> showAfterSearchCompletion(WidgetRef ref) async {
    try {
      debugPrint('InterstitialAdManager: Attempting to show ad after search completion');

      // Increment navigation action for search completion
      ref.read(navigationTrackingProvider.notifier).incrementNavigationAction();

      final adService = ref.read(adServiceProvider);
      final success = await adService.showInterstitialAdWithRemoteConfig(ref);

      if (success) {
        debugPrint('InterstitialAdManager: Search completion ad shown successfully');
      } else {
        debugPrint('InterstitialAdManager: Search completion ad not shown (frequency cap, remote config, or not ready)');
      }
    } catch (e) {
      debugPrint('InterstitialAdManager: Error showing search completion ad: $e');
    }
  }

  /// Show interstitial ad after establishment details are closed
  /// This is triggered when user closes the establishment details bottom sheet
  /// Now uses remote config and navigation tracking
  static Future<void> showAfterEstablishmentDetailsClosure(WidgetRef ref) async {
    try {
      debugPrint('InterstitialAdManager: Attempting to show ad after establishment details closure');

      // Increment navigation action for establishment details closure
      ref.read(navigationTrackingProvider.notifier).incrementNavigationAction();

      final adService = ref.read(adServiceProvider);
      final success = await adService.showInterstitialAdWithRemoteConfig(ref);

      if (success) {
        debugPrint('InterstitialAdManager: Establishment details closure ad shown successfully');
      } else {
        debugPrint('InterstitialAdManager: Establishment details closure ad not shown (frequency cap, remote config, or not ready)');
      }
    } catch (e) {
      debugPrint('InterstitialAdManager: Error showing establishment details closure ad: $e');
    }
  }

  /// Show interstitial ad when user exits profile page
  /// This is triggered when user navigates away from profile page
  /// Now uses remote config and navigation tracking
  static Future<void> showOnProfileExit(WidgetRef ref) async {
    try {
      debugPrint('InterstitialAdManager: Attempting to show ad on profile exit');

      // Increment navigation action for profile exit
      ref.read(navigationTrackingProvider.notifier).incrementNavigationAction();

      final adService = ref.read(adServiceProvider);
      final success = await adService.showInterstitialAdWithRemoteConfig(ref);

      if (success) {
        debugPrint('InterstitialAdManager: Profile exit ad shown successfully');
      } else {
        debugPrint('InterstitialAdManager: Profile exit ad not shown (frequency cap, remote config, or not ready)');
      }
    } catch (e) {
      debugPrint('InterstitialAdManager: Error showing profile exit ad: $e');
    }
  }

  /// Show interstitial ad with custom context
  /// Generic method for showing interstitial ads with custom logging
  /// Now uses remote config and navigation tracking
  static Future<bool> showWithContext(WidgetRef ref, String context) async {
    try {
      debugPrint('InterstitialAdManager: Attempting to show ad for context: $context');

      // Increment navigation action for custom context
      ref.read(navigationTrackingProvider.notifier).incrementNavigationAction();

      final adService = ref.read(adServiceProvider);
      final success = await adService.showInterstitialAdWithRemoteConfig(ref);

      if (success) {
        debugPrint('InterstitialAdManager: Ad shown successfully for context: $context');
      } else {
        debugPrint('InterstitialAdManager: Ad not shown for context: $context (frequency cap, remote config, or not ready)');
      }

      return success;
    } catch (e) {
      debugPrint('InterstitialAdManager: Error showing ad for context $context: $e');
      return false;
    }
  }

  /// Check if interstitial ad can be shown (respects frequency capping)
  static bool canShowAd(WidgetRef ref) {
    try {
      final adService = ref.read(adServiceProvider);
      return adService.canShowInterstitial;
    } catch (e) {
      debugPrint('InterstitialAdManager: Error checking if ad can be shown: $e');
      return false;
    }
  }

  /// Preload interstitial ad for better performance
  static void preloadAd() {
    try {
      debugPrint('InterstitialAdManager: Preloading interstitial ad');
      AppLovinMAX.loadInterstitial(AdConfig.interstitialAdUnitId);
    } catch (e) {
      debugPrint('InterstitialAdManager: Error preloading ad: $e');
    }
  }

  /// Check if interstitial ad is ready to be shown
  static Future<bool> isAdReady() async {
    try {
      final isReady = await AppLovinMAX.isInterstitialReady(AdConfig.interstitialAdUnitId);
      return isReady == true;
    } catch (e) {
      debugPrint('InterstitialAdManager: Error checking if ad is ready: $e');
      return false;
    }
  }

  /// Get debug information about interstitial ad state
  static Future<Map<String, dynamic>> getDebugInfo(WidgetRef ref) async {
    try {
      final adService = ref.read(adServiceProvider);
      final isReady = await isAdReady();

      return {
        'canShowAd': adService.canShowInterstitial,
        'isAdReady': isReady,
        'adUnitId': AdConfig.interstitialAdUnitId,
        'cooldownSeconds': AdConfig.interstitialCooldownSeconds,
        'maxPerSession': AdConfig.maxInterstitialsPerSession,
        ...adService.getDebugInfo(),
      };
    } catch (e) {
      debugPrint('InterstitialAdManager: Error getting debug info: $e');
      return {'error': e.toString()};
    }
  }
}

/// Provider for interstitial ad state management
final interstitialAdStateProvider = StateProvider<InterstitialAdState>((ref) {
  return const InterstitialAdState();
});

/// State class for tracking interstitial ad status
class InterstitialAdState {
  final bool isLoading;
  final bool isReady;
  final String? lastShownContext;
  final DateTime? lastShownTime;
  final String? errorMessage;

  const InterstitialAdState({this.isLoading = false, this.isReady = false, this.lastShownContext, this.lastShownTime, this.errorMessage});

  InterstitialAdState copyWith({bool? isLoading, bool? isReady, String? lastShownContext, DateTime? lastShownTime, String? errorMessage}) {
    return InterstitialAdState(
      isLoading: isLoading ?? this.isLoading,
      isReady: isReady ?? this.isReady,
      lastShownContext: lastShownContext ?? this.lastShownContext,
      lastShownTime: lastShownTime ?? this.lastShownTime,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }
}

/// Notifier for managing interstitial ad state
class InterstitialAdNotifier extends StateNotifier<InterstitialAdState> {
  final Ref ref;

  InterstitialAdNotifier(this.ref) : super(const InterstitialAdState()) {
    _initialize();
  }

  void _initialize() {
    // Set up listeners and initial state
    _checkAdReadiness();
  }

  Future<void> _checkAdReadiness() async {
    state = state.copyWith(isLoading: true);

    try {
      final isReady = await InterstitialAdManager.isAdReady();
      state = state.copyWith(isReady: isReady, isLoading: false);
    } catch (e) {
      state = state.copyWith(isReady: false, isLoading: false, errorMessage: e.toString());
    }
  }

  Future<bool> showAd(String context) async {
    try {
      state = state.copyWith(isLoading: true, errorMessage: null);

      // Use the ad service directly instead of the manager method
      final adService = ref.read(adServiceProvider);
      final success = await adService.showInterstitialAd();

      if (success) {
        state = state.copyWith(
          isLoading: false,
          lastShownContext: context,
          lastShownTime: DateTime.now(),
          isReady: false, // Ad was consumed
        );
        debugPrint('InterstitialAdNotifier: Ad shown successfully for context: $context');
      } else {
        state = state.copyWith(isLoading: false);
        debugPrint('InterstitialAdNotifier: Ad not shown for context: $context (frequency cap or not ready)');
      }

      // Check readiness for next ad
      _checkAdReadiness();

      return success;
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
      debugPrint('InterstitialAdNotifier: Error showing ad for context $context: $e');
      return false;
    }
  }

  void clearError() {
    state = state.copyWith(errorMessage: null);
  }

  void refreshReadiness() {
    _checkAdReadiness();
  }
}

/// Provider for interstitial ad notifier
final interstitialAdNotifierProvider = StateNotifierProvider<InterstitialAdNotifier, InterstitialAdState>((ref) {
  return InterstitialAdNotifier(ref);
});

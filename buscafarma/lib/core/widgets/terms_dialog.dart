import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

/// Material Design 3 dialog for displaying Terms and Conditions
/// Follows app animation standards and accessibility guidelines
/// Supports HTML content rendering with Accept/Decline functionality
class TermsDialog extends StatefulWidget {
  final String htmlContent;

  const TermsDialog({super.key, required this.htmlContent});

  @override
  State<TermsDialog> createState() => _TermsDialogState();
}

class _TermsDialogState extends State<TermsDialog> with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();

    // Initialize animation controllers following app standards (200-300ms)
    _fadeController = AnimationController(duration: const Duration(milliseconds: 250), vsync: this);

    _scaleController = AnimationController(duration: const Duration(milliseconds: 300), vsync: this);

    // Create animations with Material Design 3 curves
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut));

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(CurvedAnimation(parent: _scaleController, curve: Curves.elasticOut));

    // Start staggered animations
    _startAnimations();
  }

  void _startAnimations() {
    _fadeController.forward();
    // Stagger scale animation by 100ms
    Future.delayed(const Duration(milliseconds: 100), () {
      if (mounted) {
        _scaleController.forward();
      }
    });
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return PopScope(
      canPop: false, // Prevent dismissal with back button
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: AlertDialog(
            icon: Icon(Icons.description_outlined, size: 48, color: colorScheme.primary),
            title: Text('Términos y Condiciones', style: theme.textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.w600, color: colorScheme.onSurface), textAlign: TextAlign.center),
            content: SizedBox(
              width: double.maxFinite,
              height: MediaQuery.of(context).size.height * 0.6, // 60% of screen height
              child: SingleChildScrollView(
                child: Html(
                  data: widget.htmlContent,
                  style: {
                    "body": Style(margin: Margins.zero, padding: HtmlPaddings.zero, fontSize: FontSize(14), lineHeight: const LineHeight(1.5), color: colorScheme.onSurfaceVariant),
                    "h1": Style(fontSize: FontSize(20), fontWeight: FontWeight.bold, color: colorScheme.onSurface, margin: Margins.only(bottom: 16)),
                    "h2": Style(fontSize: FontSize(18), fontWeight: FontWeight.w600, color: colorScheme.onSurface, margin: Margins.only(top: 20, bottom: 12)),
                    "p": Style(margin: Margins.only(bottom: 12)),
                    "ul": Style(margin: Margins.only(bottom: 12), padding: HtmlPaddings.all(16)),
                    "li": Style(margin: Margins.only(bottom: 6)),
                    "strong": Style(fontWeight: FontWeight.bold, color: colorScheme.onSurface),
                  },
                ),
              ),
            ),
            actions: [
              // Secondary action - Decline
              TextButton(onPressed: () => Navigator.of(context).pop(false), child: Text('Rechazar', style: TextStyle(color: colorScheme.error))),
              // Primary action - Accept
              FilledButton(onPressed: () => Navigator.of(context).pop(true), child: const Text('Aceptar')),
            ],
            actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
            contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
            titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
            iconPadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
            // Material Design 3 styling
            backgroundColor: colorScheme.surface,
            surfaceTintColor: colorScheme.surfaceTint,
            elevation: 6,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(28)),
            // Semantic labels for accessibility
            semanticLabel: 'Términos y Condiciones de BuscaFarma',
          ),
        ),
      ),
    );
  }
}

import 'package:flutter/material.dart';

/// Material Design 3 dialog for displaying informational announcements
/// Follows app animation standards and accessibility guidelines
class AnnouncementDialog extends StatefulWidget {
  final String content;
  
  const AnnouncementDialog({
    super.key,
    required this.content,
  });

  @override
  State<AnnouncementDialog> createState() => _AnnouncementDialogState();
}

class _AnnouncementDialogState extends State<AnnouncementDialog>
    with TickerProviderStateMixin {
  late AnimationController _fadeController;
  late AnimationController _scaleController;
  late Animation<double> _fadeAnimation;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers following app standards (200-300ms)
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 250),
      vsync: this,
    );
    
    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    // Create animations with Material Design 3 curves
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _scaleController,
      curve: Curves.elasticOut,
    ));

    // Start staggered animations
    _startAnimations();
  }

  void _startAnimations() async {
    await Future.delayed(const Duration(milliseconds: 100));
    _fadeController.forward();
    await Future.delayed(const Duration(milliseconds: 50));
    _scaleController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return PopScope(
      canPop: false, // Prevent dismissal with back button
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: AlertDialog(
            icon: Icon(
              Icons.info_outline_rounded,
              size: 48,
              color: colorScheme.primary,
            ),
            title: Text(
              'Mensaje informativo',
              style: theme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.w600,
                color: colorScheme.onSurface,
              ),
              textAlign: TextAlign.center,
            ),
            content: SizedBox(
              width: double.maxFinite,
              child: SingleChildScrollView(
                child: Text(
                  widget.content,
                  style: theme.textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                    height: 1.5,
                  ),
                  textAlign: TextAlign.left,
                ),
              ),
            ),
            actions: [
              // Secondary action - Close
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: Text(
                  'Cerrar',
                  style: TextStyle(color: colorScheme.onSurfaceVariant),
                ),
              ),
              // Primary action - Understood
              FilledButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text('Entendido'),
              ),
            ],
            actionsPadding: const EdgeInsets.fromLTRB(24, 0, 24, 24),
            contentPadding: const EdgeInsets.fromLTRB(24, 20, 24, 24),
            titlePadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
            iconPadding: const EdgeInsets.fromLTRB(24, 24, 24, 0),
            // Material Design 3 styling
            backgroundColor: colorScheme.surface,
            surfaceTintColor: colorScheme.surfaceTint,
            elevation: 6,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(28),
            ),
            // Accessibility
            semanticLabel: 'Mensaje informativo de BuscaFarma',
          ),
        ),
      ),
    );
  }
}

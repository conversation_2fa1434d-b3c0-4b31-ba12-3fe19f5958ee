import 'package:flutter/material.dart';
import 'package:buscafarma/core/widgets/app_logo.dart';

/// A specialized logo widget designed for use in app bars and headers.
/// Provides consistent branding across the application's navigation areas.
class AppLogoHeader extends StatelessWidget {
  /// Whether to include the app name text alongside the logo
  final bool showAppName;
  
  /// The size of the logo
  final double logoSize;
  
  /// Optional color override for the logo
  final Color? logoColor;
  
  /// Optional color override for the app name text
  final Color? textColor;
  
  /// Whether to arrange logo and text horizontally or vertically
  final Axis direction;
  
  /// Spacing between logo and text when both are shown
  final double spacing;

  const AppLogoHeader({
    super.key,
    this.showAppName = false,
    this.logoSize = 32.0,
    this.logoColor,
    this.textColor,
    this.direction = Axis.horizontal,
    this.spacing = 8.0,
  });

  /// Creates a compact header with just the logo (suitable for app bar leading)
  const AppLogoHeader.compact({
    super.key,
    this.logoColor,
  }) : showAppName = false,
       logoSize = 28.0,
       textColor = null,
       direction = Axis.horizontal,
       spacing = 8.0;

  /// Creates a full header with logo and app name (suitable for app bar title)
  const AppLogoHeader.withTitle({
    super.key,
    this.logoColor,
    this.textColor,
    this.direction = Axis.horizontal,
    this.spacing = 12.0,
  }) : showAppName = true,
       logoSize = 32.0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    // Just logo without text
    if (!showAppName) {
      return AppLogo(
        size: logoSize,
        color: logoColor,
      );
    }
    
    // Logo with app name
    final logoWidget = AppLogo(
      size: logoSize,
      color: logoColor,
    );
    
    final textWidget = Text(
      'BuscaFarma',
      style: theme.textTheme.titleLarge?.copyWith(
        color: textColor ?? theme.colorScheme.onSurface,
        fontWeight: FontWeight.bold,
      ),
    );
    
    // Arrange widgets based on direction
    if (direction == Axis.horizontal) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          logoWidget,
          SizedBox(width: spacing),
          textWidget,
        ],
      );
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          logoWidget,
          SizedBox(height: spacing),
          textWidget,
        ],
      );
    }
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:buscafarma/core/animations/animation_constants.dart';

/// Enhanced input field with Material Design 3 styling, animations, and accessibility
class EnhancedInputField extends StatefulWidget {
  final String label;
  final String? hintText;
  final String? initialValue;
  final TextEditingController? controller;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final Function()? onTap;
  final String? Function(String?)? validator;
  final bool enabled;
  final bool readOnly;
  final bool autofocus;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final List<TextInputFormatter>? inputFormatters;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final int? maxLength;
  final int? maxLines;
  final bool obscureText;
  final FocusNode? focusNode;
  final InputFieldVariant variant;
  final bool showCharacterCount;
  final String? semanticLabel;
  final String? errorText;

  const EnhancedInputField({
    super.key,
    required this.label,
    this.hintText,
    this.initialValue,
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.validator,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.keyboardType,
    this.textInputAction,
    this.inputFormatters,
    this.prefixIcon,
    this.suffixIcon,
    this.maxLength,
    this.maxLines = 1,
    this.obscureText = false,
    this.focusNode,
    this.variant = InputFieldVariant.filled,
    this.showCharacterCount = false,
    this.semanticLabel,
    this.errorText,
  });

  @override
  State<EnhancedInputField> createState() => _EnhancedInputFieldState();
}

enum InputFieldVariant {
  filled,
  outlined,
}

class _EnhancedInputFieldState extends State<EnhancedInputField>
    with TickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _focusAnimationController;
  late AnimationController _errorAnimationController;
  late Animation<double> _focusAnimation;
  late Animation<double> _errorShakeAnimation;

  bool _isFocused = false;
  String? _currentError;

  @override
  void initState() {
    super.initState();

    // Initialize controllers
    _controller = widget.controller ?? TextEditingController(text: widget.initialValue);
    _focusNode = widget.focusNode ?? FocusNode();

    // Initialize animation controllers
    _focusAnimationController = AnimationController(
      duration: AnimationConstants.microInteractionDuration,
      vsync: this,
    );

    _errorAnimationController = AnimationController(
      duration: AnimationConstants.errorAnimationDuration,
      vsync: this,
    );

    // Initialize animations
    _focusAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _focusAnimationController,
      curve: AnimationConstants.microInteractionCurve,
    ));

    _errorShakeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _errorAnimationController,
      curve: Curves.elasticIn,
    ));

    // Add focus listener
    _focusNode.addListener(_onFocusChanged);

    // Add text change listener
    _controller.addListener(_onTextChanged);

    // Set initial focus state
    if (widget.autofocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _focusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChanged);
    _controller.removeListener(_onTextChanged);
    
    if (widget.controller == null) {
      _controller.dispose();
    }
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    
    _focusAnimationController.dispose();
    _errorAnimationController.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });

    if (_isFocused) {
      _focusAnimationController.forward();
    } else {
      _focusAnimationController.reverse();
      _validateInput();
    }
  }

  void _onTextChanged() {
    if (widget.onChanged != null) {
      widget.onChanged!(_controller.text);
    }

    // Clear error when user starts typing
    if (_currentError != null) {
      setState(() {
        _currentError = null;
      });
    }
  }

  void _validateInput() {
    if (widget.validator != null) {
      final error = widget.validator!(_controller.text);
      if (error != null && error != _currentError) {
        setState(() {
          _currentError = error;
        });
        _triggerErrorAnimation();
      }
    }
  }

  void _triggerErrorAnimation() {
    _errorAnimationController.reset();
    _errorAnimationController.forward();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    final hasError = _currentError != null || widget.errorText != null;
    final effectiveError = widget.errorText ?? _currentError;

    return Semantics(
      label: widget.semanticLabel ?? widget.label,
      child: AnimatedBuilder(
        animation: Listenable.merge([_focusAnimation, _errorShakeAnimation]),
        builder: (context, child) {
          return Transform.translate(
            offset: hasError
                ? Offset(
                    AnimationConstants.errorShakeOffset * 
                    _errorShakeAnimation.value * 
                    (1 - _errorShakeAnimation.value),
                    0,
                  )
                : Offset.zero,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInputField(theme, colorScheme, hasError),
                if (effectiveError != null) ...[
                  const SizedBox(height: 4),
                  _buildErrorText(theme, effectiveError),
                ],
                if (widget.showCharacterCount && widget.maxLength != null) ...[
                  const SizedBox(height: 4),
                  _buildCharacterCount(theme),
                ],
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInputField(ThemeData theme, ColorScheme colorScheme, bool hasError) {
    final borderColor = hasError
        ? colorScheme.error
        : _isFocused
            ? colorScheme.primary
            : colorScheme.outline;

    final backgroundColor = widget.variant == InputFieldVariant.filled
        ? colorScheme.surfaceContainerHighest.withValues(alpha: 0.5)
        : Colors.transparent;

    return AnimatedContainer(
      duration: AnimationConstants.microInteractionDuration,
      curve: AnimationConstants.microInteractionCurve,
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8.0),
        border: Border.all(
          color: borderColor,
          width: _isFocused ? 2.0 : 1.0,
        ),
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        enabled: widget.enabled,
        readOnly: widget.readOnly,
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        inputFormatters: widget.inputFormatters,
        maxLength: widget.maxLength,
        maxLines: widget.maxLines,
        obscureText: widget.obscureText,
        onTap: widget.onTap,
        onSubmitted: widget.onSubmitted,
        decoration: InputDecoration(
          labelText: widget.label,
          hintText: widget.hintText,
          prefixIcon: widget.prefixIcon,
          suffixIcon: widget.suffixIcon,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 12.0,
            vertical: 16.0,
          ),
          counterText: '', // Hide default counter
          labelStyle: TextStyle(
            color: hasError
                ? colorScheme.error
                : _isFocused
                    ? colorScheme.primary
                    : colorScheme.onSurfaceVariant,
          ),
          hintStyle: TextStyle(
            color: colorScheme.onSurfaceVariant.withValues(alpha: 0.7),
          ),
        ),
        style: TextStyle(
          color: widget.enabled
              ? colorScheme.onSurface
              : colorScheme.onSurface.withValues(alpha: 0.6),
        ),
      ),
    );
  }

  Widget _buildErrorText(ThemeData theme, String errorText) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      child: Text(
        errorText,
        style: theme.textTheme.bodySmall?.copyWith(
          color: theme.colorScheme.error,
        ),
      ),
    );
  }

  Widget _buildCharacterCount(ThemeData theme) {
    final currentLength = _controller.text.length;
    final maxLength = widget.maxLength!;
    final isNearLimit = currentLength > maxLength * 0.8;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12.0),
      child: Text(
        '$currentLength/$maxLength',
        style: theme.textTheme.bodySmall?.copyWith(
          color: isNearLimit
              ? theme.colorScheme.error
              : theme.colorScheme.onSurfaceVariant,
        ),
        textAlign: TextAlign.end,
      ),
    );
  }
}

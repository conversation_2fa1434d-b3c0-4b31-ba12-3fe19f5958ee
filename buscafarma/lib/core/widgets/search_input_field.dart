import 'dart:async';
import 'package:flutter/material.dart';
import 'package:buscafarma/core/widgets/enhanced_input_field.dart';
import 'package:buscafarma/core/validators/input_validators.dart';
import 'package:buscafarma/core/animations/animation_constants.dart';

/// Specialized search input field with debouncing, validation, and search-specific features
class SearchInputField extends StatefulWidget {
  final String label;
  final String? hintText;
  final String? initialValue;
  final Function(String)? onSearchChanged;
  final Function()? onClear;
  final bool enabled;
  final SearchInputType type;
  final Duration debounceDuration;
  final int minSearchLength;
  final bool showSearchIcon;
  final bool showClearButton;
  final String? semanticLabel;

  const SearchInputField({
    super.key,
    required this.label,
    this.hintText,
    this.initialValue,
    this.onSearchChanged,
    this.onClear,
    this.enabled = true,
    this.type = SearchInputType.general,
    this.debounceDuration = const Duration(milliseconds: 500),
    this.minSearchLength = 2,
    this.showSearchIcon = true,
    this.showClearButton = true,
    this.semanticLabel,
  });

  @override
  State<SearchInputField> createState() => _SearchInputFieldState();
}

enum SearchInputType { general, laboratoryName, establishmentName }

class _SearchInputFieldState extends State<SearchInputField> with TickerProviderStateMixin {
  late TextEditingController _controller;
  late AnimationController _clearButtonAnimationController;
  late Animation<double> _clearButtonAnimation;
  Timer? _debounceTimer;
  String? _validationError;

  @override
  void initState() {
    super.initState();

    _controller = TextEditingController(text: widget.initialValue);

    // Initialize clear button animation
    _clearButtonAnimationController = AnimationController(duration: AnimationConstants.microInteractionDuration, vsync: this);

    _clearButtonAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(CurvedAnimation(parent: _clearButtonAnimationController, curve: AnimationConstants.microInteractionCurve));

    // Add text change listener
    _controller.addListener(_onTextChanged);

    // Show clear button if there's initial text
    if (_controller.text.isNotEmpty) {
      _clearButtonAnimationController.forward();
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_onTextChanged);
    _controller.dispose();
    _clearButtonAnimationController.dispose();
    _debounceTimer?.cancel();
    super.dispose();
  }

  void _onTextChanged() {
    final text = _controller.text;

    // Animate clear button visibility
    if (text.isNotEmpty && _clearButtonAnimationController.value == 0.0) {
      _clearButtonAnimationController.forward();
    } else if (text.isEmpty && _clearButtonAnimationController.value == 1.0) {
      _clearButtonAnimationController.reverse();
    }

    // Cancel previous timer
    _debounceTimer?.cancel();

    // Clear validation error when user starts typing
    if (_validationError != null) {
      setState(() {
        _validationError = null;
      });
    }

    // Set up new debounced search
    _debounceTimer = Timer(widget.debounceDuration, () {
      _performSearch(text);
    });
  }

  void _performSearch(String query) {
    final sanitizedQuery = InputValidators.sanitizeInput(query);

    // Validate input based on type
    String? error;
    switch (widget.type) {
      case SearchInputType.laboratoryName:
        error = InputValidators.validateLaboratoryName(sanitizedQuery);
        break;
      case SearchInputType.establishmentName:
        error = InputValidators.validateEstablishmentName(sanitizedQuery);
        break;
      case SearchInputType.general:
        if (sanitizedQuery.isNotEmpty && sanitizedQuery.length < widget.minSearchLength) {
          error = 'Ingrese al menos ${widget.minSearchLength} caracteres para buscar';
        }
        break;
    }

    // Update validation error state
    if (error != null) {
      setState(() {
        _validationError = error;
      });
      return;
    }

    // Perform search if validation passes
    if (widget.onSearchChanged != null) {
      widget.onSearchChanged!(sanitizedQuery);
    }
  }

  void _clearInput() {
    _controller.clear();

    if (widget.onClear != null) {
      widget.onClear!();
    }

    if (widget.onSearchChanged != null) {
      widget.onSearchChanged!('');
    }

    // Clear validation error
    setState(() {
      _validationError = null;
    });
  }

  String? _validateInput(String? value) {
    if (value == null || value.trim().isEmpty) {
      return null; // Empty is allowed for search fields
    }

    switch (widget.type) {
      case SearchInputType.laboratoryName:
        return InputValidators.validateLaboratoryName(value);
      case SearchInputType.establishmentName:
        return InputValidators.validateEstablishmentName(value);
      case SearchInputType.general:
        return InputValidators.validateTextInput(value: value, fieldName: widget.label, minLength: widget.minSearchLength, required: false);
    }
  }

  Widget _buildSuffixIcon() {
    if (!widget.showClearButton) return const SizedBox.shrink();

    return AnimatedBuilder(
      animation: _clearButtonAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _clearButtonAnimation.value,
          child: Opacity(
            opacity: _clearButtonAnimation.value,
            child: IconButton(
              icon: Icon(Icons.clear, color: Theme.of(context).colorScheme.onSurfaceVariant, size: 20),
              onPressed: _clearInput,
              tooltip: 'Limpiar búsqueda',
              constraints: const BoxConstraints(minWidth: 32, minHeight: 32),
              padding: EdgeInsets.zero,
            ),
          ),
        );
      },
    );
  }

  String _getHintText() {
    if (widget.hintText != null) return widget.hintText!;

    switch (widget.type) {
      case SearchInputType.laboratoryName:
        return 'Buscar por laboratorio...';
      case SearchInputType.establishmentName:
        return 'Buscar por nombre de farmacia...';
      case SearchInputType.general:
        return 'Buscar...';
    }
  }

  String _getSemanticLabel() {
    if (widget.semanticLabel != null) return widget.semanticLabel!;

    switch (widget.type) {
      case SearchInputType.laboratoryName:
        return 'Campo de búsqueda para nombre de laboratorio';
      case SearchInputType.establishmentName:
        return 'Campo de búsqueda para nombre de establecimiento';
      case SearchInputType.general:
        return 'Campo de búsqueda general';
    }
  }

  @override
  Widget build(BuildContext context) {
    return EnhancedInputField(
      label: widget.label,
      hintText: _getHintText(),
      controller: _controller,
      enabled: widget.enabled,
      validator: _validateInput,
      errorText: _validationError,
      suffixIcon: _buildSuffixIcon(),
      keyboardType: TextInputType.text,
      textInputAction: TextInputAction.search,
      maxLength: InputValidators.maxSearchLength,
      semanticLabel: _getSemanticLabel(),
      variant: InputFieldVariant.filled,
      onSubmitted: (value) {
        _debounceTimer?.cancel();
        _performSearch(value);
      },
    );
  }
}

/// Helper widget for creating laboratory name search input
class LaboratoryNameSearchField extends StatelessWidget {
  final String? initialValue;
  final Function(String)? onSearchChanged;
  final Function()? onClear;
  final bool enabled;

  const LaboratoryNameSearchField({super.key, this.initialValue, this.onSearchChanged, this.onClear, this.enabled = true});

  @override
  Widget build(BuildContext context) {
    return SearchInputField(label: 'Nombre del laboratorio', initialValue: initialValue, onSearchChanged: onSearchChanged, onClear: onClear, enabled: enabled, type: SearchInputType.laboratoryName);
  }
}

/// Helper widget for creating establishment name search input
class EstablishmentNameSearchField extends StatelessWidget {
  final String? initialValue;
  final Function(String)? onSearchChanged;
  final Function()? onClear;
  final bool enabled;

  const EstablishmentNameSearchField({super.key, this.initialValue, this.onSearchChanged, this.onClear, this.enabled = true});

  @override
  Widget build(BuildContext context) {
    return SearchInputField(label: 'Nombre del establecimiento', initialValue: initialValue, onSearchChanged: onSearchChanged, onClear: onClear, enabled: enabled, type: SearchInputType.establishmentName);
  }
}

import 'package:flutter/material.dart';
import 'package:buscafarma/core/animations/animation_constants.dart';

/// An enhanced loading indicator with state transitions and sophisticated animations
class AnimatedLoadingIndicator extends StatefulWidget {
  final double size;
  final Color? color;
  final double? progress;
  final LoadingState state;
  final Duration animationDuration;

  const AnimatedLoadingIndicator({
    super.key,
    this.size = 40.0,
    this.color,
    this.progress,
    this.state = LoadingState.loading,
    this.animationDuration = AnimationConstants.stateTransitionDuration,
  });

  @override
  State<AnimatedLoadingIndicator> createState() => _AnimatedLoadingIndicatorState();
}

enum LoadingState {
  loading,
  success,
  error,
}

class _AnimatedLoadingIndicatorState extends State<AnimatedLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _stateController;
  late AnimationController _pulseController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();

    // Controller for state transitions
    _stateController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // Controller for pulse animation during loading
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    // Scale animation for state transitions
    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _stateController,
      curve: AnimationConstants.bounceCurve,
    ));

    // Fade animation for smooth transitions
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _stateController,
      curve: AnimationConstants.entranceCurve,
    ));

    // Pulse animation for loading state
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _startAnimations();
  }

  @override
  void didUpdateWidget(AnimatedLoadingIndicator oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.state != widget.state) {
      _handleStateChange();
    }
  }

  void _startAnimations() {
    _stateController.forward();
    if (widget.state == LoadingState.loading) {
      _pulseController.repeat(reverse: true);
    }
  }

  void _handleStateChange() {
    if (widget.state == LoadingState.loading) {
      _pulseController.repeat(reverse: true);
    } else {
      _pulseController.stop();
      _pulseController.reset();
    }
    
    // Restart state animation for smooth transition
    _stateController.reset();
    _stateController.forward();
  }

  @override
  void dispose() {
    _stateController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final effectiveColor = widget.color ?? colorScheme.primary;

    return AnimatedBuilder(
      animation: Listenable.merge([_stateController, _pulseController]),
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: SizedBox(
              width: widget.size,
              height: widget.size,
              child: _buildIndicatorForState(effectiveColor),
            ),
          ),
        );
      },
    );
  }

  Widget _buildIndicatorForState(Color color) {
    switch (widget.state) {
      case LoadingState.loading:
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: CircularProgressIndicator(
            strokeWidth: 3.0,
            valueColor: AlwaysStoppedAnimation<Color>(color),
            value: widget.progress,
          ),
        );

      case LoadingState.success:
        return Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.green,
          ),
          child: Icon(
            Icons.check,
            color: Colors.white,
            size: widget.size * 0.6,
          ),
        );

      case LoadingState.error:
        return Container(
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            color: Colors.red,
          ),
          child: Icon(
            Icons.close,
            color: Colors.white,
            size: widget.size * 0.6,
          ),
        );
    }
  }
}

import 'package:flutter/material.dart';

/// A reusable widget that displays the BuscaFarma application logo
/// with configurable sizing, error handling, and responsive behavior.
class AppLogo extends StatelessWidget {
  /// The size of the logo (width and height will be equal)
  final double size;
  
  /// Optional color filter to apply to the logo
  final Color? color;
  
  /// Whether to show a fallback icon if the logo fails to load
  final bool showFallback;
  
  /// Custom fallback widget to display if logo loading fails
  final Widget? fallbackWidget;

  const AppLogo({
    super.key,
    this.size = 80.0,
    this.color,
    this.showFallback = true,
    this.fallbackWidget,
  });

  /// Creates a small version of the logo suitable for app bars
  const AppLogo.small({
    super.key,
    this.color,
    this.showFallback = true,
    this.fallbackWidget,
  }) : size = 32.0;

  /// Creates a medium version of the logo suitable for cards or lists
  const AppLogo.medium({
    super.key,
    this.color,
    this.showFallback = true,
    this.fallbackWidget,
  }) : size = 48.0;

  /// Creates a large version of the logo suitable for splash screens
  const AppLogo.large({
    super.key,
    this.color,
    this.showFallback = true,
    this.fallbackWidget,
  }) : size = 120.0;

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return SizedBox(
      width: size,
      height: size,
      child: Image.asset(
        'assets/img/logo.png',
        width: size,
        height: size,
        fit: BoxFit.contain,
        color: color,
        colorBlendMode: color != null ? BlendMode.srcIn : null,
        errorBuilder: (context, error, stackTrace) {
          // If logo fails to load and fallback is enabled, show fallback
          if (showFallback) {
            return fallbackWidget ?? _buildDefaultFallback(theme);
          }
          // If no fallback, return empty container
          return const SizedBox.shrink();
        },
        // Add semantic label for accessibility
        semanticLabel: 'BuscaFarma Logo',
      ),
    );
  }

  /// Builds the default fallback widget when logo fails to load
  Widget _buildDefaultFallback(ThemeData theme) {
    return Icon(
      Icons.local_pharmacy_rounded,
      size: size * 0.8, // Slightly smaller than the logo size
      color: color ?? theme.colorScheme.primary,
    );
  }
}

import 'package:flutter/foundation.dart';
import 'package:buscafarma/core/services/remote_config_service.dart';

/// Configuration class for AppLovin MAX ad unit IDs and settings
///
/// This class centralizes all ad-related configuration to make it easy
/// to switch between development (mock) and production ad unit IDs.
///
/// IMPORTANT: Replace all MOCK tokens with real ad unit IDs from your
/// AppLovin MAX dashboard before releasing to production.
class AdConfig {
  // Private constructor to prevent instantiation
  AdConfig._();

  // Remote Config service instance
  static RemoteConfigService? _remoteConfig;

  // Set the Remote Config service instance
  static void setRemoteConfig(RemoteConfigService remoteConfig) {
    _remoteConfig = remoteConfig;
    debugPrint('AdConfig: Remote Config service set');
  }

  // ============================================================================
  // SDK Configuration
  // ============================================================================

  /// AppLovin SDK Key - Replace with your actual SDK key from AppLovin dashboard
  /// Found in: AppLovin Dashboard > Account > General > Keys
  static const String _defaultSdkKey = 'CJ6BXTWo1kigmgL6nUTb0DZFg5n_UyJCvVOqLSiJfG6yGUVPa9eSwyn0aFyx_-izg2Q-SMEu4qo1K2xRyN0z33';

  /// Get SDK key (from Remote Config if available, otherwise default)
  static String get sdkKey {
    if (_remoteConfig != null) {
      final remoteKey = _remoteConfig!.getString('applovin_sdk_key');
      if (remoteKey.isNotEmpty) {
        return remoteKey;
      }
    }
    return _defaultSdkKey;
  }

  // ============================================================================
  // Ad Unit IDs - MOCK VALUES FOR DEVELOPMENT
  // ============================================================================

  /// Default Banner Ad Unit ID for navigation page
  static const String _defaultNavigationBannerAdUnitId = '96f9ae17420b2c57';

  /// Get Banner Ad Unit ID (from Remote Config if available, otherwise default)
  static String get navigationBannerAdUnitId {
    if (_remoteConfig != null) {
      final remoteId = _remoteConfig!.getString('navigation_banner_ad_unit_id');
      if (remoteId.isNotEmpty) {
        return remoteId;
      }
    }
    return _defaultNavigationBannerAdUnitId;
  }

  /// Default MREC (Medium Rectangle) Ad Unit ID for larger banner spaces
  static const String _defaultMrecAdUnitId = 'ac754dbca1f289ad';

  /// Get MREC Ad Unit ID (from Remote Config if available, otherwise default)
  static String get mrecAdUnitId {
    if (_remoteConfig != null) {
      final remoteId = _remoteConfig!.getString('mrec_ad_unit_id');
      if (remoteId.isNotEmpty) {
        return remoteId;
      }
    }
    return _defaultMrecAdUnitId;
  }

  /// Default Interstitial Ad Unit ID for full-screen ads
  static const String _defaultInterstitialAdUnitId = '563c58c849ccad6d';

  /// Get Interstitial Ad Unit ID (from Remote Config if available, otherwise default)
  static String get interstitialAdUnitId {
    if (_remoteConfig != null) {
      final remoteId = _remoteConfig!.getString('interstitial_ad_unit_id');
      if (remoteId.isNotEmpty) {
        return remoteId;
      }
    }
    return _defaultInterstitialAdUnitId;
  }

  /// Default App Open Ad Unit ID for app launch ads
  static const String _defaultAppOpenAdUnitId = '67f966ae4c0fc6a7';

  /// Get App Open Ad Unit ID (from Remote Config if available, otherwise default)
  static String get appOpenAdUnitId {
    if (_remoteConfig != null) {
      final remoteId = _remoteConfig!.getString('app_open_ad_unit_id');
      if (remoteId.isNotEmpty) {
        return remoteId;
      }
    }
    return _defaultAppOpenAdUnitId;
  }

  // ============================================================================
  // Ad Behavior Configuration
  // ============================================================================

  /// Default minimum time between interstitial ads (in seconds)
  static const int _defaultInterstitialCooldownSeconds = 60;

  /// Get interstitial cooldown period (from Remote Config if available, otherwise default)
  static int get interstitialCooldownSeconds {
    if (_remoteConfig != null) {
      return _remoteConfig!.getInt('interstitial_cooldown_seconds');
    }
    return _defaultInterstitialCooldownSeconds;
  }

  /// Default maximum number of interstitial ads per session
  static const int _defaultMaxInterstitialsPerSession = 5;

  /// Get maximum interstitials per session (from Remote Config if available, otherwise default)
  static int get maxInterstitialsPerSession {
    if (_remoteConfig != null) {
      return _remoteConfig!.getInt('max_interstitials_per_session');
    }
    return _defaultMaxInterstitialsPerSession;
  }

  /// Default minimum time between app open ads (in seconds)
  static const int _defaultAppOpenCooldownSeconds = 240; // 4 minutes

  /// Get app open cooldown period (from Remote Config if available, otherwise default)
  static int get appOpenCooldownSeconds {
    if (_remoteConfig != null) {
      return _remoteConfig!.getInt('app_open_cooldown_seconds');
    }
    return _defaultAppOpenCooldownSeconds;
  }

  /// Whether to show ads in debug mode (Remote Config overridable)
  static bool get showAdsInDebug {
    // If in release mode, always return true
    if (!kDebugMode) return true;

    // In debug mode, check Remote Config
    if (_remoteConfig != null) {
      return _remoteConfig!.getBool('show_ads_in_debug');
    }
    return true; // Default to showing ads in debug
  }

  /// Timeout for ad loading (in seconds)
  static int get adLoadTimeoutSeconds {
    if (_remoteConfig != null) {
      return _remoteConfig!.getInt('ad_load_timeout_seconds');
    }
    return 10; // Default timeout
  }

  // ============================================================================
  // Banner Ad Configuration (AppLovin MAX 4.5.0+)
  // ============================================================================

  /// Enable adaptive banners for better user experience
  static bool get enableAdaptiveBanners {
    if (_remoteConfig != null) {
      return _remoteConfig!.getBool('enable_adaptive_banners');
    }
    return true; // Default
  }

  /// Enable auto-refresh for banner ads
  static bool get enableBannerAutoRefresh {
    if (_remoteConfig != null) {
      return _remoteConfig!.getBool('enable_banner_auto_refresh');
    }
    return true; // Default
  }

  /// Enable banner preloading for better performance
  static bool get enableBannerPreloading {
    if (_remoteConfig != null) {
      return _remoteConfig!.getBool('enable_banner_preloading');
    }
    return true; // Default
  }

  // ============================================================================
  // Helper Methods
  // ============================================================================

  /// Check if we're using mock ad unit IDs
  /// Returns true if any ad unit ID contains "MOCK"
  static bool get isUsingMockIds {
    return mrecAdUnitId.contains('MOCK') || interstitialAdUnitId.contains('MOCK') || appOpenAdUnitId.contains('MOCK') || sdkKey.contains('MOCK');
  }

  /// Get a warning message if using mock IDs in production
  static String? get mockIdWarning {
    if (isUsingMockIds) {
      return 'WARNING: Using mock ad unit IDs. Replace with production IDs before release.';
    }
    return null;
  }

  /// Validate that all required ad unit IDs are configured
  static bool get isConfigurationValid {
    return sdkKey.isNotEmpty && mrecAdUnitId.isNotEmpty && interstitialAdUnitId.isNotEmpty && appOpenAdUnitId.isNotEmpty;
  }
}

/// Instructions for replacing mock tokens with production values:
///
/// 1. Log into your AppLovin MAX dashboard
/// 2. Navigate to Mediation > Manage > Ad Units
/// 3. Create ad units for each type (Banner, MREC, Interstitial, App Open)
/// 4. Copy the ad unit IDs and replace the MOCK values above
/// 5. Get your SDK key from Account > General > Keys
/// 6. Replace the MOCK_SDK_KEY_REPLACE_WITH_REAL_KEY with your actual SDK key
///
/// Example production configuration:
/// ```dart
/// static const String sdkKey = 'YOUR_ACTUAL_SDK_KEY_HERE';
/// static const String bannerAdUnitId = 'YOUR_BANNER_AD_UNIT_ID';
/// static const String mrecAdUnitId = 'YOUR_MREC_AD_UNIT_ID';
/// static const String interstitialAdUnitId = 'YOUR_INTERSTITIAL_AD_UNIT_ID';
/// static const String appOpenAdUnitId = 'YOUR_APP_OPEN_AD_UNIT_ID';
/// ```

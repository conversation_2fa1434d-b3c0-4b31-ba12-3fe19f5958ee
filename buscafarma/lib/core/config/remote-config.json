{"parameters": {"api_base_url_prod": {"defaultValue": {"value": "https://backend-production-ecfc.up.railway.app/api/v1"}, "description": "URL base de la API para el entorno de producción", "valueType": "STRING"}, "api_base_url_dev": {"defaultValue": {"value": "http://********:3000/api/v1"}, "description": "URL base de la API para desarrollo en emulador Android", "valueType": "STRING"}, "api_base_url_ios_dev": {"defaultValue": {"value": "http://localhost:3000/api/v1"}, "description": "URL base de la API para desarrollo en simulador iOS", "valueType": "STRING"}, "enable_ads": {"defaultValue": {"value": "true"}, "description": "Habilita o deshabilita la visualización de anuncios en la aplicación", "valueType": "BOOLEAN"}, "ads_frequency": {"defaultValue": {"value": "3"}, "description": "Número de acciones de navegación antes de mostrar un anuncio", "valueType": "NUMBER"}, "review_minimum_session_count": {"defaultValue": {"value": "3"}, "description": "Número mínimo de sesiones antes de mostrar prompts de valoración", "valueType": "NUMBER"}, "review_minimum_positive_interactions": {"defaultValue": {"value": "2"}, "description": "Interacciones positivas mínimas antes de solicitar valoración", "valueType": "NUMBER"}, "review_minimum_days_since_install": {"defaultValue": {"value": "2"}, "description": "Días mínimos desde la instalación para mostrar prompts de valoración", "valueType": "NUMBER"}, "review_days_between_prompts": {"defaultValue": {"value": "30"}, "description": "Días entre prompts de valoración", "valueType": "NUMBER"}, "review_days_after_dismissal": {"defaultValue": {"value": "90"}, "description": "Días después de descartar antes de volver a mostrar prompt", "valueType": "NUMBER"}, "review_days_after_remind_later": {"defaultValue": {"value": "7"}, "description": "Días después de 'recordar más tarde' antes de volver a mostrar prompt", "valueType": "NUMBER"}, "enable_in_app_update": {"defaultValue": {"value": "true"}, "description": "Habilitar actualizaciones en la aplicación", "valueType": "BOOLEAN"}, "enable_health_data_feature": {"defaultValue": {"value": "true"}, "description": "Habilitar característica de datos de salud", "valueType": "BOOLEAN"}, "api_timeout_seconds": {"defaultValue": {"value": "10"}, "description": "Tiempo de espera para peticiones API en segundos", "valueType": "NUMBER"}, "api_retry_count": {"defaultValue": {"value": "3"}, "description": "Número de reintentos para peticiones API fallidas", "valueType": "NUMBER"}, "primary_color": {"defaultValue": {"value": "#2196F3"}, "description": "Color primario de la aplicación (hex)", "valueType": "STRING"}, "background_color": {"defaultValue": {"value": "#FFFFFF"}, "description": "Color de fondo de la aplicación (hex)", "valueType": "STRING"}, "text_color": {"defaultValue": {"value": "#000000"}, "description": "Color de texto principal (hex)", "valueType": "STRING"}, "navigation_banner_ad_unit_id": {"defaultValue": {"value": "96f9ae17420b2c57"}, "description": "ID de unidad de anuncio para banners de navegación", "valueType": "STRING"}, "mrec_ad_unit_id": {"defaultValue": {"value": "ac754dbca1f289ad"}, "description": "ID de unidad de anuncio para MREC", "valueType": "STRING"}, "interstitial_ad_unit_id": {"defaultValue": {"value": "563c58c849ccad6d"}, "description": "ID de unidad de anuncio para intersticiales", "valueType": "STRING"}, "app_open_ad_unit_id": {"defaultValue": {"value": "67f966ae4c0fc6a7"}, "description": "ID de unidad de anuncio para apertura de app", "valueType": "STRING"}, "applovin_sdk_key": {"defaultValue": {"value": "CJ6BXTWo1kigmgL6nUTb0DZFg5n_UyJCvVOqLSiJfG6yGUVPa9eSwyn0aFyx_-izg2Q-SMEu4qo1K2xRyN0z33"}, "description": "Clave SDK de AppLovin", "valueType": "STRING"}}, "conditions": [{"name": "ad_frequency_low", "expression": "device.os == 'ios'", "tagColor": "BLUE"}, {"name": "ad_frequency_high", "expression": "device.os == 'android'", "tagColor": "GREEN"}]}
import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

@freezed
abstract class Failure with _$Failure {
  const factory Failure.locationPermissionDenied() = LocationPermissionDenied;
  const factory Failure.locationServiceDisabled() = LocationServiceDisabled;
  const factory Failure.serverError([String? message]) = ServerError;
  const factory Failure.networkError() = NetworkError;
  const factory Failure.unexpectedError([String? message]) = UnexpectedError;

  // Auth failures
  const factory Failure.authCancelled() = AuthCancelled;
  const factory Failure.authFailed([String? message]) = AuthFailed;
  const factory Failure.notAuthenticated() = NotAuthenticated;

  // Review failures
  const factory Failure.reviewNotAvailable([String? message]) = ReviewNotAvailable;
  const factory Failure.reviewPromptError([String? message]) = ReviewPromptError;

  // Update failures
  const factory Failure.updateNotAvailable([String? message]) = UpdateNotAvailable;
  const factory Failure.updateDownloadFailed([String? message]) = UpdateDownloadFailed;
  const factory Failure.updateInstallationFailed([String? message]) = UpdateInstallationFailed;
  const factory Failure.updateCheckFailed([String? message]) = UpdateCheckFailed;
}

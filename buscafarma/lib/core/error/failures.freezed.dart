// dart format width=80
// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'failures.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

// dart format off
T _$identity<T>(T value) => value;
/// @nodoc
mixin _$Failure {





@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is Failure);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'Failure()';
}


}

/// @nodoc
class $FailureCopyWith<$Res>  {
$FailureCopyWith(Failure _, $Res Function(Failure) __);
}


/// @nodoc


class LocationPermissionDenied implements Failure {
  const LocationPermissionDenied();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LocationPermissionDenied);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'Failure.locationPermissionDenied()';
}


}




/// @nodoc


class LocationServiceDisabled implements Failure {
  const LocationServiceDisabled();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LocationServiceDisabled);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'Failure.locationServiceDisabled()';
}


}




/// @nodoc


class ServerError implements Failure {
  const ServerError([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ServerErrorCopyWith<ServerError> get copyWith => _$ServerErrorCopyWithImpl<ServerError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ServerError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.serverError(message: $message)';
}


}

/// @nodoc
abstract mixin class $ServerErrorCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $ServerErrorCopyWith(ServerError value, $Res Function(ServerError) _then) = _$ServerErrorCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$ServerErrorCopyWithImpl<$Res>
    implements $ServerErrorCopyWith<$Res> {
  _$ServerErrorCopyWithImpl(this._self, this._then);

  final ServerError _self;
  final $Res Function(ServerError) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(ServerError(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class NetworkError implements Failure {
  const NetworkError();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NetworkError);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'Failure.networkError()';
}


}




/// @nodoc


class UnexpectedError implements Failure {
  const UnexpectedError([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UnexpectedErrorCopyWith<UnexpectedError> get copyWith => _$UnexpectedErrorCopyWithImpl<UnexpectedError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UnexpectedError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.unexpectedError(message: $message)';
}


}

/// @nodoc
abstract mixin class $UnexpectedErrorCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $UnexpectedErrorCopyWith(UnexpectedError value, $Res Function(UnexpectedError) _then) = _$UnexpectedErrorCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$UnexpectedErrorCopyWithImpl<$Res>
    implements $UnexpectedErrorCopyWith<$Res> {
  _$UnexpectedErrorCopyWithImpl(this._self, this._then);

  final UnexpectedError _self;
  final $Res Function(UnexpectedError) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(UnexpectedError(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class AuthCancelled implements Failure {
  const AuthCancelled();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthCancelled);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'Failure.authCancelled()';
}


}




/// @nodoc


class AuthFailed implements Failure {
  const AuthFailed([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$AuthFailedCopyWith<AuthFailed> get copyWith => _$AuthFailedCopyWithImpl<AuthFailed>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is AuthFailed&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.authFailed(message: $message)';
}


}

/// @nodoc
abstract mixin class $AuthFailedCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $AuthFailedCopyWith(AuthFailed value, $Res Function(AuthFailed) _then) = _$AuthFailedCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$AuthFailedCopyWithImpl<$Res>
    implements $AuthFailedCopyWith<$Res> {
  _$AuthFailedCopyWithImpl(this._self, this._then);

  final AuthFailed _self;
  final $Res Function(AuthFailed) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(AuthFailed(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class NotAuthenticated implements Failure {
  const NotAuthenticated();
  






@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is NotAuthenticated);
}


@override
int get hashCode => runtimeType.hashCode;

@override
String toString() {
  return 'Failure.notAuthenticated()';
}


}




/// @nodoc


class ReviewNotAvailable implements Failure {
  const ReviewNotAvailable([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ReviewNotAvailableCopyWith<ReviewNotAvailable> get copyWith => _$ReviewNotAvailableCopyWithImpl<ReviewNotAvailable>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ReviewNotAvailable&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.reviewNotAvailable(message: $message)';
}


}

/// @nodoc
abstract mixin class $ReviewNotAvailableCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $ReviewNotAvailableCopyWith(ReviewNotAvailable value, $Res Function(ReviewNotAvailable) _then) = _$ReviewNotAvailableCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$ReviewNotAvailableCopyWithImpl<$Res>
    implements $ReviewNotAvailableCopyWith<$Res> {
  _$ReviewNotAvailableCopyWithImpl(this._self, this._then);

  final ReviewNotAvailable _self;
  final $Res Function(ReviewNotAvailable) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(ReviewNotAvailable(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class ReviewPromptError implements Failure {
  const ReviewPromptError([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$ReviewPromptErrorCopyWith<ReviewPromptError> get copyWith => _$ReviewPromptErrorCopyWithImpl<ReviewPromptError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is ReviewPromptError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.reviewPromptError(message: $message)';
}


}

/// @nodoc
abstract mixin class $ReviewPromptErrorCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $ReviewPromptErrorCopyWith(ReviewPromptError value, $Res Function(ReviewPromptError) _then) = _$ReviewPromptErrorCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$ReviewPromptErrorCopyWithImpl<$Res>
    implements $ReviewPromptErrorCopyWith<$Res> {
  _$ReviewPromptErrorCopyWithImpl(this._self, this._then);

  final ReviewPromptError _self;
  final $Res Function(ReviewPromptError) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(ReviewPromptError(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class UpdateNotAvailable implements Failure {
  const UpdateNotAvailable([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateNotAvailableCopyWith<UpdateNotAvailable> get copyWith => _$UpdateNotAvailableCopyWithImpl<UpdateNotAvailable>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateNotAvailable&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.updateNotAvailable(message: $message)';
}


}

/// @nodoc
abstract mixin class $UpdateNotAvailableCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $UpdateNotAvailableCopyWith(UpdateNotAvailable value, $Res Function(UpdateNotAvailable) _then) = _$UpdateNotAvailableCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$UpdateNotAvailableCopyWithImpl<$Res>
    implements $UpdateNotAvailableCopyWith<$Res> {
  _$UpdateNotAvailableCopyWithImpl(this._self, this._then);

  final UpdateNotAvailable _self;
  final $Res Function(UpdateNotAvailable) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(UpdateNotAvailable(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class UpdateDownloadFailed implements Failure {
  const UpdateDownloadFailed([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateDownloadFailedCopyWith<UpdateDownloadFailed> get copyWith => _$UpdateDownloadFailedCopyWithImpl<UpdateDownloadFailed>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateDownloadFailed&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.updateDownloadFailed(message: $message)';
}


}

/// @nodoc
abstract mixin class $UpdateDownloadFailedCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $UpdateDownloadFailedCopyWith(UpdateDownloadFailed value, $Res Function(UpdateDownloadFailed) _then) = _$UpdateDownloadFailedCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$UpdateDownloadFailedCopyWithImpl<$Res>
    implements $UpdateDownloadFailedCopyWith<$Res> {
  _$UpdateDownloadFailedCopyWithImpl(this._self, this._then);

  final UpdateDownloadFailed _self;
  final $Res Function(UpdateDownloadFailed) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(UpdateDownloadFailed(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class UpdateInstallationFailed implements Failure {
  const UpdateInstallationFailed([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateInstallationFailedCopyWith<UpdateInstallationFailed> get copyWith => _$UpdateInstallationFailedCopyWithImpl<UpdateInstallationFailed>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateInstallationFailed&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.updateInstallationFailed(message: $message)';
}


}

/// @nodoc
abstract mixin class $UpdateInstallationFailedCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $UpdateInstallationFailedCopyWith(UpdateInstallationFailed value, $Res Function(UpdateInstallationFailed) _then) = _$UpdateInstallationFailedCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$UpdateInstallationFailedCopyWithImpl<$Res>
    implements $UpdateInstallationFailedCopyWith<$Res> {
  _$UpdateInstallationFailedCopyWithImpl(this._self, this._then);

  final UpdateInstallationFailed _self;
  final $Res Function(UpdateInstallationFailed) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(UpdateInstallationFailed(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class UpdateCheckFailed implements Failure {
  const UpdateCheckFailed([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$UpdateCheckFailedCopyWith<UpdateCheckFailed> get copyWith => _$UpdateCheckFailedCopyWithImpl<UpdateCheckFailed>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is UpdateCheckFailed&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.updateCheckFailed(message: $message)';
}


}

/// @nodoc
abstract mixin class $UpdateCheckFailedCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $UpdateCheckFailedCopyWith(UpdateCheckFailed value, $Res Function(UpdateCheckFailed) _then) = _$UpdateCheckFailedCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$UpdateCheckFailedCopyWithImpl<$Res>
    implements $UpdateCheckFailedCopyWith<$Res> {
  _$UpdateCheckFailedCopyWithImpl(this._self, this._then);

  final UpdateCheckFailed _self;
  final $Res Function(UpdateCheckFailed) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(UpdateCheckFailed(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class TermsNotAccepted implements Failure {
  const TermsNotAccepted([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$TermsNotAcceptedCopyWith<TermsNotAccepted> get copyWith => _$TermsNotAcceptedCopyWithImpl<TermsNotAccepted>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is TermsNotAccepted&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.termsNotAccepted(message: $message)';
}


}

/// @nodoc
abstract mixin class $TermsNotAcceptedCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $TermsNotAcceptedCopyWith(TermsNotAccepted value, $Res Function(TermsNotAccepted) _then) = _$TermsNotAcceptedCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$TermsNotAcceptedCopyWithImpl<$Res>
    implements $TermsNotAcceptedCopyWith<$Res> {
  _$TermsNotAcceptedCopyWithImpl(this._self, this._then);

  final TermsNotAccepted _self;
  final $Res Function(TermsNotAccepted) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(TermsNotAccepted(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

/// @nodoc


class LegalDocumentError implements Failure {
  const LegalDocumentError([this.message]);
  

 final  String? message;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@JsonKey(includeFromJson: false, includeToJson: false)
@pragma('vm:prefer-inline')
$LegalDocumentErrorCopyWith<LegalDocumentError> get copyWith => _$LegalDocumentErrorCopyWithImpl<LegalDocumentError>(this, _$identity);



@override
bool operator ==(Object other) {
  return identical(this, other) || (other.runtimeType == runtimeType&&other is LegalDocumentError&&(identical(other.message, message) || other.message == message));
}


@override
int get hashCode => Object.hash(runtimeType,message);

@override
String toString() {
  return 'Failure.legalDocumentError(message: $message)';
}


}

/// @nodoc
abstract mixin class $LegalDocumentErrorCopyWith<$Res> implements $FailureCopyWith<$Res> {
  factory $LegalDocumentErrorCopyWith(LegalDocumentError value, $Res Function(LegalDocumentError) _then) = _$LegalDocumentErrorCopyWithImpl;
@useResult
$Res call({
 String? message
});




}
/// @nodoc
class _$LegalDocumentErrorCopyWithImpl<$Res>
    implements $LegalDocumentErrorCopyWith<$Res> {
  _$LegalDocumentErrorCopyWithImpl(this._self, this._then);

  final LegalDocumentError _self;
  final $Res Function(LegalDocumentError) _then;

/// Create a copy of Failure
/// with the given fields replaced by the non-null parameter values.
@pragma('vm:prefer-inline') $Res call({Object? message = freezed,}) {
  return _then(LegalDocumentError(
freezed == message ? _self.message : message // ignore: cast_nullable_to_non_nullable
as String?,
  ));
}


}

// dart format on

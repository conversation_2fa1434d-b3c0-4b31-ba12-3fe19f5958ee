import 'dart:async';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:buscafarma/core/utils/api_config.dart';
import 'package:buscafarma/features/auth/data/providers/auth_local_provider.dart';

class TokenRefreshInterceptor extends Interceptor {
  final Dio dio;
  final AuthLocalProvider authLocalProvider;

  // Flag to prevent multiple simultaneous refresh token requests
  bool _isRefreshing = false;

  // Queue of requests that failed due to token expiration and are awaiting retry
  List<Map<String, dynamic>> _pendingRequests = [];

  TokenRefreshInterceptor({required this.dio, required this.authLocalProvider});

  @override
  Future<void> onRequest(RequestOptions options, RequestInterceptorHandler handler) async {
    // Skip authentication for login and token refresh endpoints
    if (_isLoginOrRefreshRequest(options.path)) {
      return handler.next(options);
    }

    // Get current access token from storage
    final accessToken = await authLocalProvider.getAccessToken();

    if (accessToken != null) {
      options.headers['Authorization'] = 'Bearer $accessToken';
    }

    return handler.next(options);
  }

  @override
  void onResponse(Response response, ResponseInterceptorHandler handler) async {
    // Check for token expiration messages in the response body
    // even if HTTP status code is 200
    if (response.statusCode == 200 && response.data is Map<String, dynamic>) {
      final responseData = response.data as Map<String, dynamic>;

      // Check if response contains token expiration error
      if (_isTokenExpiredMessage(responseData)) {
        debugPrint('Detected expired token in response body');

        final refreshToken = await authLocalProvider.getRefreshToken();

        // If no refresh token is available, pass through the response
        if (refreshToken == null || refreshToken.isEmpty) {
          debugPrint('No refresh token available.');
          return handler.next(response);
        }

        // Handle token refresh and retry
        if (!_isRefreshing) {
          _isRefreshing = true;

          try {
            final newAccessToken = await _refreshToken(refreshToken);

            if (newAccessToken != null) {
              // Retry the original request with new token
              final retryRequestOptions = response.requestOptions;
              retryRequestOptions.headers['Authorization'] = 'Bearer $newAccessToken';

              try {
                final retryResponse = await dio.fetch(retryRequestOptions);
                handler.resolve(retryResponse);
                return;
              } catch (e) {
                debugPrint('Error retrying request after token refresh: $e');
                // If retry fails, return original response
                handler.next(response);
              }
            } else {
              // Couldn't refresh token
              handler.next(response);
            }
          } catch (refreshError) {
            debugPrint('Error refreshing token: $refreshError');
            handler.next(response);
          } finally {
            _isRefreshing = false;
          }
        } else {
          // Already refreshing, add to queue to be processed later
          // But for onResponse we can't really queue it effectively, so return original
          handler.next(response);
        }
      } else {
        // Response doesn't have token expiration message, proceed normally
        handler.next(response);
      }
    } else {
      // Regular response, proceed normally
      handler.next(response);
    }
  }

  @override
  Future<void> onError(DioException err, ErrorInterceptorHandler handler) async {
    // Check if the error is due to an expired token (401 Unauthorized)
    if (err.response?.statusCode == 401) {
      final refreshToken = await authLocalProvider.getRefreshToken();

      // If no refresh token is available, pass through the error
      if (refreshToken == null || refreshToken.isEmpty) {
        debugPrint('No refresh token available.');
        return handler.next(err);
      }

      // Original request that failed
      final RequestOptions options = err.requestOptions;

      // Handle refresh token logic
      if (!_isRefreshing) {
        _isRefreshing = true;

        try {
          // Attempt to refresh the token
          final newAccessToken = await _refreshToken(refreshToken);

          if (newAccessToken != null) {
            // Update options with new token
            options.headers['Authorization'] = 'Bearer $newAccessToken';

            // Retry the original request
            await _retryRequest(options, handler);

            // Process any pending requests with the new token
            await _processPendingRequests(newAccessToken);
          } else {
            // If we couldn't refresh, pass along the original error
            handler.next(err);
          }
        } catch (refreshError) {
          debugPrint('Error refreshing token: $refreshError');
          // If refresh fails, pass along the original error
          handler.next(err);
        } finally {
          _isRefreshing = false;
        }
      } else {
        // If we're already refreshing, add this request to the queue
        _pendingRequests.add({'options': options, 'handler': handler, 'error': err});
      }
    } else {
      // Check for token expiration in error response body with other status codes
      if (err.response?.data is Map<String, dynamic> && _isTokenExpiredMessage(err.response!.data as Map<String, dynamic>)) {
        debugPrint('Detected expired token in error response body');

        final refreshToken = await authLocalProvider.getRefreshToken();

        // If no refresh token is available, pass through the error
        if (refreshToken == null || refreshToken.isEmpty) {
          debugPrint('No refresh token available.');
          return handler.next(err);
        }

        // Original request that failed
        final RequestOptions options = err.requestOptions;

        // Handle refresh token logic
        if (!_isRefreshing) {
          _isRefreshing = true;

          try {
            // Attempt to refresh the token
            final newAccessToken = await _refreshToken(refreshToken);

            if (newAccessToken != null) {
              // Update options with new token
              options.headers['Authorization'] = 'Bearer $newAccessToken';

              // Retry the original request
              await _retryRequest(options, handler);

              // Process any pending requests with the new token
              await _processPendingRequests(newAccessToken);
            } else {
              // If we couldn't refresh, pass along the original error
              handler.next(err);
            }
          } catch (refreshError) {
            debugPrint('Error refreshing token: $refreshError');
            // If refresh fails, pass along the original error
            handler.next(err);
          } finally {
            _isRefreshing = false;
          }
        } else {
          // If we're already refreshing, add this request to the queue
          _pendingRequests.add({'options': options, 'handler': handler, 'error': err});
        }
      } else {
        // For errors not related to token expiration, just pass them through
        return handler.next(err);
      }
    }
  }

  /// Check if a response contains a token expired message
  bool _isTokenExpiredMessage(Map<String, dynamic> responseData) {
    // Check the message for token expiration
    final message = responseData['message'] as String?;
    if (message != null && (message.contains('jwt expired') || message.contains('token expired') || message.contains('invalid token') || message.contains('token inválido') || message.contains('unauthorized'))) {
      return true;
    }

    // Check for specific status codes in response body that indicate auth issues
    final status = responseData['status'];
    if (status != null && (status == 401 || status == 403 || status == 500 && message != null && message.toLowerCase().contains('jwt'))) {
      return true;
    }

    return false;
  }

  bool _isLoginOrRefreshRequest(String path) {
    return path.contains('/firebase/login') || path.contains('/refresh-token');
  }

  Future<String?> _refreshToken(String refreshToken) async {
    try {
      debugPrint('Attempting to refresh token');

      // Create a new Dio instance to avoid interceptor loops
      final refreshDio = Dio(BaseOptions(baseUrl: ApiConfig.baseUrl, connectTimeout: Duration(seconds: ApiConfig.timeoutSeconds), receiveTimeout: Duration(seconds: ApiConfig.timeoutSeconds)));

      // Call the token refresh endpoint
      final response = await refreshDio.post('/users/refresh-token', data: {'refreshToken': refreshToken});

      if (response.statusCode == 200 && response.data != null) {
        final responseData = response.data;
        if (responseData is Map<String, dynamic> && responseData.containsKey('data') && responseData['data'] is Map<String, dynamic> && responseData['data'].containsKey('accessToken')) {
          final newAccessToken = responseData['data']['accessToken'] as String;

          // Save the new token
          await authLocalProvider.saveAccessToken(newAccessToken);

          debugPrint('Token refreshed successfully');
          return newAccessToken;
        } else {
          debugPrint('Invalid token refresh response format: $responseData');
          return null;
        }
      } else {
        debugPrint('Failed to refresh token: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      debugPrint('Error refreshing token: $e');
      // If the refresh token itself is expired or invalid, clear the stored tokens
      if (e is DioException && (e.response?.statusCode == 401 || e.response?.statusCode == 403)) {
        await authLocalProvider.clearAuthData();
      }
      return null;
    }
  }

  Future<void> _retryRequest(RequestOptions options, ErrorInterceptorHandler handler) async {
    try {
      final retryResponse = await dio.request(
        options.path,
        data: options.data,
        queryParameters: options.queryParameters,
        options: Options(method: options.method, headers: options.headers, contentType: options.contentType, responseType: options.responseType, extra: options.extra),
      );

      handler.resolve(retryResponse);
    } catch (e) {
      if (e is DioException) {
        handler.next(e);
      } else {
        handler.next(DioException(requestOptions: options, error: e, message: 'Error during retry: ${e.toString()}'));
      }
    }
  }

  Future<void> _processPendingRequests(String newToken) async {
    final requests = List<Map<String, dynamic>>.from(_pendingRequests);
    _pendingRequests = [];

    for (final request in requests) {
      final RequestOptions options = request['options'];
      final ErrorInterceptorHandler handler = request['handler'];

      options.headers['Authorization'] = 'Bearer $newToken';

      try {
        final retryResponse = await dio.request(
          options.path,
          data: options.data,
          queryParameters: options.queryParameters,
          options: Options(method: options.method, headers: options.headers, contentType: options.contentType, responseType: options.responseType, extra: options.extra),
        );

        handler.resolve(retryResponse);
      } catch (e) {
        if (e is DioException) {
          handler.next(e);
        } else {
          handler.next(request['error']);
        }
      }
    }
  }
}

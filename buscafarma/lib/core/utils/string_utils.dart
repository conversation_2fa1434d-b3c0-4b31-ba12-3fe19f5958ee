import 'dart:math';

/// Normaliza un string removiendo acentos, convirtiendo a minúsculas
/// y eliminando caracteres especiales para facilitar comparaciones.
String normalizeString(String input) {
  String normalized = input.toLowerCase();
  normalized = normalized.replaceAll('á', 'a').replaceAll('é', 'e').replaceAll('í', 'i').replaceAll('ó', 'o').replaceAll('ú', 'u').replaceAll('ü', 'u').replaceAll('ñ', 'n').replaceAll(RegExp(r'[^a-z0-9\s]'), '').trim();
  return normalized;
}

/// Calcula la distancia de Levenshtein entre dos strings.
///
/// La distancia de Levenshtein es la cantidad mínima de operaciones
/// (inserción, eliminación o sustitución) necesarias para transformar
/// una cadena en otra.
///
/// Cuanto menor sea el valor, más similares son las cadenas.
///
/// Ejemplo:
/// ```dart
/// final distance = levenshteinDistance('kitten', 'sitting'); // 3
/// ```
int levenshteinDistance(String a, String b) {
  // Si alguna cadena es vacía, la distancia es la longitud de la otra
  if (a.isEmpty) return b.length;
  if (b.isEmpty) return a.length;

  // Normalizamos ambas cadenas para mejorar comparaciones
  final String s1 = normalizeString(a);
  final String s2 = normalizeString(b);

  // Longitudes de las cadenas
  final int m = s1.length;
  final int n = s2.length;

  // Creamos matriz para programación dinámica
  List<List<int>> d = List.generate(m + 1, (_) => List.filled(n + 1, 0));

  // Inicializamos la primera fila y columna
  for (int i = 0; i <= m; i++) {
    d[i][0] = i;
  }
  for (int j = 0; j <= n; j++) {
    d[0][j] = j;
  }

  // Llenamos la matriz
  for (int i = 1; i <= m; i++) {
    for (int j = 1; j <= n; j++) {
      int cost = s1[i - 1] == s2[j - 1] ? 0 : 1;
      d[i][j] = min(min(d[i - 1][j] + 1, d[i][j - 1] + 1), d[i - 1][j - 1] + cost);
    }
  }

  // El resultado es el valor en la esquina inferior derecha
  return d[m][n];
}

/// Encuentra el elemento con la string más similar en una lista.
///
/// Retorna el elemento de la lista cuyo nombre (obtenido con getName)
/// tenga la menor distancia de Levenshtein respecto a la cadena de referencia.
T? findClosestMatch<T>(String reference, List<T> items, String Function(T) getName) {
  if (items.isEmpty) return null;
  if (reference.isEmpty) return null;

  T? closest;
  int minDistance = double.maxFinite.toInt();

  for (final item in items) {
    final itemName = getName(item);
    final distance = levenshteinDistance(reference, itemName);

    if (distance < minDistance) {
      minDistance = distance;
      closest = item;
    }
  }

  return closest;
}

import 'package:flutter/foundation.dart';
import 'package:buscafarma/core/services/remote_config_service.dart';

class ApiConfig {
  // Instancia de RemoteConfig
  static RemoteConfigService? _remoteConfig;

  // Establece la instancia de RemoteConfig
  static void setRemoteConfig(RemoteConfigService? remoteConfig) {
    _remoteConfig = remoteConfig;
    if (remoteConfig != null) {
      debugPrint('ApiConfig: Remote Config service establecido');
    } else {
      debugPrint('ApiConfig: Remote Config service is null, using default values');
    }
  }

  // Base URL para la API
  static String get baseUrl {
    // Si RemoteConfig está disponible, úsalo
    if (_remoteConfig != null) {
      if (kDebugMode) {
        // Para desarrollo, verificar si estamos en iOS
        if (TargetPlatform.iOS == defaultTargetPlatform) {
          return _remoteConfig!.getString('api_base_url_ios_dev');
        }
        // Para desarrollo en Android
        return _remoteConfig!.getString('api_base_url_dev');
      } else {
        // Para producción
        return _remoteConfig!.getString('api_base_url_prod');
      }
    }

    // Fallback a valores codificados si RemoteConfig no está disponible
    if (kDebugMode) {
      // Local development
      return 'http://********:3000/api/v1'; // Para emulador Android
      // Use 'http://localhost:3000/api/v1' para simulador iOS
    } else {
      // Entorno de producción
      return 'https://backend-production-ecfc.up.railway.app/api/v1';
    }
  }

  // Tiempo de espera para peticiones API
  static int get timeoutSeconds {
    if (_remoteConfig != null) {
      return _remoteConfig!.getInt('api_timeout_seconds');
    }
    return 10; // Valor predeterminado
  }

  // Número de reintentos para peticiones API fallidas
  static int get retryCount {
    if (_remoteConfig != null) {
      return _remoteConfig!.getInt('api_retry_count');
    }
    return 3; // Valor predeterminado
  }
}

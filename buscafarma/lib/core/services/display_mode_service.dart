import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'package:flutter_displaymode/flutter_displaymode.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:buscafarma/core/services/remote_config_service.dart';

/// Service for managing display modes and refresh rates
/// Provides high-level interface for display mode operations with error handling
class DisplayModeService {
  // Remote Config service instance
  static RemoteConfigService? _remoteConfig;

  // Singleton instance
  static DisplayModeService? _instance;

  // Current state
  List<DisplayMode> _supportedModes = [];
  DisplayMode? _currentMode;
  DisplayMode? _preferredMode;
  bool _isInitialized = false;

  // SharedPreferences keys
  static const String _preferenceKey = 'display_mode_preference';

  /// Private constructor
  DisplayModeService._();

  /// Factory to get or create instance
  static DisplayModeService getInstance() {
    _instance ??= DisplayModeService._();
    return _instance!;
  }

  /// Set the Remote Config service instance
  static void setRemoteConfig(RemoteConfigService? remoteConfig) {
    _remoteConfig = remoteConfig;
    if (remoteConfig != null) {
      debugPrint('DisplayModeService: Remote Config service set');
    } else {
      debugPrint('DisplayModeService: Remote Config service is null, using defaults');
    }
  }

  /// Initialize the display mode service
  Future<bool> initialize() async {
    try {
      debugPrint('DisplayModeService: Initializing...');

      // Check if platform is supported
      if (!Platform.isAndroid) {
        debugPrint('DisplayModeService: Platform not supported (${Platform.operatingSystem}). Display mode features disabled.');
        _isInitialized = true;
        return true; // Return true to not block app initialization
      }

      // Check if feature is enabled via Remote Config
      if (!_isFeatureEnabled()) {
        debugPrint('DisplayModeService: Feature disabled via Remote Config');
        _isInitialized = true;
        return true;
      }

      // Get supported display modes
      await _loadSupportedModes();

      // Get current active mode
      await _loadCurrentMode();

      // Apply preferred mode if available
      await _applyPreferredMode();

      _isInitialized = true;
      debugPrint('DisplayModeService: Initialization completed successfully');
      return true;
    } catch (e) {
      debugPrint('DisplayModeService: Initialization failed: $e');
      _isInitialized = true; // Mark as initialized to prevent blocking app
      return false;
    }
  }

  /// Load supported display modes
  Future<void> _loadSupportedModes() async {
    try {
      _supportedModes = await FlutterDisplayMode.supported;
      debugPrint('DisplayModeService: Found ${_supportedModes.length} supported modes:');
      for (final mode in _supportedModes) {
        debugPrint('  - ${mode.toString()}');
      }
    } on PlatformException catch (e) {
      _handlePlatformException(e, 'loading supported modes');
      _supportedModes = [];
    }
  }

  /// Load current active mode
  Future<void> _loadCurrentMode() async {
    try {
      _currentMode = await FlutterDisplayMode.active;
      debugPrint('DisplayModeService: Current active mode: ${_currentMode.toString()}');
    } on PlatformException catch (e) {
      _handlePlatformException(e, 'loading current mode');
    }
  }

  /// Apply preferred mode based on user preference and Remote Config
  Future<void> _applyPreferredMode() async {
    try {
      final preferenceMode = _getPreferredModeFromConfig();

      switch (preferenceMode) {
        case 'high':
          await setHighRefreshRate();
          break;
        case 'low':
          await setLowRefreshRate();
          break;
        case 'auto':
        default:
          await setHighRefreshRate(); // Default to high refresh rate
          break;
      }
    } catch (e) {
      debugPrint('DisplayModeService: Failed to apply preferred mode: $e');
    }
  }

  /// Set high refresh rate
  Future<bool> setHighRefreshRate() async {
    if (!_canPerformOperation()) return false;

    try {
      await FlutterDisplayMode.setHighRefreshRate();
      await _loadCurrentMode(); // Refresh current mode
      debugPrint('DisplayModeService: High refresh rate set successfully');
      return true;
    } on PlatformException catch (e) {
      _handlePlatformException(e, 'setting high refresh rate');
      return false;
    }
  }

  /// Set low refresh rate
  Future<bool> setLowRefreshRate() async {
    if (!_canPerformOperation()) return false;

    try {
      await FlutterDisplayMode.setLowRefreshRate();
      await _loadCurrentMode(); // Refresh current mode
      debugPrint('DisplayModeService: Low refresh rate set successfully');
      return true;
    } on PlatformException catch (e) {
      _handlePlatformException(e, 'setting low refresh rate');
      return false;
    }
  }

  /// Set specific display mode
  Future<bool> setDisplayMode(DisplayMode mode) async {
    if (!_canPerformOperation()) return false;

    try {
      await FlutterDisplayMode.setPreferredMode(mode);
      await _loadCurrentMode(); // Refresh current mode
      debugPrint('DisplayModeService: Display mode set to: ${mode.toString()}');
      return true;
    } on PlatformException catch (e) {
      _handlePlatformException(e, 'setting display mode');
      return false;
    }
  }

  /// Get current display mode
  DisplayMode? get currentMode => _currentMode;

  /// Get supported display modes
  List<DisplayMode> get supportedModes => List.unmodifiable(_supportedModes);

  /// Check if service is initialized
  bool get isInitialized => _isInitialized;

  /// Check if platform supports display mode changes
  bool get isPlatformSupported => Platform.isAndroid;

  /// Check if feature is enabled
  bool get isFeatureEnabled => _isFeatureEnabled();

  /// Save user preference for display mode
  Future<void> saveUserPreference(String preference) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_preferenceKey, preference);
      debugPrint('DisplayModeService: User preference saved: $preference');
    } catch (e) {
      debugPrint('DisplayModeService: Failed to save user preference: $e');
    }
  }

  /// Get user preference for display mode
  Future<String> getUserPreference() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.getString(_preferenceKey) ?? 'auto';
    } catch (e) {
      debugPrint('DisplayModeService: Failed to get user preference: $e');
      return 'auto';
    }
  }

  /// Check if feature is enabled via Remote Config
  bool _isFeatureEnabled() {
    if (_remoteConfig != null) {
      return _remoteConfig!.getBool('enable_high_refresh_rate');
    }
    return true; // Default to enabled
  }

  /// Get preferred mode from Remote Config
  String _getPreferredModeFromConfig() {
    if (_remoteConfig != null) {
      return _remoteConfig!.getString('preferred_refresh_rate_mode');
    }
    return 'auto'; // Default preference
  }

  /// Check if operations can be performed
  bool _canPerformOperation() {
    if (!Platform.isAndroid) {
      debugPrint('DisplayModeService: Operation not supported on ${Platform.operatingSystem}');
      return false;
    }

    if (!_isFeatureEnabled()) {
      debugPrint('DisplayModeService: Feature disabled via Remote Config');
      return false;
    }

    return true;
  }

  /// Handle platform exceptions with appropriate logging
  void _handlePlatformException(PlatformException e, String operation) {
    switch (e.code) {
      case 'noAPI':
        debugPrint('DisplayModeService: No API support for $operation (Android Marshmallow+ required)');
        break;
      case 'noActivity':
        debugPrint('DisplayModeService: Activity not available for $operation (app may be in background)');
        break;
      default:
        debugPrint('DisplayModeService: Platform exception during $operation: ${e.message}');
        break;
    }
  }

  /// Get debug information about display mode service state
  Map<String, dynamic> getDebugInfo() {
    return {
      'isInitialized': _isInitialized,
      'isPlatformSupported': isPlatformSupported,
      'isFeatureEnabled': isFeatureEnabled,
      'supportedModesCount': _supportedModes.length,
      'currentMode': _currentMode?.toString(),
      'preferredMode': _preferredMode?.toString(),
      'platform': Platform.operatingSystem,
    };
  }
}

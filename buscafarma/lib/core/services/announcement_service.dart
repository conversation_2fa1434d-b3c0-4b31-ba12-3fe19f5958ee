import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:buscafarma/core/widgets/announcement_dialog.dart';

/// Service for managing informational announcements
/// Handles first-launch detection and announcement display logic
class AnnouncementService {
  static const String _firstLaunchKey = 'announcement_first_launch_shown';

  /// Checks if this is the first app launch (announcement not yet shown)
  Future<bool> isFirstLaunch() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return !(prefs.getBool(_firstLaunchKey) ?? false);
    } catch (e) {
      // Graceful fallback - assume first launch if SharedPreferences fails
      debugPrint('AnnouncementService: Failed to check first launch status: $e');
      return true;
    }
  }

  /// Marks the announcement as shown to prevent future automatic displays
  Future<void> markAnnouncementAsShown() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_firstLaunchKey, true);
    } catch (e) {
      // Non-blocking error - log but don't throw
      debugPrint('AnnouncementService: Failed to mark announcement as shown: $e');
    }
  }

  /// Returns the announcement content in Spanish
  String getAnnouncementContent() {
    return '''Bienvenidos al módulo de consulta al ciudadano:

Debes tener en cuenta:

1. Los precios de venta al público podrían diferir del precio ofertado al momento de la compra.
2. Se recomienda comunicarse con el establecimiento previo a realizar la compra.
3. Los datos obtenidos en la consulta son en línea y reflejan la información en el momento de la consulta.''';
  }

  /// Shows the announcement dialog
  /// Returns true if user acknowledged, false if dismissed
  Future<bool> showAnnouncement(BuildContext context) async {
    try {
      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: false, // Prevent dismissal by tapping outside
        builder: (context) => AnnouncementDialog(content: getAnnouncementContent()),
      );

      return result ?? false;
    } catch (e) {
      // Graceful fallback - log error but don't break app flow
      debugPrint('AnnouncementService: Failed to show announcement: $e');
      return false;
    }
  }

  /// Shows announcement for first-time users and marks as shown
  /// Returns true if announcement was shown and acknowledged
  Future<bool> showFirstLaunchAnnouncement(BuildContext context) async {
    final isFirst = await isFirstLaunch();
    if (isFirst) {
      // Check if context is still valid before showing dialog
      if (!context.mounted) return false;

      final acknowledged = await showAnnouncement(context);
      if (acknowledged) {
        await markAnnouncementAsShown();
        return true;
      }
    }
    return false;
  }
}

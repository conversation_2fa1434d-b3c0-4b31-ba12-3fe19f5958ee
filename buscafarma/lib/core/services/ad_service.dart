import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:applovin_max/applovin_max.dart';
import 'package:buscafarma/core/config/ad_config.dart';
import 'package:buscafarma/core/providers/remote_config_provider.dart';
import 'package:buscafarma/core/providers/navigation_tracking_provider.dart';

/// Provider that watches RemoteConfig state and updates AdConfig when ready
final _adConfigUpdaterProvider = Provider<void>((ref) {
  final remoteConfigState = ref.watch(remoteConfigProvider);
  final remoteConfig = ref.watch(remoteConfigServiceProvider);

  // Update AdConfig when RemoteConfig becomes available
  remoteConfigState.whenData((_) {
    if (remoteConfig != null) {
      AdConfig.setRemoteConfig(remoteConfig);
      debugPrint('AdService: RemoteConfig updated in AdConfig');
    }
  });
});

/// Provider for the ad service
final adServiceProvider = Provider<AdService>((ref) {
  // Watch the updater to ensure AdConfig gets updated when RemoteConfig loads
  ref.watch(_adConfigUpdaterProvider);

  // Try to get RemoteConfig service if available, but don't block AdService initialization
  try {
    final remoteConfigState = ref.read(remoteConfigProvider);
    final remoteConfig = ref.read(remoteConfigServiceProvider);

    debugPrint('AdService: RemoteConfig state: $remoteConfigState');

    // Set RemoteConfig in AdConfig if it's available and loaded
    if (remoteConfig != null && remoteConfigState is AsyncData) {
      AdConfig.setRemoteConfig(remoteConfig);
      debugPrint('AdService: RemoteConfig set successfully');
    } else if (remoteConfigState is AsyncLoading) {
      debugPrint('AdService: RemoteConfig is loading, AdService will initialize with defaults and update later');
    } else if (remoteConfigState is AsyncError) {
      debugPrint('AdService: RemoteConfig failed to load: ${remoteConfigState.error}');
    } else {
      debugPrint('AdService: RemoteConfig not available, using default ad configuration');
    }
  } catch (e) {
    debugPrint('AdService: Failed to get RemoteConfig: $e');
  }

  return AdService();
});

/// High-level service for managing AppLovin MAX ads
/// Provides simplified interface for ad operations and frequency management
class AdService {
  // Frequency management
  DateTime? _lastInterstitialTime;
  DateTime? _lastAppOpenTime;
  int _interstitialCountThisSession = 0;

  AdService();

  /// Initialize AppLovin MAX SDK with 4.5.0+ API
  Future<bool> initializeSDK() async {
    try {
      // Show warning if using mock IDs
      final warning = AdConfig.mockIdWarning;
      if (warning != null) {
        debugPrint(warning);
      }

      // Validate configuration
      if (!AdConfig.isConfigurationValid) {
        debugPrint('AdService: Invalid ad configuration detected');
        return false;
      }

      // Initialize SDK - returns MaxConfiguration in 4.5.0+
      final maxConfiguration = await AppLovinMAX.initialize(AdConfig.sdkKey);

      if (maxConfiguration != null) {
        debugPrint('AdService: SDK initialized with configuration: ${maxConfiguration.toString()}');
      }

      // Set up global ad listeners
      _setupGlobalListeners();

      debugPrint('AdService: AppLovin MAX SDK 4.5.0+ initialized successfully');
      return true;
    } catch (e) {
      debugPrint('AdService: Failed to initialize SDK: $e');
      return false;
    }
  }

  /// Set up global ad event listeners for debugging and analytics
  void _setupGlobalListeners() {
    // Banner ad listeners - Updated for 4.5.0+ API
    AppLovinMAX.setBannerListener(
      AdViewAdListener(
        onAdLoadedCallback: (ad) => debugPrint('Banner ad loaded: ${ad.adUnitId}'),
        onAdLoadFailedCallback: (adUnitId, error) => debugPrint('Banner ad failed to load: $adUnitId, Error: ${error.message}'),
        onAdClickedCallback: (ad) => debugPrint('Banner ad clicked: ${ad.adUnitId}'),
        onAdExpandedCallback: (ad) => debugPrint('Banner ad expanded: ${ad.adUnitId}'),
        onAdCollapsedCallback: (ad) => debugPrint('Banner ad collapsed: ${ad.adUnitId}'),
      ),
    );

    // Interstitial ad listeners - Updated for 4.5.0+ API
    AppLovinMAX.setInterstitialListener(
      InterstitialListener(
        onAdLoadedCallback: (ad) {
          debugPrint('Interstitial ad loaded: ${ad.adUnitId}');
        },
        onAdLoadFailedCallback: (adUnitId, error) {
          debugPrint('Interstitial ad failed to load: $adUnitId, Error: ${error.message}');
          // Preload next ad
          _preloadInterstitial();
        },
        onAdDisplayedCallback: (ad) => debugPrint('Interstitial ad displayed: ${ad.adUnitId}'),
        onAdDisplayFailedCallback: (ad, error) {
          debugPrint('Interstitial ad failed to display: ${ad.adUnitId}, Error: ${error.message}');
          // Preload next ad
          _preloadInterstitial();
        },
        onAdClickedCallback: (ad) => debugPrint('Interstitial ad clicked: ${ad.adUnitId}'),
        onAdHiddenCallback: (ad) {
          debugPrint('Interstitial ad hidden: ${ad.adUnitId}');
          // Preload next ad
          _preloadInterstitial();
        },
      ),
    );

    // App open ad listeners
    AppLovinMAX.setAppOpenAdListener(
      AppOpenAdListener(
        onAdLoadedCallback: (ad) => debugPrint('App open ad loaded: ${ad.adUnitId}'),
        onAdLoadFailedCallback: (adUnitId, error) {
          debugPrint('App open ad failed to load: $adUnitId, Error: ${error.message}');
          // Preload next ad
          _preloadAppOpenAd();
        },
        onAdDisplayedCallback: (ad) => debugPrint('App open ad displayed: ${ad.adUnitId}'),
        onAdDisplayFailedCallback: (ad, error) {
          debugPrint('App open ad failed to display: ${ad.adUnitId}, Error: ${error.message}');
          // Preload next ad
          _preloadAppOpenAd();
        },
        onAdClickedCallback: (ad) => debugPrint('App open ad clicked: ${ad.adUnitId}'),
        onAdHiddenCallback: (ad) {
          debugPrint('App open ad hidden: ${ad.adUnitId}');
          // Preload next ad
          _preloadAppOpenAd();
        },
      ),
    );
  }

  /// Preload all ad formats
  Future<void> preloadAllAds() async {
    try {
      _preloadInterstitial();
      _preloadAppOpenAd();
      debugPrint('AdService: Preloading all ads');
    } catch (e) {
      debugPrint('AdService: Failed to preload ads: $e');
    }
  }

  /// Reset session counters
  void resetSessionCounters() {
    _interstitialCountThisSession = 0;
    debugPrint('AdService: Session counters reset');
  }

  /// Preload interstitial ad for better user experience
  void _preloadInterstitial() {
    try {
      AppLovinMAX.loadInterstitial(AdConfig.interstitialAdUnitId);
    } catch (e) {
      debugPrint('Failed to preload interstitial ad: $e');
    }
  }

  /// Preload app open ad for better user experience
  void _preloadAppOpenAd() {
    try {
      AppLovinMAX.loadAppOpenAd(AdConfig.appOpenAdUnitId);
    } catch (e) {
      debugPrint('Failed to preload app open ad: $e');
    }
  }

  /// Check if enough time has passed since last interstitial ad
  bool get canShowInterstitial {
    if (_lastInterstitialTime == null) return true;

    final timeSinceLastAd = DateTime.now().difference(_lastInterstitialTime!);
    final cooldownPassed = timeSinceLastAd.inSeconds >= AdConfig.interstitialCooldownSeconds;
    final underSessionLimit = _interstitialCountThisSession < AdConfig.maxInterstitialsPerSession;

    return cooldownPassed && underSessionLimit;
  }

  /// Check if enough time has passed since last app open ad
  bool get canShowAppOpenAd {
    if (_lastAppOpenTime == null) return true;

    final timeSinceLastAd = DateTime.now().difference(_lastAppOpenTime!);
    return timeSinceLastAd.inSeconds >= AdConfig.appOpenCooldownSeconds;
  }

  /// Show interstitial ad if conditions are met
  Future<bool> showInterstitialAd() async {
    try {
      if (!canShowInterstitial) {
        debugPrint('AdService: Interstitial ad blocked by frequency cap');
        return false;
      }

      final isReady = await AppLovinMAX.isInterstitialReady(AdConfig.interstitialAdUnitId);
      if (isReady != true) {
        debugPrint('AdService: Interstitial ad not ready');
        // Try to preload for next time
        _preloadInterstitial();
        return false;
      }

      AppLovinMAX.showInterstitial(AdConfig.interstitialAdUnitId);

      // Update frequency tracking
      _lastInterstitialTime = DateTime.now();
      _interstitialCountThisSession++;

      debugPrint('AdService: Interstitial ad shown successfully');
      return true;
    } catch (e) {
      debugPrint('AdService: Failed to show interstitial ad: $e');
      return false;
    }
  }

  /// Show app open ad if conditions are met
  Future<bool> showAppOpenAd() async {
    try {
      if (!canShowAppOpenAd) {
        debugPrint('AdService: App open ad blocked by frequency cap');
        return false;
      }

      final isReady = await AppLovinMAX.isAppOpenAdReady(AdConfig.appOpenAdUnitId);
      if (isReady != true) {
        debugPrint('AdService: App open ad not ready');
        // Try to preload for next time
        _preloadAppOpenAd();
        return false;
      }

      AppLovinMAX.showAppOpenAd(AdConfig.appOpenAdUnitId);

      // Update frequency tracking
      _lastAppOpenTime = DateTime.now();

      debugPrint('AdService: App open ad shown successfully');
      return true;
    } catch (e) {
      debugPrint('AdService: Failed to show app open ad: $e');
      return false;
    }
  }

  /// Show interstitial ad with remote config and navigation frequency checking
  Future<bool> showInterstitialAdWithRemoteConfig(WidgetRef ref) async {
    try {
      // Check if ads are enabled via remote config
      final navigationTracking = ref.read(navigationTrackingProvider.notifier);
      final trackingState = ref.read(navigationTrackingProvider);

      if (!trackingState.adsEnabled) {
        debugPrint('AdService: Interstitial ad blocked by remote config (ads disabled)');
        return false;
      }

      // Check navigation frequency
      if (!navigationTracking.shouldShowAd()) {
        debugPrint('AdService: Interstitial ad blocked by navigation frequency');
        return false;
      }

      // Use existing frequency checks
      if (!canShowInterstitial) {
        debugPrint('AdService: Interstitial ad blocked by existing frequency cap');
        return false;
      }

      final isReady = await AppLovinMAX.isInterstitialReady(AdConfig.interstitialAdUnitId);
      if (isReady != true) {
        debugPrint('AdService: Interstitial ad not ready');
        _preloadInterstitial();
        return false;
      }

      AppLovinMAX.showInterstitial(AdConfig.interstitialAdUnitId);

      // Update frequency tracking
      _lastInterstitialTime = DateTime.now();
      _interstitialCountThisSession++;

      // Reset navigation counter since ad was shown
      navigationTracking.recordAdShown();

      debugPrint('AdService: Interstitial ad shown successfully with remote config');
      return true;
    } catch (e) {
      debugPrint('AdService: Failed to show interstitial ad with remote config: $e');
      return false;
    }
  }

  /// Show app open ad with remote config checking
  Future<bool> showAppOpenAdWithRemoteConfig(WidgetRef ref) async {
    try {
      // Check if ads are enabled via remote config
      final trackingState = ref.read(navigationTrackingProvider);

      if (!trackingState.adsEnabled) {
        debugPrint('AdService: App open ad blocked by remote config (ads disabled)');
        return false;
      }

      // Use existing frequency checks
      if (!canShowAppOpenAd) {
        debugPrint('AdService: App open ad blocked by existing frequency cap');
        return false;
      }

      final isReady = await AppLovinMAX.isAppOpenAdReady(AdConfig.appOpenAdUnitId);
      if (isReady != true) {
        debugPrint('AdService: App open ad not ready');
        _preloadAppOpenAd();
        return false;
      }

      AppLovinMAX.showAppOpenAd(AdConfig.appOpenAdUnitId);

      // Update frequency tracking
      _lastAppOpenTime = DateTime.now();

      debugPrint('AdService: App open ad shown successfully with remote config');
      return true;
    } catch (e) {
      debugPrint('AdService: Failed to show app open ad with remote config: $e');
      return false;
    }
  }

  /// Show app open ad with remote config checking (Ref version for StateNotifiers)
  Future<bool> showAppOpenAdWithRemoteConfigRef(Ref ref) async {
    try {
      // Check if ads are enabled via remote config
      final trackingState = ref.read(navigationTrackingProvider);

      if (!trackingState.adsEnabled) {
        debugPrint('AdService: App open ad blocked by remote config (ads disabled)');
        return false;
      }

      // Use existing frequency checks
      if (!canShowAppOpenAd) {
        debugPrint('AdService: App open ad blocked by existing frequency cap');
        return false;
      }

      final isReady = await AppLovinMAX.isAppOpenAdReady(AdConfig.appOpenAdUnitId);
      if (isReady != true) {
        debugPrint('AdService: App open ad not ready');
        _preloadAppOpenAd();
        return false;
      }

      AppLovinMAX.showAppOpenAd(AdConfig.appOpenAdUnitId);

      // Update frequency tracking
      _lastAppOpenTime = DateTime.now();

      debugPrint('AdService: App open ad shown successfully with remote config');
      return true;
    } catch (e) {
      debugPrint('AdService: Failed to show app open ad with remote config: $e');
      return false;
    }
  }

  /// Check if banner ads should be shown based on remote config
  bool shouldShowBannerAd(WidgetRef ref) {
    try {
      final trackingState = ref.read(navigationTrackingProvider);
      return trackingState.adsEnabled;
    } catch (e) {
      debugPrint('AdService: Failed to check banner ad eligibility: $e');
      return true; // Default to showing ads if check fails
    }
  }

  /// Get debug information about ad service state
  Map<String, dynamic> getDebugInfo() {
    return {
      'canShowInterstitial': canShowInterstitial,
      'canShowAppOpenAd': canShowAppOpenAd,
      'interstitialCountThisSession': _interstitialCountThisSession,
      'lastInterstitialTime': _lastInterstitialTime?.toIso8601String(),
      'lastAppOpenTime': _lastAppOpenTime?.toIso8601String(),
      'isUsingMockIds': AdConfig.isUsingMockIds,
    };
  }
}

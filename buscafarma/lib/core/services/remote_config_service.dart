import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:flutter/material.dart';

/// Service to handle Firebase Remote Config
class RemoteConfigService {
  final FirebaseRemoteConfig _remoteConfig;

  /// Default configs that will be used if remote configs aren't available
  static final Map<String, dynamic> _defaults = {
    // App behavior
    'enable_ads': true,
    'ads_frequency': 3, // Number of navigation actions before showing an ad
    // Review prompt config
    'review_minimum_session_count': 3,
    'review_minimum_positive_interactions': 2,
    'review_minimum_days_since_install': 2,
    'review_days_between_prompts': 30,
    'review_days_after_dismissal': 90,
    'review_days_after_remind_later': 7,

    // Feature flags
    'enable_in_app_update': true,
    'enable_health_data_feature': true,

    // API settings
    'api_timeout_seconds': 10,
    'api_retry_count': 3,

    // App appearance
    'primary_color': '#2196F3', // Material Blue
    'background_color': '#FFFFFF', // White
    'text_color': '#000000', // Black
    // Display mode settings
    'enable_high_refresh_rate': true,
    'preferred_refresh_rate_mode': 'auto', // auto, high, low
    'battery_aware_switching': false,
  };

  /// Private constructor
  RemoteConfigService._({required FirebaseRemoteConfig remoteConfig}) : _remoteConfig = remoteConfig;

  /// Singleton instance
  static RemoteConfigService? _instance;

  /// Factory to get or create instance
  static Future<RemoteConfigService> getInstance() async {
    if (_instance == null) {
      final remoteConfig = FirebaseRemoteConfig.instance;

      // Set default values
      await remoteConfig.setDefaults(_defaults);

      // Set fetch settings
      await remoteConfig.setConfigSettings(RemoteConfigSettings(fetchTimeout: const Duration(minutes: 1), minimumFetchInterval: const Duration(hours: 1)));

      // Create instance
      _instance = RemoteConfigService._(remoteConfig: remoteConfig);

      // Fetch values
      await _instance!._fetchAndActivate();
    }

    return _instance!;
  }

  /// Fetch and activate remote configs
  Future<bool> _fetchAndActivate() async {
    try {
      final result = await _remoteConfig.fetchAndActivate();
      debugPrint('RemoteConfigService: Fetch and activate completed with result: $result');
      return result;
    } catch (e) {
      debugPrint('RemoteConfigService: Error fetching remote config: $e');
      return false;
    }
  }

  /// Fetch and activate remote configs manually (when needed)
  Future<bool> refreshConfig() async {
    return _fetchAndActivate();
  }

  /// Get bool value
  bool getBool(String key) {
    try {
      return _remoteConfig.getBool(key);
    } catch (e) {
      debugPrint('RemoteConfigService: Error getting bool value for $key: $e');
      return _defaults[key] as bool? ?? false;
    }
  }

  /// Get int value
  int getInt(String key) {
    try {
      return _remoteConfig.getInt(key);
    } catch (e) {
      debugPrint('RemoteConfigService: Error getting int value for $key: $e');
      return _defaults[key] as int? ?? 0;
    }
  }

  /// Get double value
  double getDouble(String key) {
    try {
      return _remoteConfig.getDouble(key);
    } catch (e) {
      debugPrint('RemoteConfigService: Error getting double value for $key: $e');
      return _defaults[key] as double? ?? 0.0;
    }
  }

  /// Get String value
  String getString(String key) {
    try {
      return _remoteConfig.getString(key);
    } catch (e) {
      debugPrint('RemoteConfigService: Error getting string value for $key: $e');
      return _defaults[key] as String? ?? '';
    }
  }

  /// Get Color from string hex value
  Color getColor(String key) {
    try {
      final hexColor = _remoteConfig.getString(key);
      if (hexColor.isEmpty) {
        return _hexToColor(_defaults[key] as String? ?? '#2196F3');
      }
      return _hexToColor(hexColor);
    } catch (e) {
      debugPrint('RemoteConfigService: Error getting color value for $key: $e');
      return _hexToColor(_defaults[key] as String? ?? '#2196F3');
    }
  }

  // Helper to convert hex string to color
  Color _hexToColor(String hexString) {
    final buffer = StringBuffer();
    if (hexString.length <= 7) {
      buffer.write('ff');
    }
    buffer.write(hexString.replaceFirst('#', ''));

    try {
      return Color(int.parse(buffer.toString(), radix: 16));
    } catch (e) {
      debugPrint('RemoteConfigService: Error parsing color: $e');
      return Colors.blue; // Fallback color
    }
  }

  /// Get review configurations
  Map<String, dynamic> getReviewConfig() {
    return {
      'minimumSessionCount': getInt('review_minimum_session_count'),
      'minimumPositiveInteractions': getInt('review_minimum_positive_interactions'),
      'minimumDaysSinceInstall': getInt('review_minimum_days_since_install'),
      'daysBetweenPrompts': getInt('review_days_between_prompts'),
      'daysAfterDismissal': getInt('review_days_after_dismissal'),
      'daysAfterRemindLater': getInt('review_days_after_remind_later'),
    };
  }

  /// Get raw underlying instance (for advanced use cases)
  FirebaseRemoteConfig get instance => _remoteConfig;
}

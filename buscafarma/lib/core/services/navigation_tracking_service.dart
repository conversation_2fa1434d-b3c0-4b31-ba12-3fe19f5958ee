import 'package:flutter/foundation.dart';
import 'package:buscafarma/core/services/remote_config_service.dart';

/// Service for tracking user navigation actions and determining when ads should be shown
/// based on the ads_frequency parameter from remote config
class NavigationTrackingService {
  final RemoteConfigService? _remoteConfig;

  // Counter for navigation actions in current session
  int _navigationActionCount = 0;

  // Last time an ad was shown due to navigation frequency
  DateTime? _lastNavigationAdTime;

  NavigationTrackingService({RemoteConfigService? remoteConfig}) : _remoteConfig = remoteConfig;

  /// Get the ads frequency from remote config (number of navigation actions before showing an ad)
  int get adsFrequency {
    try {
      if (_remoteConfig != null) {
        return _remoteConfig.getInt('ads_frequency');
      }
    } catch (e) {
      debugPrint('NavigationTrackingService: Error getting ads_frequency: $e');
    }
    return 3; // Default fallback value
  }

  /// Check if ads are enabled from remote config
  bool get adsEnabled {
    try {
      if (_remoteConfig != null) {
        return _remoteConfig.getBool('enable_ads');
      }
    } catch (e) {
      debugPrint('NavigationTrackingService: Error getting enable_ads: $e');
    }
    return true; // Default fallback value (allow ads while loading)
  }

  /// Get current navigation action count
  int get navigationActionCount => _navigationActionCount;

  /// Get last navigation ad time
  DateTime? get lastNavigationAdTime => _lastNavigationAdTime;

  /// Increment navigation action counter
  void incrementNavigationAction() {
    _navigationActionCount++;
    debugPrint('NavigationTrackingService: Navigation action count incremented to $_navigationActionCount');
  }

  /// Check if an ad should be shown based on navigation frequency
  bool shouldShowAdBasedOnFrequency() {
    // First check if ads are enabled
    if (!adsEnabled) {
      debugPrint('NavigationTrackingService: Ads disabled by remote config');
      return false;
    }

    // Check if we've reached the frequency threshold
    final frequency = adsFrequency;
    final shouldShow = _navigationActionCount >= frequency;

    debugPrint('NavigationTrackingService: Navigation count: $_navigationActionCount, Frequency: $frequency, Should show: $shouldShow');

    return shouldShow;
  }

  /// Reset navigation counter (typically called after showing an ad)
  void resetNavigationCounter() {
    _navigationActionCount = 0;
    _lastNavigationAdTime = DateTime.now();
    debugPrint('NavigationTrackingService: Navigation counter reset, last ad time updated');
  }

  /// Reset session counters (called on app start or session reset)
  void resetSessionCounters() {
    _navigationActionCount = 0;
    _lastNavigationAdTime = null;
    debugPrint('NavigationTrackingService: Session counters reset');
  }

  /// Get debug information about the current state
  Map<String, dynamic> getDebugInfo() {
    return {
      'navigationActionCount': _navigationActionCount,
      'adsFrequency': adsFrequency,
      'adsEnabled': adsEnabled,
      'shouldShowAd': shouldShowAdBasedOnFrequency(),
      'lastNavigationAdTime': _lastNavigationAdTime?.toIso8601String(),
      'remoteConfigAvailable': _remoteConfig != null,
    };
  }

  /// Check if enough time has passed since last navigation-triggered ad
  /// This can be used in combination with frequency checking for additional control
  bool hasEnoughTimePassed({Duration minimumInterval = const Duration(minutes: 1)}) {
    if (_lastNavigationAdTime == null) return true;

    final timeSinceLastAd = DateTime.now().difference(_lastNavigationAdTime!);
    final hasPassedTime = timeSinceLastAd >= minimumInterval;

    debugPrint('NavigationTrackingService: Time since last ad: ${timeSinceLastAd.inSeconds}s, Required: ${minimumInterval.inSeconds}s, Has passed: $hasPassedTime');

    return hasPassedTime;
  }

  /// Comprehensive check for whether an ad should be shown
  /// Combines frequency checking with time-based controls
  bool shouldShowAd({Duration minimumInterval = const Duration(minutes: 1)}) {
    return shouldShowAdBasedOnFrequency() && hasEnoughTimePassed(minimumInterval: minimumInterval);
  }

  /// Record that an ad was shown due to navigation frequency
  /// This resets the counter and updates the last ad time
  void recordAdShown() {
    resetNavigationCounter();
    debugPrint('NavigationTrackingService: Ad shown recorded, counters reset');
  }

  /// Update remote config service (useful when remote config is initialized later)
  void updateRemoteConfig(RemoteConfigService remoteConfig) {
    // Note: We don't store the new remote config to avoid issues with the final field
    // Instead, this method can be used to trigger a refresh of cached values if needed
    debugPrint('NavigationTrackingService: Remote config update requested');
  }
}

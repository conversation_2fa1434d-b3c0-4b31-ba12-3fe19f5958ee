import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:buscafarma/core/widgets/disclaimer_dialog.dart';

/// Service for managing disclaimers about government data sources and app nature
/// Handles first-time display detection and disclaimer acknowledgment tracking
class DisclaimerService {
  static const String _dataSourceDisclaimerKey = 'data_source_disclaimer_shown';
  static const String _disclaimerVersionKey = 'disclaimer_version_acknowledged';
  static const String _currentDisclaimerVersion = '2.0';

  /// Checks if the user has seen the data source disclaimer
  Future<bool> hasSeenDataSourceDisclaimer() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final hasSeenDisclaimer = prefs.getBool(_dataSourceDisclaimerKey) ?? false;
      final acknowledgedVersion = prefs.getString(_disclaimerVersionKey) ?? '';

      // User must have seen disclaimer AND the version must match current version
      final isCurrentVersion = acknowledgedVersion == _currentDisclaimerVersion;

      debugPrint('DisclaimerService: hasSeenDisclaimer=$hasSeenDisclaimer, acknowledgedVersion=$acknowledgedVersion, currentVersion=$_currentDisclaimerVersion');
      return hasSeenDisclaimer && isCurrentVersion;
    } catch (e) {
      // Graceful fallback - assume disclaimer not seen if SharedPreferences fails
      debugPrint('DisclaimerService: Failed to check disclaimer status: $e');
      return false;
    }
  }

  /// Marks the data source disclaimer as acknowledged for the current version
  Future<void> markDataSourceDisclaimerAsAcknowledged() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool(_dataSourceDisclaimerKey, true);
      await prefs.setString(_disclaimerVersionKey, _currentDisclaimerVersion);
      debugPrint('DisclaimerService: Data source disclaimer marked as acknowledged for version $_currentDisclaimerVersion');
    } catch (e) {
      // Non-blocking error - log but don't throw
      debugPrint('DisclaimerService: Failed to mark disclaimer as acknowledged: $e');
    }
  }

  /// Returns the data source disclaimer content in Spanish
  String getDataSourceDisclaimerContent() {
    return '''⚠️ AVISO IMPORTANTE

BuscaFarma NO es una aplicación gubernamental.

Somos una aplicación privada independiente que utiliza datos públicos del Ministerio de Salud del Perú (MINSA) para mostrar información sobre medicamentos y farmacias.

IMPORTANTE:
• No representamos a ninguna entidad pública
• No tenemos autorización para facilitar servicios públicos oficiales
• Los datos mostrados provienen de fuentes públicas del MINSA
• Esta aplicación NO proporciona consejos médicos

Para información oficial, consulte directamente las fuentes oficiales del MINSA.

Siempre consulte con un profesional de la salud antes de tomar decisiones médicas.''';
  }

  /// Shows the data source disclaimer dialog
  /// Returns true if user acknowledged, false if dismissed
  Future<bool> showDataSourceDisclaimer(BuildContext context) async {
    try {
      // Check if context is still valid before showing dialog
      if (!context.mounted) return false;

      final result = await showDialog<bool>(
        context: context,
        barrierDismissible: true, // Allow dismissal by tapping outside
        builder: (context) => DisclaimerDialog(content: getDataSourceDisclaimerContent()),
      );

      return result ?? false;
    } catch (e) {
      // Graceful fallback - log error but don't break app flow
      debugPrint('DisclaimerService: Failed to show disclaimer: $e');
      return false;
    }
  }

  /// Shows disclaimer for first-time users and marks as acknowledged
  /// Returns true if disclaimer was shown and acknowledged
  Future<bool> showFirstTimeDataSourceDisclaimer(BuildContext context) async {
    final hasSeen = await hasSeenDataSourceDisclaimer();
    if (hasSeen) {
      debugPrint('DisclaimerService: Disclaimer already acknowledged, skipping display');
      return true; // Already acknowledged
    }

    // Check if context is still valid before showing dialog
    if (!context.mounted) return false;

    final result = await showDataSourceDisclaimer(context);

    if (result) {
      await markDataSourceDisclaimerAsAcknowledged();
      return true;
    }

    // User dismissed without acknowledging
    return false;
  }

  /// Shows disclaimer from profile section (always shows, regardless of acknowledgment status)
  /// Returns true if user acknowledged, false if dismissed
  Future<bool?> showDataSourceDisclaimerFromProfile(BuildContext context) async {
    return await showDataSourceDisclaimer(context);
  }

  /// Resets disclaimer acknowledgment status (useful for testing or user logout)
  Future<void> resetDisclaimerAcknowledgment() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_dataSourceDisclaimerKey);
      await prefs.remove(_disclaimerVersionKey);
      debugPrint('DisclaimerService: Disclaimer acknowledgment status reset');
    } catch (e) {
      debugPrint('DisclaimerService: Failed to reset disclaimer acknowledgment: $e');
    }
  }

  /// Gets the current disclaimer version
  String get currentDisclaimerVersion => _currentDisclaimerVersion;

  /// Checks if disclaimer needs to be shown (user hasn't acknowledged current version)
  Future<bool> shouldShowDisclaimer() async {
    return !(await hasSeenDataSourceDisclaimer());
  }

  /// Returns about app content with disclaimers
  String getAboutAppContent() {
    return '''BuscaFarma - Encuentra Farmacias Cercanas

NATURALEZA DE LA APLICACIÓN:
BuscaFarma es una aplicación móvil privada e independiente desarrollada para ayudar a los usuarios a encontrar farmacias cercanas y consultar información sobre medicamentos.

⚠️ IMPORTANTE - NO SOMOS GUBERNAMENTALES:
• BuscaFarma NO es una aplicación oficial del gobierno peruano
• NO representamos al Ministerio de Salud (MINSA) ni a ninguna entidad pública
• NO tenemos autorización para facilitar servicios públicos oficiales
• Somos completamente independientes de cualquier institución gubernamental

FUENTES DE DATOS:
Utilizamos datos públicos del Ministerio de Salud del Perú (MINSA) disponibles a través de sus APIs públicas para mostrar:
• Información sobre medicamentos
• Precios en establecimientos farmacéuticos
• Ubicaciones de farmacias y boticas

LIMITACIONES Y RESPONSABILIDADES:
• No garantizamos la exactitud de los datos gubernamentales
• Los precios pueden cambiar sin previo aviso
• Para información oficial, consulte directamente al MINSA
• Esta aplicación NO proporciona consejos médicos

DISCLAIMER MÉDICO:
La información mostrada es únicamente para fines informativos. Siempre consulte con un profesional de la salud calificado antes de tomar decisiones médicas.

Versión de la aplicación: 1.0
Última actualización: Diciembre 2024''';
  }
}

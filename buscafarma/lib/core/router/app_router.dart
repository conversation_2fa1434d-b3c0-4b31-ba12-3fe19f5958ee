import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:buscafarma/core/router/route_handlers.dart';
import 'package:buscafarma/core/router/app_routes.dart';

/// Main router configuration class for the application
/// Uses Fluro for routing with organized route definitions
class AppRouter {
  // App router instance
  static final FluroRouter router = FluroRouter();

  /// Setup all routes and configure the router
  static void setupRouter() {
    _defineRoutes();

    // Configure not found handler
    router.notFoundHandler = RouteHandlers.notFoundHandler;
  }

  /// Define all application routes
  static void _defineRoutes() {
    // Splash route
    router.define(AppRoutes.splash, handler: RouteHandlers.splashHandler, transitionType: TransitionType.fadeIn);

    // Root route
    router.define(AppRoutes.root, handler: RouteHandlers.mapHandler, transitionType: TransitionType.fadeIn);

    // Map route
    router.define(AppRoutes.map, handler: RouteHandlers.mapHandler, transitionType: TransitionType.fadeIn);

    // Login route
    router.define(AppRoutes.login, handler: RouteHandlers.loginHandler, transitionType: TransitionType.fadeIn);

    // Profile route
    router.define(AppRoutes.profile, handler: RouteHandlers.profileHandler, transitionType: TransitionType.fadeIn);

    // Edit health data route
    router.define(AppRoutes.editHealthData, handler: RouteHandlers.healthDataEditHandler, transitionType: TransitionType.cupertino);

    // Additional routes can be defined here following the same pattern
    // Example:
    // router.define(
    //   AppRoutes.profile,
    //   handler: RouteHandlers.profileHandler,
    //   transitionType: TransitionType.fadeIn,
    // );
  }

  /// Helper method for navigation with query parameters
  static void navigateTo(BuildContext context, String route, {Map<String, dynamic>? queryParams, bool replace = false, bool clearStack = false, TransitionType transition = TransitionType.fadeIn}) {
    String queryString = '';
    if (queryParams != null) {
      queryString = '?';
      queryParams.forEach((key, value) {
        queryString += '$key=$value&';
      });

      // Remove trailing "&" if present
      if (queryString.endsWith('&')) {
        queryString = queryString.substring(0, queryString.length - 1);
      }
    }

    router.navigateTo(context, '$route$queryString', transition: transition, replace: replace, clearStack: clearStack);
  }
}

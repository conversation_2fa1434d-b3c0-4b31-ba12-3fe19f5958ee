import 'package:flutter/material.dart';
import 'package:fluro/fluro.dart';
import 'package:buscafarma/core/router/app_router.dart';
import 'package:buscafarma/core/router/app_routes.dart';

/// Extensions on BuildContext for easier navigation
extension RouterExtensions on BuildContext {
  /// Navigate to a route using the app router
  void navigateTo(String route, {Map<String, dynamic>? queryParams, bool replace = false, bool clearStack = false, TransitionType transition = TransitionType.fadeIn}) {
    AppRouter.navigateTo(this, route, queryParams: queryParams, replace: replace, clearStack: clearStack, transition: transition);
  }

  /// Navigate to the splash page
  void goToSplash({bool replace = false, bool clearStack = false}) {
    navigateTo(AppRoutes.splash, replace: replace, clearStack: clearStack);
  }

  /// Navigate to the map page
  void goToMap({bool replace = false, bool clearStack = false}) {
    navigateTo(AppRoutes.map, replace: replace, clearStack: clearStack);
  }

  /// Navigate to the profile page
  void goToProfile({bool replace = false, bool clearStack = false}) {
    navigateTo(AppRoutes.profile, replace: replace, clearStack: clearStack);
  }

  /// Navigate to the search page with an optional query
  void goToSearch({String? query, bool replace = false, bool clearStack = false}) {
    Map<String, dynamic>? queryParams;

    if (query != null && query.isNotEmpty) {
      queryParams = {'query': query};
    }

    navigateTo(AppRoutes.search, queryParams: queryParams, replace: replace, clearStack: clearStack);
  }

  /// Go back to the previous route if possible
  void goBack() {
    Navigator.of(this).pop();
  }
}

import 'package:fluro/fluro.dart';
import 'package:flutter/material.dart';
import 'package:buscafarma/features/auth/presentation/pages/auth_pages.dart';
import 'package:buscafarma/features/navigation/presentation/pages/main_navigation_page.dart';
import 'package:buscafarma/features/splash/presentation/pages/splash_page.dart';

/// This class contains all route handlers for the application.
/// Each handler is responsible for creating the appropriate widget
/// based on the route parameters.
class RouteHandlers {
  /// Handler for splash route
  static final Handler splashHandler = Handler(
    handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
      return const SplashPage();
    },
  );

  /// Handler for the root route and map route
  static final Handler mapHandler = Handler(
    handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
      return const MainNavigationPage();
    },
  );

  /// Handler for login route
  static final Handler loginHandler = Handler(
    handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
      return const LoginPage();
    },
  );

  /// Handler for user profile route
  static final Handler profileHandler = Handler(
    handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
      return const ProfilePage();
    },
  );

  /// Handler for health data edit route
  static final Handler healthDataEditHandler = Handler(
    handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
      return const HealthDataEditPage();
    },
  );

  /// Handler for not found routes
  static final Handler notFoundHandler = Handler(
    handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
      return Scaffold(appBar: AppBar(title: const Text('Ruta no encontrada')), body: const Center(child: Text('La ruta solicitada no existe')));
    },
  );

  // Additional handlers can be added here for other routes
  // Example:
  // static final Handler profileHandler = Handler(
  //   handlerFunc: (BuildContext? context, Map<String, List<String>> params) {
  //     return const ProfilePage();
  //   },
  // );
}

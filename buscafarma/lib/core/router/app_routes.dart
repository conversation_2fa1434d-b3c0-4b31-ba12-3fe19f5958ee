/// Class that defines all route paths and names in the application.
/// This centralizes route constants to avoid string duplication and typos.
class AppRoutes {
  // Private constructor to prevent instantiation
  AppRoutes._();

  // Route paths
  static const String root = '/';
  static const String splash = '/splash';
  static const String map = '/map';
  static const String profile = '/profile';
  static const String search = '/search';
  static const String favorites = '/favorites';
  static const String settings = '/settings';
  static const String login = '/login';
  static const String editHealthData = '/profile/edit-health-data';

  // Route names - useful for named navigation
  static const String rootName = 'root';
  static const String splashName = 'splash';
  static const String mapName = 'map';
  static const String profileName = 'profile';
  static const String searchName = 'search';
  static const String favoritesName = 'favorites';
  static const String settingsName = 'settings';
  static const String loginName = 'login';

  // Route with parameters examples
  static String profileWithId(String id) => '$profile/$id';
  static String searchWithQuery(String query) => '$search?query=$query';
}

import 'package:flutter/material.dart';
import 'package:buscafarma/core/animations/animation_constants.dart';

/// A utility class for managing staggered animations with coordinated timing
class StaggeredAnimationController {
  final AnimationController _controller;
  final Map<String, Animation<double>> _animations = {};
  final Map<String, Animation<Offset>> _slideAnimations = {};
  final Map<String, Animation<double>> _scaleAnimations = {};
  final Map<String, Animation<double>> _rotationAnimations = {};

  StaggeredAnimationController({
    required AnimationController controller,
  }) : _controller = controller;

  /// Create a fade animation with specified timing
  Animation<double> createFadeAnimation({
    required String key,
    required Duration delay,
    Duration? duration,
    Curve curve = AnimationConstants.entranceCurve,
  }) {
    final animationDuration = duration ?? AnimationConstants.stateTransitionDuration;
    final totalDuration = _controller.duration!;
    
    final startTime = delay.inMilliseconds / totalDuration.inMilliseconds;
    final endTime = (delay.inMilliseconds + animationDuration.inMilliseconds) / totalDuration.inMilliseconds;
    
    final animation = Tween<double>(
      begin: AnimationConstants.hiddenOpacity,
      end: AnimationConstants.visibleOpacity,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(startTime, endTime.clamp(0.0, 1.0), curve: curve),
      ),
    );
    
    _animations[key] = animation;
    return animation;
  }

  /// Create a slide animation with specified timing and direction
  Animation<Offset> createSlideAnimation({
    required String key,
    required Duration delay,
    required Offset fromOffset,
    Duration? duration,
    Curve curve = AnimationConstants.entranceCurve,
  }) {
    final animationDuration = duration ?? AnimationConstants.stateTransitionDuration;
    final totalDuration = _controller.duration!;
    
    final startTime = delay.inMilliseconds / totalDuration.inMilliseconds;
    final endTime = (delay.inMilliseconds + animationDuration.inMilliseconds) / totalDuration.inMilliseconds;
    
    final animation = Tween<Offset>(
      begin: fromOffset,
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(startTime, endTime.clamp(0.0, 1.0), curve: curve),
      ),
    );
    
    _slideAnimations[key] = animation;
    return animation;
  }

  /// Create a scale animation with specified timing
  Animation<double> createScaleAnimation({
    required String key,
    required Duration delay,
    double fromScale = AnimationConstants.logoEntranceScale,
    double toScale = 1.0,
    Duration? duration,
    Curve curve = AnimationConstants.bounceCurve,
  }) {
    final animationDuration = duration ?? AnimationConstants.stateTransitionDuration;
    final totalDuration = _controller.duration!;
    
    final startTime = delay.inMilliseconds / totalDuration.inMilliseconds;
    final endTime = (delay.inMilliseconds + animationDuration.inMilliseconds) / totalDuration.inMilliseconds;
    
    final animation = Tween<double>(
      begin: fromScale,
      end: toScale,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(startTime, endTime.clamp(0.0, 1.0), curve: curve),
      ),
    );
    
    _scaleAnimations[key] = animation;
    return animation;
  }

  /// Create a rotation animation with specified timing
  Animation<double> createRotationAnimation({
    required String key,
    required Duration delay,
    double fromRotation = AnimationConstants.logoEntranceRotation,
    double toRotation = 0.0,
    Duration? duration,
    Curve curve = AnimationConstants.entranceCurve,
  }) {
    final animationDuration = duration ?? AnimationConstants.stateTransitionDuration;
    final totalDuration = _controller.duration!;
    
    final startTime = delay.inMilliseconds / totalDuration.inMilliseconds;
    final endTime = (delay.inMilliseconds + animationDuration.inMilliseconds) / totalDuration.inMilliseconds;
    
    final animation = Tween<double>(
      begin: fromRotation,
      end: toRotation,
    ).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Interval(startTime, endTime.clamp(0.0, 1.0), curve: curve),
      ),
    );
    
    _rotationAnimations[key] = animation;
    return animation;
  }

  /// Get a fade animation by key
  Animation<double>? getFadeAnimation(String key) => _animations[key];

  /// Get a slide animation by key
  Animation<Offset>? getSlideAnimation(String key) => _slideAnimations[key];

  /// Get a scale animation by key
  Animation<double>? getScaleAnimation(String key) => _scaleAnimations[key];

  /// Get a rotation animation by key
  Animation<double>? getRotationAnimation(String key) => _rotationAnimations[key];

  /// Start all animations
  void forward() => _controller.forward();

  /// Reverse all animations
  void reverse() => _controller.reverse();

  /// Reset all animations
  void reset() => _controller.reset();

  /// Dispose of the controller
  void dispose() => _controller.dispose();
}

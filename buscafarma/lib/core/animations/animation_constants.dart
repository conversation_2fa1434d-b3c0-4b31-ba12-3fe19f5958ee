import 'package:flutter/material.dart';

/// Animation constants following Material Design 3 motion principles
/// and the app's animation standards defined in buscafarma.mdc
class AnimationConstants {
  // Duration constants
  static const Duration splashEntranceDuration = Duration(milliseconds: 1200);
  static const Duration loginEntranceDuration = Duration(milliseconds: 1000);
  static const Duration stateTransitionDuration = Duration(milliseconds: 300);
  static const Duration microInteractionDuration = Duration(milliseconds: 150);
  static const Duration hoverDuration = Duration(milliseconds: 200);
  static const Duration errorAnimationDuration = Duration(milliseconds: 400);

  // Input field specific durations
  static const Duration inputFocusDuration = Duration(milliseconds: 150);
  static const Duration inputErrorDuration = Duration(milliseconds: 300);
  static const Duration inputClearButtonDuration = Duration(milliseconds: 120);
  static const Duration inputValidationDuration = Duration(milliseconds: 200);

  // Stagger intervals for coordinated animations
  static const Duration splashStaggerInterval = Duration(milliseconds: 150);
  static const Duration loginStaggerInterval = Duration(milliseconds: 120);

  // Animation curves following Material Design 3 motion
  static const Curve entranceCurve = Curves.easeOutCubic;
  static const Curve exitCurve = Curves.easeInCubic;
  static const Curve microInteractionCurve = Curves.easeInOut;
  static const Curve bounceCurve = Curves.elasticOut;
  static const Curve emphasizedCurve = Curves.easeInOutCubicEmphasized;

  // Scale and offset values for animations
  static const double logoEntranceScale = 0.3;
  static const double buttonPressScale = 0.95;
  static const double errorShakeOffset = 8.0;

  // Input field specific scale and offset values
  static const double inputFocusScale = 1.02;
  static const double inputErrorShakeOffset = 4.0;
  static const double inputClearButtonScale = 0.9;

  // Slide animation offsets
  static const Offset slideFromLeft = Offset(-1.0, 0.0);
  static const Offset slideFromRight = Offset(1.0, 0.0);
  static const Offset slideFromTop = Offset(0.0, -1.0);
  static const Offset slideFromBottom = Offset(0.0, 1.0);

  // Rotation values (in radians)
  static const double logoEntranceRotation = 0.1; // ~5.7 degrees

  // Opacity values
  static const double hiddenOpacity = 0.0;
  static const double visibleOpacity = 1.0;
  static const double dimmedOpacity = 0.6;
}

/// Animation timing configurations for different sequences
class AnimationTimings {
  /// Splash screen animation sequence timing
  static const Map<String, Duration> splash = {'logo': Duration(milliseconds: 0), 'title': Duration(milliseconds: 200), 'tagline': Duration(milliseconds: 350), 'loading': Duration(milliseconds: 500)};

  /// Login screen animation sequence timing
  static const Map<String, Duration> login = {'logo': Duration(milliseconds: 0), 'title': Duration(milliseconds: 150), 'tagline': Duration(milliseconds: 300), 'button': Duration(milliseconds: 450)};
}

---
description: 
globs: *.dart
alwaysApply: false
---
System Prompt: Flutter App Development Best Practices (Riverpod & Fluro)

Use the following guidelines for any Flutter application (of any type) that uses Riverpod for state management and Fluro for routing. The aim is to ensure a scalable, maintainable, and clean architecture with a testable codebase.

Architectural Pattern: Clean Architecture & MVVM Principles
	•	Adopt a Clean Architecture: Structure the app into clear layers to separate concerns. Clean Architecture provides a “clear separation of concerns, encourages interfaces and abstractions, and facilitates changes in the dependencies without affecting the application’s core logic” ￼. In practice, this means keeping UI code independent from business logic and data handling.
	•	Layered Structure: Organize the project with distinct layers (this improves scalability as the app grows ￼). A proven approach is a four-layer architecture ￼:
	•	Presentation Layer: UI widgets and state controllers (e.g. Riverpod Notifiers) that handle user interaction ￼. (In MVVM terms, Widgets are the View, and Riverpod providers act as ViewModels.) UI reads state from providers and sends user intents (events) to these controllers ￼.
	•	Application Layer: Application-specific services or use-case classes that coordinate operations. This layer orchestrates domain logic (e.g. form a workflow by calling domain services/repositories) and can be seen as the middle tier for complex scenarios ￼.
	•	Domain Layer: Pure Dart layer containing business logic and models/entities defining core data structures ￼. Keep this layer platform-independent. Define abstract repositories or use-case interfaces here, representing operations the app needs (e.g. fetching data, saving data) without knowing the implementation. This layer should contain immutable data models and business rules. By defining repository interfaces in domain and implementing them in the data layer, you invert dependencies (the Domain doesn’t depend on Data – Data depends on Domain interfaces) ￼.
	•	Data Layer: Handles data sourcing – e.g. network requests, database access, device storage. This layer provides concrete repository implementations and data sources (API clients, DAOs, etc.) that fulfill the contracts defined in the Domain ￼. It converts raw data (JSON, database rows) into domain models.
	•	Why this pattern: Each layer has a single responsibility and only depends on the layer beneath it (Presentation → Application → Domain → Data). This makes the codebase easier to extend and refactor. For example, you can modify how data is fetched (Data layer) or add new features (Presentation layer) with minimal impact on other layers.
	•	Riverpod Integration: Leverage Riverpod to glue these layers. Provide dependencies via Riverpod (see State Management below) – e.g., a repository implementation in the Data layer can be exposed as a provider, which the Domain or Application layer can consume. This way, Riverpod acts as a dependency injector ensuring that each layer receives the instances it needs without tight coupling. The unopinionated nature of Riverpod’s core ￼ means you should enforce this architecture consistently to avoid spaghetti code.
	•	MVVM Compatibility: This layered approach is compatible with MVVM (Model-View-ViewModel) principles, which also emphasize separating UI from logic ￼. Using Riverpod, your state providers (e.g., StateNotifier classes) serve as the ViewModels holding UI state and logic, the Domain models are the Models, and Flutter widgets are the Views. In fact, with the addition of repository and service layers, MVVM in Flutter becomes very similar to this Riverpod-based Clean Architecture ￼. Following this pattern ensures the UI never directly manipulates data; it only reacts to state provided by ViewModel-like providers.
	•	Example: If building a shopping app, you might have: Presentation layer with CartPage widget and a CartController (Riverpod provider) managing the cart state; Domain layer with a Cart model and a CartRepository interface; Data layer with a CartRepositoryImpl that uses an ApiService and local cache to get cart data. The UI talks to CartController (e.g., to add/remove items), CartController calls domain use-cases or repository methods (through the interface), and the CartRepositoryImpl in Data actually fetches from network or database. This separation makes it easy to change the implementation (say, switch to a different API) or test each part in isolation.

State Management with Riverpod
	•	Use Riverpod for State & Dependency Injection: Riverpod should be the primary way to manage state and provide dependencies across the app. Start by wrapping the app’s root with a ProviderScope – “All Flutter applications using Riverpod must contain a ProviderScope at the root of their widget tree” ￼. This sets up a container for all providers and makes them accessible throughout the widget tree.
	•	Provider Types and Usage: Choose the appropriate Riverpod provider type for each state or dependency:
	•	Use Provider (or AutoDisposeProvider) for read-only values or objects that don’t change (e.g. a configuration object, service instances). For example, a dioClientProvider might provide a configured Dio HTTP client instance to the rest of the app.
	•	Use StateNotifierProvider or the newer AsyncNotifierProvider for pieces of state that have logic and can change over time. Encapsulate the logic in a StateNotifier/AsyncNotifier class. The widget UI will listen to the provider’s state, and user interactions can call methods on the notifier (through ref.read or callbacks) to trigger state changes. Example: a TodoListController as a StateNotifier manages a list of todos and loading/error state; the UI calls addTodo() on it and rebuilds when the state changes. Widgets will “notify events and listen to states emitted from the StateNotifierProvider” ￼, keeping business logic out of the UI.
	•	Use FutureProvider or StreamProvider for straightforward asynchronous data where you just need to fetch and expose the result (or stream). Riverpod will handle the loading and error states for you in an AsyncValue. This is great for simple use cases like a one-time fetch that doesn’t require complex state management.
	•	Use ChangeNotifierProvider only if you must integrate an existing ChangeNotifier class. Prefer StateNotifier over ChangeNotifier for new code to keep logic decoupled from Flutter’s widget lifecycle.
	•	Organize Providers by Layer: Declare providers in the layer where they logically belong. For instance, a repository provider might live in the Data layer code, whereas a UI state provider (controller) lives in the Presentation layer. This makes it clear which part of the app is responsible for which provider. You can provide these at a global level (top of app) or locally (within a subtree using ProviderScope) depending on whether the state should be global or feature-scoped.
	•	Dependency Injection via Providers: Riverpod doubles as a DI system. Provide instances of services and repositories using Provider or Provider.family, instead of creating them inline. For example, define: final apiServiceProvider = Provider((ref) => ApiService()); and have other providers or widgets use ref.watch(apiServiceProvider) to get it. This approach ensures that, say, your StateNotifier that needs an ApiService can just call ref.watch(apiServiceProvider) – no need for global singletons or manual wiring. This also makes testing easier, since you can override these providers with mocks (discussed in Testing).
	•	Avoid Massive State Objects: Keep state granular. It’s better to have multiple focused providers than one huge provider managing unrelated states. For instance, separate user authentication state from UI theme state into different providers. This leads to more modular code and only rebuilds parts of UI that actually depend on a particular state.
	•	Consumer Widgets: In the UI, use ConsumerWidget (or HookConsumerWidget if using hooks) to watch providers. This allows the widget to rebuild automatically when the provider’s state changes. Use ref.watch() in the widget build method to subscribe to a provider. For performing actions or one-time reads (like calling a method on a notifier without rebuilding), use ref.read() inside event handlers (but not in build). This ensures UI stays in sync with state and follows the reactive pattern.
	•	Lifecycle and Disposal: Riverpod automatically disposes providers when they are no longer needed (if using autoDispose or when ProviderScope is removed), preventing memory leaks. Leverage this by using autoDispose on providers tied to UI that can be removed (e.g., a provider inside a screen that should reset when the screen is popped). This keeps the app resource-efficient.
	•	Example: For a login form, you might have a loginFormProvider (StateNotifier) that holds form input state and a submission status. The Login screen widget listens to loginFormProvider for form state (loading, error, success) and displays messages accordingly. When the user taps “Login”, the widget calls a method on the LoginFormNotifier (through ref.read(loginFormProvider.notifier).submit(username, pass)). The notifier then uses ref.watch(authRepoProvider) to get the auth repository and perform the login network call, updating its state to loading/data/error. Thanks to Riverpod, the UI rebuilds with the new state and shows a loader or error message as appropriate. This pattern keeps the async logic out of the widget and in a testable notifier class, following MVVM separation.

Routing and Navigation with Fluro
	•	Use Fluro for Flexible Routing: Fluro is a routing package that “offers flexible routing options, such as wildcards and named parameters, and provides clear route definitions” ￼. It will serve as the navigation backbone of the app. Set up Fluro to manage all routes centrally, rather than using Navigator 1.0 pushNamed or deep links ad-hoc.
	•	Router Initialization: Create a single instance of FluroRouter (often as a global or a singleton). For example, in your main app initialization: final FluroRouter router = FluroRouter();. It’s a good practice to store this router globally so it can be accessed anywhere in the app ￼. This router will hold all defined routes.
	•	Define Routes Centrally: Maintain a route definitions file or class (e.g., AppRoutes) where you define all app routes in one place. Use router.define(routePath, handler: routeHandler, transitionType: ...) for each route. Each route path should be a constant string (for maintainability, define them as static const String homeRoute = "/home"; etc. to avoid typos). Each Handler should build the target screen/widget. For example, router.define("/user/:id", handler: Handler(handlerFunc: (context, params) => UserScreen(userId: params["id"]![0]))); will route to a UserScreen passing the id parameter ￼. Keeping route definitions centralized ensures you have a clear map of navigation in the app and prevents duplicate routes or inconsistencies.
	•	Named Parameters and Wildcards: Take advantage of Fluro’s support for path parameters and query parameters. Design routes that carry minimal information needed to reconstruct the screen’s state (usually an ID or key, not entire objects). For example, use /product/:productId rather than a route that tries to pass a complex object. On navigation, you can use Fluro to extract that ID and then use a provider to fetch or have in memory the corresponding data. This keeps navigation payloads simple.
	•	Route Navigation: Use the Fluro router to navigate instead of the default Navigator where appropriate. For instance, instead of Navigator.pushNamed(context, "/home"), do router.navigateTo(context, "/home", transition: TransitionType.cupertino) (or whatever transition). However, you can integrate Fluro with Flutter’s navigation by setting onGenerateRoute: router.generator in your MaterialApp (or CupertinoApp). This ties Fluro into the Navigator system so that calls to Navigator.pushNameduse your Fluro route definitions [oai_citation:17‡dhiwise.com](mdc:backend/https:/www.dhiwise.com/post/getting-started-with-fluro-a-guide-for-flutter-developers#:~:text=You%20can%20use%20FluroRouter%20with,Here%27s%20an%20example). It’s often convenient to wrap Fluro’srouter.navigateToin your own navigation service or utility functions for consistency (e.g., aNavigationService.pushHome()` that internally calls the router).
	•	Handle Unknown Routes: Set up a notFoundHandler on the Fluro router to catch undefined routes ￼. This is a safety net – if the app tries to navigate to a route that isn’t defined, the notFound handler can show an error page or redirect to a safe page (like redirecting to /home or showing a “404 - Page not found” screen). This ensures the app doesn’t fail navigation silently and provides a better user experience for bad deep links or programmer errors in route names.
	•	Navigation Structure and Lifecycle: Be mindful of how you structure navigation. For complex apps, consider using a stack approach (push/pop) for modal pages and maybe a bottom-nav with tabs for primary sections. Fluro can handle nested navigation if needed (though that can get complex). Keep an eye on the lifecycle: if using global providers for state, pushing a new route won’t dispose those providers (which is usually fine). If you use feature-specific providers with autoDispose, note that navigating away from a screen will dispose its state. For example, if you have a provider inside a page and use autoDispose, going to another page will clear that state – which might be desired (for fresh state next time) or not (if you expect to come back and see previous state). Plan your provider lifetimes according to navigation flows.
	•	Passing Data vs Using Providers: Prefer passing minimal identifiers via routes and then use Riverpod to look up or fetch the actual data. For instance, navigate with a product ID in the route, and inside the new screen, use a provider (maybe a FutureProvider or a cached global state) to get the full product details. This keeps the navigation payload small and leverages state management for data. Only pass larger data through routes if it’s genuinely needed and not easily accessible otherwise.
	•	Route Transitions: Fluro allows custom transitions (e.g., fade, slide, custom curves). Use these sparingly and consistently – for most cases, sticking to the platform default (material or cupertino) transition is best for user familiarity. If you do implement custom transitions for certain flows (like a modal dialog or a special animation between specific screens), define them in the route so it’s clear and centralized. Fluro has built-in transition types (native, Cupertino, FadeIn, etc.).
	•	Best Practices: “Properly handling route definitions, mapping parameters, and understanding the lifecycle of screens are essential” for smooth navigation ￼. In practice, this means: keep your routes file organized (group related routes together or add comments for sections), ensure you consistently use either named parameters or query params as per Fluro’s patterns, and know when a new screen is being created versus using an existing one (which affects state). Avoid deep nesting of routes beyond what’s necessary; too many levels can make back navigation confusing.
	•	Example: You might define routes like /login, /dashboard, /dashboard/profile, /product/:id, etc. The Fluro handler for /product/:id loads a ProductPage which reads the :id param and then uses a provider (productProvider(id)) to load the product details. If an undefined route is hit, your notFoundHandler could redirect to /dashboard with an error message. In your app’s MaterialApp, you set onGenerateRoute: AppRouter.router.generator (assuming you stored it in AppRouter). Now any call to Navigator.pushNamed(context, "/product/123") will go through Fluro and open the ProductPage. This central setup makes it easy to change navigation in one spot (for instance, add a transition or middleware in the route handler) and ensures all developers use the same patterns.

Supporting Libraries and Packages

To complement Riverpod and Fluro, use sustainable, widely-adopted libraries for common tasks. This will avoid reinventing the wheel and ensure long-term support:
	•	HTTP Networking: Use the Dio package for HTTP requests. Dio is a powerful HTTP client for Dart, supporting interceptors, global configuration, FormData for file uploads, request cancellation, file downloading, and timeouts ￼. This is more robust than Dart’s basic http package for large apps. Dio’s interceptors allow implementation of cross-cutting concerns like logging, authentication token refresh, caching, etc., in one place. If you prefer a smaller dependency, the http package (from Dart team) is fine for simple cases, but lacks the advanced features (you’d have to implement error handling and retries yourself) ￼. For most scalable apps, Dio is recommended for its flexibility and popularity. Consider pairing Dio with a library like retrofit (for generating API clients from annotations) or using chopper as an alternative if you like a generator approach.
	•	JSON Serialization & Data Classes: Use code generation to handle JSON serialization and immutable data classes. The json_serializable package (with build_runner) can generate fromJson/toJson for your model classes. Even better, consider using Freezed for data classes and sealed classes. Freezed allows you to define immutable model classes with union types (sealed classes) and will generate equality, copyWith, and serialization code for you ￼. For example, Freezed can create a User class with fromJson automatically, and also help define sealed classes for events or states. Using Equatable is another option to easily make Dart objects comparably by value ￼ (though Freezed can generate == for you). These tools make your Domain models simpler and less error-prone, and they are widely used in the Flutter community, ensuring long-term maintenance.
	•	Local Storage: Many apps need to store data locally (caching responses, user preferences, small datasets). For simple key-value storage (like user settings, a boolean flag, tokens, etc.), use SharedPreferences, which is a lightweight way to store primitives on the device ￼. It’s easy to use and maintained by the Flutter team. For more complex local storage needs (storing lists of objects, offline support, larger data), consider using Hive, a popular fast binary storage solution. “Hive is a highly popular and fast local database solution in the Flutter community” ￼ – it’s NoSQL, so you save Dart objects or maps directly, and it’s very performant for reads/writes. Hive is great for caching API responses or storing user data securely (it even has encryption support). If you need relational storage or complex queries, use SQLite through the sqflite plugin or higher-level O/R mapper like Drift (moor). Drift provides type-safe SQL queries and integrates with Flutter well. For most apps, SharedPreferences + Hive cover a lot of use cases (prefs and structured data respectively). Remember to abstract these behind repository interfaces where appropriate (e.g., a SettingsRepository that uses SharedPreferences under the hood), so you can swap implementations if needed.
	•	Dependency Injection: While Riverpod itself handles a lot of dependency injection via providers, you might still use a service locator for certain cases. get_it is a simple and popular service locator for Flutter/Dart. It “allows decoupling the interface from a concrete implementation and to access the concrete implementation from everywhere in your app” ￼. get_it is often used to register singleton services (like a Database, API client, or Analytics service) that need to be accessed in non-widget classes easily. In a Riverpod app, you can integrate get_it by registering your singletons in main() and then providing them via providers (or directly calling them in legacy code). Another approach is to use injectable (a codegen tool) on top of get_it to automatically register dependencies. However, in most cases you can stick to Riverpod’s own mechanisms – for example, instead of get_it<ApiService>(), you can do ref.read(apiServiceProvider). Using Riverpod for DI has the advantage of easily replacing those dependencies in tests via overrides. Choose one approach and be consistent. If using get_it, avoid using it for mutable state or business logic (prefer providers for those), and use it mainly for truly global services or for bridging non-provider-aware code.
	•	State Management Helpers: Given we use Riverpod, we likely don’t need other state management libraries (like Provider, BLoC, MobX, etc.). Riverpod covers reactivity, but you can complement it with Flutter Hooks (via hooks_riverpod) if you want a cleaner way to manage widget lifecycle (hooks can reduce boilerplate when dealing with AnimationController, TextControllers, etc.). Hooks are optional; they can make UI code more concise but have a learning curve. If you use them, prefer the HookConsumerWidget from hooks_riverpod which integrates hooks and Riverpod providers seamlessly.
	•	UI Component Libraries: Depending on the design needs, you might incorporate component libraries like Flutter SDK’s Material and Cupertino widgets (by default) and maybe community packages for certain widgets (like flutter_svg for SVG images, cached_network_image for efficient image loading, etc.). Ensure any UI library you use is well-supported. If the app design is complex, consider using Flutter’s theming to maintain consistent styles, and organize colors/text styles in a theme file rather than scattering magic values.
	•	Utility Libraries: For various utilities: use intl for internationalization and date/number formatting (especially if the app is multi-lingual), use provider_logger or simply intercept Riverpod’s ProviderObserver to log state changes during development (helps in debugging state issues). For form validation, packages like flutter_form_builder or simple formz can help manage form fields in a testable way. Use logger package for logging structured messages to console (instead of print statements) – it allows different log levels and easier toggling of debug output.
	•	Error Monitoring: Though not exactly a direct coding library, it’s recommended to integrate an error monitoring service (like Sentry or Firebase Crashlytics) early on. These services will catch uncaught exceptions and crashes in production and report them. They often have Flutter SDKs that are easy to add. This is part of long-term maintainability: knowing when your app crashes or has errors in the wild.
	•	Keep Packages Updated: Use stable versions of the above packages and keep an eye on updates. Flutter and Dart evolve quickly; updating packages regularly (within semver compatibility) ensures you get performance improvements and fixes. Manage your pubspec.yaml carefully – use caret syntax for version constraints (e.g., ^5.0.0) to allow non-breaking updates ￼. Avoid pinning to a specific patch version unless you have to, as that can prevent getting important fixes. Periodically run flutter pub outdated to see available upgrades, and test and update dependencies in batches to keep the project up-to-date.

Code Organization and Modularization
	•	Consistent Project Structure: Decide on a clear project structure (feature-first vs layer-first) from the start and apply it consistently. This makes it easier for multiple developers to find and add code without stepping on each other’s toes ￼. Given the Clean Architecture layers, you can either structure your folders by layer (all features’ models in a domain folder, etc.) or by feature (all layers grouped under feature folders). For large apps, a feature-first approach is often a better choice ￼. This means grouping all files related to a feature into one folder (with subfolders for that feature’s presentation, domain, data). For example, you might have lib/features/shopping_cart/presentation/ (widgets, controllers for cart UI), .../domain/ (cart models, usecases), .../data/ (cart API, repository impl). This way, each feature is self-contained and easier to work on independently. It also scales well as teams grow (each team or developer can handle a feature directory). Conversely, a layer-first structure (all models together, all widgets together) can become unwieldy as the number of features increases, because each layer folder grows very large ￼.
	•	Separate Core Module: In addition to feature folders, maintain a core or common module for shared utilities. This could be lib/core/ containing things like networking code (e.g., a Dio client setup), error classes, theme definitions, constants, and helper functions that are used across features. This avoids duplication and centralizes important cross-cutting code. Make sure this core does not become a dumping ground – it should contain only those pieces that genuinely are reused in multiple places (e.g., a Failure class for error handling, or a AppStyles class for UI constants).
	•	Encapsulation and Modularization: Encapsulate feature logic within its module. One feature should not directly access another feature’s internals; if communication is needed (say Feature A needs something from Feature B), expose it via a service or the state management (for example, a Provider in Feature B that Feature A can read, or a method in a common interface). This decoupling ensures you can modify or even remove a feature without breaking others. In extreme scalability cases, you can even compile features into separate Flutter modules or packages. Flutter allows creating custom local packages inside the packages/ directory or using path dependencies. For instance, you could have a package:my_app_auth for authentication feature and my_app_profile for profile feature, each as separate Dart packages sharing the same repository. This enforces strict modular boundaries (since they can only interact through public APIs). This level of modularization might be overkill for small-medium apps, but keep it in mind as the project grows or if you plan to allow reuse of modules across apps.
	•	File and Class Organization: Within each layer or feature, follow clear conventions for naming and grouping. For example, in the presentation layer, you might group by screen: have a folder for each screen containing the main Widget file, its controller/provider, and maybe a subfolder for widgets used only by that screen. In the data layer, you might have a repositories/ folder and models/ (DTOs) folder. Consistently name files (e.g., home_page.dart, home_controller.dart, home_repository.dart, home_model.dart etc.) so it’s obvious what each does. This consistency greatly aids newcomers to the codebase.
	•	Avoid God Classes: No single file should hold too many responsibilities. For instance, don’t put 10 provider definitions in one file; instead, split them by domain or feature. A provider that fetches products goes in the product feature folder, etc. Similarly, avoid giant utility classes – break them into smaller ones grouped by purpose.
	•	Dependency Rule: Uphold the dependency direction of Clean Architecture in your code organization. This means inner layers should not import from outer layers. For example, nothing in domain/ should import from data/ or presentation/. The data layer can import domain (to implement interfaces or use models), and presentation can import domain (to use models or interfaces) and maybe application services. Enforce this by reviewing imports – if you see an import of a data class in a widget, that’s a red flag (likely the widget should be using a domain model or going through a provider). Sometimes using Dart’s export and part files can help present a cleaner API of a module.
	•	Example Structure:

lib/
  core/
    error/failures.dart
    network/api_client.dart
    utils/date_formatter.dart
    // ...common utilities
  features/
    authentication/
      presentation/
        login_page.dart
        login_controller.dart
        widgets/ (maybe for smaller UI components)
      domain/
        entities/ user.dart
        usecases/ login_usecase.dart
        repos/ auth_repository.dart (abstract)
      data/
        models/ user_dto.dart
        repositories/ auth_repository_impl.dart
    product_catalog/
      presentation/
        product_list_page.dart
        product_controller.dart
        product_details_page.dart
        product_details_controller.dart
      domain/
        entities/ product.dart, category.dart
        repos/ product_repository.dart
      data/
        models/ product_dto.dart
        repositories/ product_repository_impl.dart
        data_source/ product_api.dart
    // ... other features
  app.dart (MaterialApp and Fluro setup)
  main.dart

This is just one way; adjust to your needs. The key is that each feature contains everything it needs, and common stuff goes to core.

	•	Modular Services: If some functionality is cross-cutting but complex, consider making it a module as well. For example, if many features need offline caching, you could have a offline_cache/ module in core or features that provides that service. Or a separate module for push notifications handling. Treat these like mini-features.
	•	Review and Refactor: As the codebase grows, periodically review if the current structure still makes sense. It’s okay to refactor folder structure (with appropriate IDE support to update imports) to better accommodate new features. What’s important is that at any given time, the structure is logical and documented for the team. New engineers should be able to navigate the project by reading a brief README that explains the module structure.

Dependency Management Best Practices
	•	Manage Pub Dependencies Thoughtfully: Use the pubspec to manage third-party packages carefully. Follow Dart guidelines for versioning: use caret syntax (^) for version ranges so you automatically get non-breaking updates ￼. For example, dio: ^5.1.0 will allow minor/patch upgrades. This ensures your app can receive bug fixes and improvements without manual intervention (within a safe range). Lock file (pubspec.lock) will pin the exact versions you’re using; commit it to source control for consistency across environments.
	•	Regular Updates: Periodically update your dependencies to avoid getting too far behind (technical debt). A good practice is to run flutter pub outdated and flutter pub upgrade every few sprints and test the app. Stay especially on top of Riverpod and Flutter SDK updates, as those can have important fixes. Before major version upgrades (like Riverpod 2 to 3 or Flutter 3 to 4), read the changelogs for breaking changes.
	•	Use Dev Dependencies Appropriately: Keep dev-specific packages (for testing, code generation, linting) in the dev_dependencies section of pubspec ￼. For example, build_runner, lint packages, flutter_test (comes by default) should be dev dependencies. This keeps them out of release builds and avoids bloating the APK. The rule is: if you don’t import it in your app’s runtime code (under lib/), it should probably be a dev_dependency ￼.
	•	Avoid Unnecessary Dependencies: Each external package is a maintenance cost. Use packages that are widely used and actively maintained. Before adding a new package, consider if Flutter SDK or an existing utility you have can do the job to reduce dependency count. For instance, for something simple like parsing a date, you might not need a whole package – Dart’s DateTime and intl might suffice. Fewer dependencies mean fewer potential version conflicts and easier upgrades in the future.
	•	Conflict Resolution: If you encounter version conflicts (e.g., two packages require different versions of a third package), you might need to use dependency_override in pubspec to force a resolution. However, use this sparingly and try to resolve conflicts by upgrading packages in harmony if possible. Overriding dependencies can lead to runtime issues if a package isn’t tested with the overridden version.
	•	Locking Down Critical Versions: For critical pieces (like Firebase libs or something that you don’t want auto-updated unexpectedly), you can pin or use dependency_override to a specific version. But generally prefer not to lock unless necessary.
	•	Dependency Injection (in code): Manage your own class dependencies with care as well. Do not instantiate heavy objects at the point of use if they can be injected. For example, if a service class requires a Dio client, pass it through the constructor (and use Riverpod to supply it) instead of creating a new Dio inside the service. This follows the inversion of control principle and makes testing easier (you can pass a mock). In Flutter, thanks to Riverpod, you can provide these dependencies globally. Ensure only one instance of classes like API clients, database, etc., is created (singleton pattern or one per ProviderScope) to avoid resource contention. Riverpod’s providers (especially if using the flutter_riverpod approach) naturally create singletons for you unless you use autoDispose.
	•	Transitive Dependencies: Be aware of what your packages bring in transitively. If you use a large library that itself has many dependencies, note that those will also need to be managed. For example, some utility package might bring in crypto or collection packages. Keep an eye on the size and impact.
	•	Remove Unused Dependencies: Periodically prune dependencies that are no longer used. It’s common for apps to accumulate packages during development that end up not being used in the final product. Removing them reduces app size and complexity. Use tools or manually check imports to ensure a package is really not used before removal.
	•	Example: In the pubspec, you might have:

dependencies:
  flutter:
    sdk: flutter
  flutter_riverpod: ^2.3.0
  fluro: ^2.0.5
  dio: ^5.0.0
  shared_preferences: ^2.0.15
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  freezed_annotation: ^2.1.0
  get_it: ^8.0.3
  # ... other dependencies
dev_dependencies:
  build_runner: ^2.3.3
  freezed: ^2.1.0
  flutter_test:
    sdk: flutter
  lint: ^2.0.0

This setup uses caret versions for flexibility. We include freezed_annotation in deps and freezed in dev_deps for codegen. We’ve ensured not to include anything unnecessary (e.g., no provider because we use Riverpod exclusively to avoid duplication). If later we decide get_it is not needed, we remove it both from code and pubspec.

	•	Documentation: Document any unusual dependency choices in the README or code comments. For example, if you use a specific version of a package due to a bug in the latest version, note that for future developers. Or if you evaluate alternatives (like why Fluro over GoRouter), note that in docs so it’s clear and saves reconsideration later unless requirements change.

Deprecated flutter tools
	- 'withOpacity' is deprecated and shouldn't be used. Use .withValues() to avoid precision loss. Try replacing the use of the deprecated member with the replacement
	- 'surfaceVariant' is deprecated and shouldn't be used. Use surfaceContainerHighest instead. This feature was deprecated after v3.18.0-0.1.pre. Try replacing the use of the deprecated member with the replacement.
	- Don't use 'BuildContext's across async gaps. Try rewriting the code to not use the 'BuildContext', or guard the use with a 'mounted' check.

Error Handling and Logging
	•	Error Propagation Strategy: Establish a clear strategy for handling errors throughout the layers. Aim to catch errors as low as possible (in the data layer or at boundaries) and convert them into a form that higher layers can handle or display. For example, catch network exceptions in the repository implementation and wrap them in a custom Failure or Exception type defined in the domain layer (e.g., throw a NetworkFailure or return a Result.failure). This prevents raw exceptions from leaking upward. The UI layer should not have to know about HTTP status codes or SQLite exceptions – it should deal with abstracted error objects or states.
	•	Use AsyncValue for UI State: Riverpod’s AsyncValue is a powerful tool to represent data along with loading/error state in a single object. It allows handling loading, error, and data states in a clean, type-safe way ￼. When using providers that fetch data (Future/StreamProviders or AsyncNotifiers), prefer to have them return an AsyncValue<T> as state. This standardizes the way you handle errors in the UI: you can call .when(data:…, loading:…, error:…) on the AsyncValue to easily build UI for each state. This avoids the need for separate bools for loading or error flags. Example: final userProvider = FutureProvider<User>((ref) async { return await repository.fetchUser(); }); – this userProvider will automatically yield AsyncValue<User>. In the UI, ref.watch(userProvider) gives an AsyncValue that could be loading, data(user), or error(err). You can then display a loader or error message accordingly.
	•	AsyncValue.guard: When performing an async operation inside a StateNotifier or provider, use AsyncValue.guard to simplify error handling. AsyncValue.guard(() => someFuture) will run the future and catch any exception, transforming it into an AsyncValue.error with stack trace ￼. This reduces boilerplate try-catch in your async state management. “AsyncValue.guard wraps an asynchronous function, handling any exceptions for you and converting them into an error state within AsyncValue” ￼. This way, your notifier doesn’t need its own try-catch every time – it just assigns the result of guard to state. Ensure that exceptions you expect (like a business rule validation) are thrown as exceptions so they get caught and put into AsyncValue. In turn, the UI will get an AsyncValue in the error state, which you handle via .when or .maybeWhen to show an error.
	•	Custom Error Classes: Use custom exceptions or failure classes to categorize errors. For example, define an abstract AppError (or Failure) class in the domain layer, with subclasses like NetworkError, ServerError, CacheError, ValidationError, etc. This allows different handling for different error types ￼. Perhaps a NetworkError might show a “Check your internet connection” message, while a ValidationError might highlight a form field. By throwing or returning these from the repository/usecase layer, the presentation layer can inspect the error type. You can integrate this with AsyncValue by throwing a specific exception; AsyncValue will carry that error. Alternatively, have your notifier catch exceptions and map them to an AsyncValue.error(CustomError(...)). The goal is to avoid just catching everything and returning a generic message – be specific when you can.
	•	Global Error Handling: Despite best efforts, some errors might go uncaught in the normal flow (like programming errors). Set up a global error handler to catch those. In Flutter, you can do:

FlutterError.onError = (FlutterErrorDetails details) {
  FlutterError.presentError(details);
  // log to service or console
};
PlatformDispatcher.instance.onError = (error, stack) {
  // handle async errors that were not in a Future (like timers)
  return true; // to consume the error
};

Additionally, if using an error reporting service (Crashlytics/Sentry), initialize it to catch errors. For example, Sentry’s Flutter integration can capture all uncaught exceptions. This global net ensures the app can log an error (and possibly show a friendly fallback UI) instead of just crashing unexpectedly.

	•	User Feedback on Errors: When an error occurs (say a network request fails), ensure the user is informed in a friendly way. This could be a SnackBar (“Failed to load data. Pull to retry.”), an error widget in place of content (with a retry button), or a dialog for critical issues. The presentation layer should decide how to present errors, but it relies on the error data it gets from domain/application. So design your error data to include user-friendly messages or codes that the UI can interpret. For example, a LoginFailure.invalidCredentials could be used by the UI to show “Invalid username or password”, whereas a generic LoginFailure.serverError shows “Something went wrong, please try again later.”
	•	Logging: Integrate logging for errors and important events. During development, use the logger package or even print to console for debugging. You can set up Riverpod ProviderObserver to listen to provider state changes and log them, which is useful to trace what led to an error state. In production, limit logging to error reporting services or internal logs (avoid spamming the console which doesn’t exist in release on mobile). Logging is also important in the data layer: log the URL and response of failing HTTP calls (without sensitive data) to diagnose issues. If using Dio, you can use an Interceptor to log requests/responses. Make sure not to log sensitive info (like user tokens) in production.
	•	Graceful Degradation: In error scenarios, the app should handle it gracefully, not just throw. For instance, if a cached value exists, use it when network fails (stale-but-valid data). Or if a feature fails to load, the app should still function minimally. Plan how your app behaves offline or on server errors: perhaps certain features are disabled with a message.
	•	Testing Error Cases: As part of testing (next section), write tests for error scenarios to ensure your handling works (e.g., test that a 500 error from API results in an AsyncValue.error and the UI shows an error widget).
	•	Example: Suppose the app has a data layer class WeatherApi that fetches weather. If it throws a SocketException, catch that in the repository and throw a NetworkError. If the server returns a 404 or invalid data, throw a ServerError. In the WeatherNotifier (StateNotifier), use AsyncValue.guard(() => repository.getWeather(city)). In the UI, do:

ref.watch(weatherProvider(city)).when(
  data: (weather) => Text("${weather.temp}°"),
  loading: () => CircularProgressIndicator(),
  error: (err, stack) {
    if (err is NetworkError) {
      return Text("No internet. Showing cached data...");
    } else {
      return Text("Failed to load weather :(");
    }
  },
);

This way, a network error can be differentiated from other errors. Also, maybe the repository cached last weather – the notifier could emit that cached data even on error if available. The key is the user isn’t left with a blank screen; they get a message or cached content.

Testing Strategy (Unit, Widget, Integration)

Adopt a robust testing approach to ensure the codebase remains clean and regressions are caught early. Include unit tests, widget tests, and integration tests:
	•	Unit Testing (Logic in Isolation): Unit tests should cover business logic and any pure Dart code (the Domain and parts of Application layer, plus Data layer logic with mocks). With our architecture, a lot of logic lives in Riverpod notifiers, use-case classes, and repository implementations. Test these in isolation by providing them with dummy or mock inputs. For example, test that your LoginUseCase correctly validates input and calls the repository. Use mocking frameworks like mockito or mocktail to stub out dependencies (e.g., a mock AuthRepository that throws an error or returns a fake user). Riverpod makes it easy to test providers: you can instantiate a ProviderContainer in a Dart test and use container.read(yourProvider) to get results. Providers are pure Dart, so you don’t need a Flutter tester for unit tests. Override dependencies in tests – “we can use the overrides parameter of ProviderScope/ProviderContainer to override the behavior of repositoryProvider” ￼. For example, if you have a repositoryProvider that normally provides a real API service, in a test you can do:

final container = ProviderContainer(overrides: [
  repositoryProvider.overrideWithValue(FakeRepository()),
]);
final result = container.read(todoListProvider.future);
expect(await result, isNotEmpty);

This way, todoListProvider will use FakeRepository instead of the real one ￼. This technique is powerful for unit testing Riverpod-based logic without making actual network calls. Also verify that state transitions occur as expected: e.g., after calling loginNotifier.login(), assert that loginNotifier.state goes to loading then success or error accordingly. Each unit test should ideally focus on one behavior.

	•	Widget Testing (GUI in Isolation): Widget tests (using flutter_test’s testWidgets) allow you to render UI components in a simulated environment and verify their behavior. They are faster than full integration tests and don’t require a physical device. In widget tests, you can pump a widget tree that includes your widget and a ProviderScope wrapping it. Use provider overrides to control the state your widget receives. For example, to test the Login screen UI for an “invalid credentials” error state, you can override the loginProvider to immediately provide an AsyncValue.error(LoginError.invalidCredentials) before pumping the widget. Then verify that a certain error text is found. Wrap widgets with ProviderScope in tests: “Use the ProviderScope overrides to provide a fake implementation… child: MyApp() …” ￼ – this is the pattern for injecting test dependencies in widget tests. You can also utilize Goldens (screenshot testing) to ensure UI looks correct under various states (loading spinner visible, error message shown, etc.). When writing widget tests, utilize Flutter’s widget testing utilities: await tester.pumpWidget(...) to build, tester.tap and tester.enterText to simulate user input, and tester.pump(Duration) to advance timers or animations. Verify UI with finders: e.g., expect(find.text("Invalid password"), findsOneWidget). Widget tests are great for checking interactive widgets, layout, and widget-tree outcomes for given states.
	•	Integration Testing (End-to-End): Integration tests validate the app as a whole running on a real or simulated device. They ensure that all pieces work together – from UI down to data layer. Use Flutter’s integration_test package for this (Flutter now merges integration test capability with the app, superseding the old flutter_driver). Integration tests should cover critical user journeys, like “Login and navigate to Dashboard” or “Add item to cart and complete checkout flow”. They actually launch the app (or a specific screen) on an emulator/simulator or physical device and simulate taps and waits like an actual user. Under the hood, integration tests use the same WidgetTester API, but driving the real app. The integration_test package lets you use flutter drive command to run tests on a physical device or emulator ￼, and you can even run them on CI services or Firebase Test Lab for broader coverage. Write integration tests in the integration_test/ directory (e.g., integration_test/app_test.dart). You typically initialize the app in the test with something like IntegrationTestWidgetsFlutterBinding.ensureInitialized(); then use tester to find and tap widgets. Since these tests run the full app, you might want to use a test configuration where the app points to a test environment (for example, using a different API base URL or a mock server). One approach is to have a bool or flavor for testing that uses a Mock repository everywhere (so no real network calls happen, ensuring tests are deterministic). Or use dependency overrides at the app entry for test (similar to widget test, but in integration test you might not have direct access to override before app runs, so often a special main for tests is created).
	•	Coverage and Maintenance: Aim to cover all critical logic with unit tests (especially the domain and data layer). For UI, test the essential screens and interactions (you don’t need 100% widget coverage, but do test dynamic widgets like forms, lists, and navigation). Integration tests, due to being slower, should cover only the main use cases – logging in, basic navigation, and maybe one scenario per major feature (like creating an item, performing a transaction, etc.). Use Continuous Integration (CI) to run tests on each commit or PR, so regressions are caught. Flutter’s flutter test can run unit/widget tests headlessly, and integration tests can be run on emulators or using flutter drive. Keep test data and scenarios realistic but not reliant on external systems (if using a test backend, ensure it’s stable or use local mocks).
	•	Testing Riverpod specifics: Test that providers produce expected outputs. For example, if you have a counterProvider that increments, write a test that reads it, then calls the increment method (via notifier) and verify the state increased. The Riverpod official docs demonstrate that when using separate ProviderContainers, “no state is shared between tests”, so tests won’t interfere with each other ￼ – leverage that by always creating a fresh container per test (or using the setUp function to create one, and addTearDown to dispose it).
	•	Mocking vs Fake Implementations: Decide whether to use a mocking library (like Mockito) or just write simple fake classes for tests. E.g., a FakeRepository that implements your Repository interface with in-memory returns. Fakes can be easier to manage for small interfaces, while mocking frameworks can auto-generate mocks for larger APIs but require learning their syntax (and sometimes can lead to brittle tests if over-specified). With Riverpod overrides, often you can get by with simple fakes: override a provider to return a stubbed instance or a predetermined value (like override a FutureProvider to return a preset AsyncValue).
	•	Test the Error Paths: In addition to normal cases, test error handling. For unit tests, simulate exceptions in the repository and ensure the notifier outputs an error state (and maybe the error is of the right type). For widget tests, simulate an error state provider and ensure the UI shows the error message widget. For integration, you can force an error by pointing to a test server that returns errors and verifying the app doesn’t crash and handles it.
	•	Performance of Tests: Keep unit tests lightning fast (they should be if not doing I/O). Widget tests are a bit slower (because they render UI); try not to pump excessively large trees unnecessarily. Integration tests are slowest – possibly a minute or two each because of startup and real interaction delays – so keep their count reasonable.
	•	Example: You write a unit test for CartController: you override cartRepositoryProvider with a fake that returns a known list of items. You then call controller.loadCart() and expect that controller.state becomes AsyncValue.data with that list. Next, a widget test for CartPage: override cartProvider to return an AsyncValue.data with 2 items, pump the CartPage and verify that 2 item widgets are rendered and the total price is shown correctly. Another widget test: override cartProvider to AsyncValue.error(SomeError) and ensure an error Text or widget is displayed. Finally, an integration test: start the app, navigate to Cart screen (assuming user is logged in or skip auth for test), then tap “Add to Cart” on a product, then go to Cart and verify the item appears. This end-to-end test ensures the add-to-cart flow works with actual widget interaction. By covering all these levels, you can refactor internals with confidence, since tests will catch if something breaks (e.g., if you change repository logic and forget to update the notifier, a unit test will fail).

MCP tool use:
	- If you need documentation about a dependencies using ./.cursor/mcp.json

By following these guidelines as a system prompt, any developer or AI assistant involved in the project will adhere to consistent best practices. This ensures the Flutter application remains scalable, maintainable, and robust from day one through the long term, with a clean architecture and a comprehensive test suite backing it up.
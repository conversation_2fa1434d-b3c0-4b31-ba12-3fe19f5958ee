{"mcpServers": {"flutter Docs": {"url": "https://gitmcp.io/flutter/flutter"}, "freezed Docs": {"url": "https://gitmcp.io/rrousselGit/freezed"}, "riverpod Docs": {"url": "https://gitmcp.io/rrousselGit/riverpod"}, "flutter_map Docs": {"url": "https://gitmcp.io/fleaflet/flutter_map"}, "fluro Docs": {"url": "https://gitmcp.io/lukepighetti/fluro"}, "flutter-packages Docs": {"url": "https://gitmcp.io/material-foundation/flutter-packages"}, "mcp-deepwiki": {"command": "npx", "args": ["-y", "mcp-deep<PERSON><PERSON>@latest"]}}}
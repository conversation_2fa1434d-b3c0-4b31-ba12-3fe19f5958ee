# Context
Filename: applovin-max-integration-task.md
Created On: 2024-12-28
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
Implement AppLovin MAX SDK and Ad Units in BuscaFarma Flutter application with strategic ad placements for optimal monetization while maintaining positive user experience. The integration includes Banner/MREC ads, Interstitial ads, and App Open ads positioned at natural user flow points.

# Project Overview
BuscaFarma is a Flutter application for finding pharmacies and medication prices in Peru. The app features:
- Map-based pharmacy search with location services
- Medication search with price comparison
- Favorites system for medications
- User authentication with Google Sign-In
- In-app review and update functionality
- Material Design 3 theming with animations

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## Current App Architecture
- **Main Entry Point**: `lib/main.dart` - Initializes Firebase, sets up router, and configures providers
- **Navigation**: Uses Fluro router with centralized route management in `core/router/`
- **State Management**: Flutter Riverpod for all state management
- **UI Framework**: Material Design 3 with dynamic color theming
- **Key Features**: Map view, favorites, authentication, splash screen, in-app updates/reviews

## Key Files for Ad Integration
1. **Main App**: `lib/main.dart` - App initialization and provider setup
2. **Splash Screen**: `features/splash/presentation/pages/splash_page.dart` - App startup flow
3. **Map Page**: `features/map/presentation/pages/map_page.dart` - Primary user interaction area
4. **Favorites Page**: `features/favorites/presentation/pages/favorites_page.dart` - User favorites management
5. **Main Navigation**: `features/navigation/presentation/pages/main_navigation_page.dart` - Bottom navigation
6. **Search Flow**: `features/map/presentation/widgets/search_suggestions_panel.dart` - Medicine search process
7. **Profile Page**: `features/auth/presentation/pages/profile_page.dart` - User profile management

## Current Dependencies
- Flutter Riverpod for state management
- Firebase suite (Core, Auth, Analytics, Crashlytics, Remote Config)
- Google Sign-In for authentication
- In-app review and update packages
- Flutter Map for map functionality
- Dio for HTTP requests

## User Flow Analysis
1. **App Launch**: Splash screen → Authentication check → Main navigation
2. **Primary Usage**: Map view with search → Medicine selection → Location processing → Results display
3. **Secondary Features**: Favorites management, profile settings, authentication
4. **Natural Break Points**: 
   - After successful search completion
   - When closing establishment details
   - App startup after splash
   - Profile page exit

## Technical Constraints
- Uses Material Design 3 theming
- Extensive animation system with staggered controllers
- Complex state management with multiple providers
- Background geocoding processes that can be cancelled
- Existing error handling and loading states

## Ad Placement Opportunities
1. **Banner Ads**: Map page bottom, Favorites page bottom, Main navigation persistent
2. **Interstitial Ads**: Post-search completion, establishment details closure, profile exit
3. **App Open Ads**: After splash screen completion, before main navigation

## Integration Considerations
- Must not interfere with existing FloatingActionButton positioning
- Should respect existing loading states and cancellation mechanisms
- Need to handle ad loading failures gracefully
- Must maintain app performance during background processes
- Should integrate with existing error handling patterns

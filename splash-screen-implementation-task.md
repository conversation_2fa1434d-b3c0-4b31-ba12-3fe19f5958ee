# Context
Filename: splash-screen-implementation-task.md
Created On: 2024-12-19
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
Create a new splash screen route in the Flutter application that serves as an initial loading screen. This splash route should:

1. Display a loading indicator or splash screen UI
2. Perform necessary data initialization tasks (such as checking authentication status, loading user preferences, or fetching initial app data)
3. Automatically navigate to the appropriate screen based on the user's authentication state:
   - If the user is authenticated (has valid JWT token), navigate to the dashboard/main screen
   - If the user is not authenticated or token is expired, navigate to the login screen
4. Handle any errors during the initialization process gracefully
5. Follow the existing app's navigation patterns and UI/UX design standards
6. Implement proper state management for the splash screen logic
7. Ensure the splash screen has appropriate timing (not too fast, not too slow) for a good user experience

The splash screen should be the initial route when the app starts, replacing any current direct navigation to login or dashboard screens.

# Project Overview
BuscaFarma is a Flutter application that helps users find nearby pharmacies. The app uses:
- Flutter with Riverpod for state management
- Fluro for routing and navigation
- Firebase for authentication (Google Sign-In)
- JWT tokens for backend authentication
- Material Design 3 with dynamic color theming
- Existing authentication flow with AuthProvider and AuthRepository

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## Current Application Structure
- **Main Entry Point**: `lib/main.dart` with `BuscaFarmaApp` as root widget
- **Current Initial Route Logic**: Directly determines initial route based on `authState.isAuthenticated` in main.dart line 77
- **Router**: Uses Fluro router with centralized route definitions in `AppRouter` class
- **Authentication**: AuthProvider with AuthNotifier manages authentication state using JWT tokens
- **Current Routes**: `/` (root), `/map`, `/login`, `/profile`, `/search`, `/favorites`, `/settings`, `/profile/edit-health-data`

## Current Authentication Flow
- **Token Storage**: Uses SharedPreferences via AuthLocalProvider for JWT access/refresh tokens
- **Authentication Check**: `checkAuthState()` method in AuthNotifier validates stored tokens
- **Token Validation**: Backend JWT service validates tokens with 1-day expiry in development
- **Current Initialization**: Authentication check happens in `initState()` of `_BuscaFarmaAppState` with `Future.delayed(Duration.zero)`

## Current UI/UX Patterns
- **Theme**: Material Design 3 with dynamic color theming using `DynamicColorBuilder`
- **Loading Indicators**: Uses `CircularProgressIndicator` and `LinearProgressIndicator` consistently
- **Color Scheme**: Blue primary color with teal secondary, supports light/dark/system themes
- **Loading States**: Consistent loading state management with `isLoading` boolean flags
- **Animations**: 300ms duration animations with `Curves.easeIn` and `Curves.easeOut`

## Current Navigation Patterns
- **Router Setup**: `AppRouter.setupRouter()` called before `runApp()`
- **Route Handlers**: Centralized in `RouteHandlers` class with specific handlers for each route
- **Navigation Extensions**: `RouterExtensions` provides convenient navigation methods
- **Transition Types**: Uses `TransitionType.fadeIn` for most routes

## Key Dependencies and Providers
- **Auto Location**: `autoLocationProvider` and `autoLocationInitializerProvider` for GPS functionality
- **Theme**: `themeProvider` for theme management
- **Auth**: `authProvider` for authentication state
- **Firebase**: Initialized in main() before app startup

## Current Issues with Initial Route Logic
- Authentication check happens after MaterialApp creation
- Initial route is determined synchronously based on potentially stale auth state
- No loading screen during authentication verification
- No centralized initialization logic for app startup tasks

# Proposed Solution (Populated by INNOVATE mode)

## Approach Analysis

### Approach 1: Splash Screen as Initial Route ⭐ RECOMMENDED
Create a dedicated `/splash` route that becomes the new initial route, handling all initialization tasks before navigating to the appropriate screen.

**Pros:**
- Clean separation of initialization logic
- Follows existing Fluro routing patterns
- Easy to test and maintain
- Clear navigation flow
- Consistent with app's route-based architecture

**Cons:**
- Requires modification of multiple files
- Adds another route to manage

### Approach 2: Splash Provider with Conditional Rendering
Create a splash provider that manages initialization state and conditionally renders either the splash screen or the main app.

**Pros:**
- Minimal routing changes required
- Centralized initialization logic
- Easier state management

**Cons:**
- Mixes initialization logic with main app logic
- Harder to test in isolation
- Deviates from route-based architecture

### Approach 3: Hybrid Approach
Combine both approaches with splash provider for state management and splash route for UI presentation.

**Pros:**
- Best of both worlds
- Maintains route-based architecture
- Clean state management

**Cons:**
- More complex implementation
- Requires coordination between provider and routing

## Selected Solution: Splash Screen as Initial Route

**Rationale:** This approach best aligns with the existing Fluro routing architecture, provides clear separation of concerns, and maintains consistency with established patterns in the codebase.

## Implementation Strategy

### Core Components
1. **SplashProvider**: StateNotifier to manage initialization state and coordinate startup tasks
2. **SplashScreen**: UI widget displaying loading indicator with app branding
3. **Route Integration**: Add `/splash` route to AppRouter and make it the initial route
4. **Navigation Logic**: Automatic navigation to appropriate screen after initialization

### Initialization Tasks
1. **Authentication Check**: Validate stored JWT tokens using existing AuthProvider
2. **Theme Loading**: Load user theme preferences via ThemeProvider
3. **Firebase Verification**: Ensure Firebase is properly initialized
4. **Minimum Display Time**: Ensure splash shows for at least 1.5 seconds for good UX
5. **Error Handling**: Graceful error handling with retry options

### UI/UX Design
- **Branding**: App logo (pharmacy icon) and "BuscaFarma" text consistent with login screen
- **Loading Indicator**: CircularProgressIndicator with Material Design 3 theming
- **Animations**: 300ms fade transitions consistent with existing app patterns
- **Error States**: Clear error messages with retry button
- **Timing**: 1.5-5 second display window for optimal user experience

# Implementation Plan (Generated by PLAN mode)

## File Structure and Components

### New Files to Create
1. `lib/features/splash/presentation/providers/splash_provider.dart` - State management for splash screen
2. `lib/features/splash/presentation/pages/splash_page.dart` - Splash screen UI
3. `lib/features/splash/domain/entities/splash_state.dart` - Splash state model

### Files to Modify
1. `lib/core/router/app_routes.dart` - Add splash route constant
2. `lib/core/router/route_handlers.dart` - Add splash route handler
3. `lib/core/router/app_router.dart` - Register splash route
4. `lib/main.dart` - Change initial route to splash
5. `lib/core/router/router_extensions.dart` - Add splash navigation extension

## Detailed Change Plan

### Change 1: Create Splash State Entity
- **File**: `lib/features/splash/domain/entities/splash_state.dart`
- **Rationale**: Define immutable state model for splash screen initialization status
- **Details**: Create Freezed class with initialization status, error message, and target route

### Change 2: Create Splash Provider
- **File**: `lib/features/splash/presentation/providers/splash_provider.dart`
- **Rationale**: Manage splash screen state and coordinate initialization tasks
- **Details**: StateNotifier implementation with auth check, theme loading, timing logic

### Change 3: Create Splash Screen UI
- **File**: `lib/features/splash/presentation/pages/splash_page.dart`
- **Rationale**: Provide visual feedback during app initialization
- **Details**: Material Design 3 UI with loading indicators, error states, app branding

### Change 4: Add Splash Route Constant
- **File**: `lib/core/router/app_routes.dart`
- **Rationale**: Centralize route path definition following existing pattern
- **Details**: Add `static const String splash = '/splash';`

### Change 5: Add Splash Route Handler
- **File**: `lib/core/router/route_handlers.dart`
- **Rationale**: Define route handler following existing Fluro pattern
- **Details**: Create `splashHandler` that returns `SplashPage()`

### Change 6: Register Splash Route
- **File**: `lib/core/router/app_router.dart`
- **Rationale**: Register route in router configuration
- **Details**: Add route definition with fadeIn transition

### Change 7: Update Initial Route
- **File**: `lib/main.dart`
- **Rationale**: Make splash screen the entry point instead of conditional auth routing
- **Details**: Change `initialRoute` to `AppRoutes.splash`, remove auth-based routing logic

### Change 8: Add Navigation Extension
- **File**: `lib/core/router/router_extensions.dart`
- **Rationale**: Provide convenient navigation method following existing pattern
- **Details**: Add `goToSplash()` method for consistency

## Implementation Checklist

1. Create splash state entity with Freezed annotations and proper state management
2. Create splash provider with StateNotifier pattern and initialization logic
3. Create splash screen UI with Material Design 3 theming and loading indicators
4. Add splash route constant to AppRoutes class
5. Add splash route handler to RouteHandlers class
6. Register splash route in AppRouter with fadeIn transition
7. Update main.dart to use splash as initial route and remove conditional auth logic
8. Add splash navigation extension to RouterExtensions
9. Test splash screen initialization flow with different authentication states
10. Verify error handling and retry functionality works correctly
11. Test navigation timing and ensure good user experience
12. Validate theme application and accessibility support

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "Testing splash screen implementation"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   2024-12-19
    *   Step: 1. Create splash state entity with Freezed annotations and proper state management
    *   Modifications: Created lib/features/splash/domain/entities/splash_state.dart with SplashStatus enum, SplashNavigationTarget enum, and SplashState Freezed class
    *   Change Summary: Implemented immutable state model for splash screen with initialization status tracking
    *   Reason: Executing plan step 1
    *   Blockers: None
    *   Status: Pending Confirmation

*   2024-12-19
    *   Step: 2. Create splash provider with StateNotifier pattern and initialization logic
    *   Modifications: Created lib/features/splash/presentation/providers/splash_provider.dart with SplashNotifier class implementing initialization coordination
    *   Change Summary: Implemented splash provider with auth check, theme loading, timing logic, and error handling
    *   Reason: Executing plan step 2
    *   Blockers: None
    *   Status: Pending Confirmation

*   2024-12-19
    *   Step: 3. Create splash screen UI with Material Design 3 theming and loading indicators
    *   Modifications: Created lib/features/splash/presentation/pages/splash_page.dart with animated splash screen UI
    *   Change Summary: Implemented splash screen with app branding, loading indicators, error states, and navigation logic
    *   Reason: Executing plan step 3
    *   Blockers: None
    *   Status: Pending Confirmation

*   2024-12-19
    *   Step: 4-8. Router integration and main app updates
    *   Modifications: Updated app_routes.dart, route_handlers.dart, app_router.dart, main.dart, and router_extensions.dart
    *   Change Summary: Integrated splash route into routing system and made it the initial route
    *   Reason: Executing plan steps 4-8
    *   Blockers: None
    *   Status: Pending Confirmation

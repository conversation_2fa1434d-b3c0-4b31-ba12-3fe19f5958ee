# Context
Filename: terms-and-conditions-implementation-task.md
Created On: 2024-12-28
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
Implement a comprehensive Terms and Conditions feature in the BuscaFarma Flutter app with the following requirements:

**Backend Integration:**
- Fetch Terms and Conditions content from the existing backend endpoint `/terms-of-service.html`
- Handle the HTML content appropriately for mobile display

**First-Time Display Logic:**
- Show Terms and Conditions modal/dialog automatically when a user logs in for the first time
- Use SharedPreferences to track whether the user has already accepted the terms
- Display should occur after successful authentication but before navigating to the main dashboard
- User must accept the terms to proceed to the app

**Profile Section Access:**
- Add a "Terms and Conditions" option in the profile section for users to view anytime
- Should open the same content in a modal dialog or dedicated screen

**Technical Requirements:**
- Follow Material Design 3 principles and existing app design patterns
- Implement proper error handling for network requests
- Use appropriate loading states while fetching content
- Ensure proper HTML rendering in Flutter (using flutter_html package)
- Follow the existing app architecture patterns and state management
- Include proper accessibility features
- Integrate with the existing authentication flow and splash screen logic

**UI/UX Considerations:**
- Mo<PERSON> should be non-dismissible on first display until user accepts
- Include "Accept" and "Decline" buttons (handle decline appropriately)
- Ensure responsive design for different screen sizes
- Follow the app's animation standards from buscafarma.mdc
- Maintain consistency with existing profile section styling

# Project Overview
BuscaFarma is a Flutter application that helps users find nearby pharmacies. The app uses:
- Flutter with Riverpod for state management
- Fluro for routing and navigation
- Firebase for authentication (Google Sign-In)
- JWT tokens for backend authentication
- Material Design 3 with dynamic color theming
- Existing authentication flow with AuthProvider and AuthRepository
- Backend API built with Elysia.js and legal document system

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## Current Application Structure
- **Authentication Flow**: Splash screen → Login page → Terms check → Main app navigation
- **Profile Section**: Card-based preferences with ListTile pattern for menu items
- **Backend API**: Legal document system with privacy policy, extensible for terms of service
- **SharedPreferences Patterns**: AnnouncementService pattern for first-time display tracking
- **HTTP Client**: Dio provider with proper configuration and error handling
- **Dialog Patterns**: AnnouncementDialog with Material Design 3 styling and animations

## Key Findings
- Backend already has legal document infrastructure that can be extended
- Existing AnnouncementService provides perfect pattern for Terms service
- Authentication flow in login_page.dart can be modified to include Terms checking
- Profile page has established pattern for adding new menu items
- HTML rendering requires flutter_html package for proper display

# Proposed Solution (Populated by INNOVATE mode)

## Architecture Approach
**Backend Extension**: Extend existing legal document system to support terms-of-service document type
**Frontend Service**: Create dedicated TermsService following AnnouncementService pattern
**UI Implementation**: Create specialized TermsDialog with HTML rendering and Accept/Decline functionality
**Integration Strategy**: Hook into authentication flow after successful login but before navigation to dashboard
**Profile Access**: Add Terms and Conditions option to profile preferences section

## Technical Implementation
- Reuse existing legal document backend infrastructure
- Follow established SharedPreferences patterns for tracking acceptance
- Implement Material Design 3 dialog with proper animations
- Use flutter_html for HTML content rendering
- Integrate with existing authentication flow seamlessly

# Implementation Plan (Generated by PLAN mode)

## Backend Changes
1. Update GetLegalDocument service to support terms-of-service content
2. Add new terms-of-service.html endpoint to legal router
3. Implement comprehensive Terms and Conditions content in Spanish

## Frontend Changes
1. Add flutter_html dependency for HTML rendering
2. Create LegalDocumentService for fetching HTML content from backend
3. Create TermsService following AnnouncementService pattern
4. Create TermsProvider for Riverpod dependency injection
5. Implement TermsDialog with Material Design 3 styling and HTML rendering
6. Update ApiConfig to support legal document endpoints
7. Add legal document failure types to error handling
8. Integrate Terms acceptance into login authentication flow
9. Add Terms and Conditions option to profile preferences section

Implementation Checklist:
1. ✅ Update backend legal document service to support terms of service content
2. ✅ Add flutter_html dependency to pubspec.yaml
3. ✅ Create LegalDocumentService for fetching HTML content from backend
4. ✅ Create TermsService for managing Terms and Conditions state and SharedPreferences
5. ✅ Create TermsProvider for Riverpod dependency injection
6. ✅ Implement TermsDialog with HTML rendering and Accept/Decline functionality
7. ✅ Update ApiConfig to support legal document endpoints
8. ✅ Add legal document failure types to error handling
9. ✅ Integrate Terms acceptance into login authentication flow
10. ✅ Add Terms and Conditions option to profile preferences section
11. 🔄 Test first-time Terms display and acceptance flow
12. 🔄 Test Terms access from profile section
13. 🔄 Test error handling for network failures and backend errors
14. 🔄 Verify proper HTML rendering and responsive design

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "Testing and validation of implemented features"

# Task Progress (Appended by EXECUTE mode after each step completion)

## 2024-12-28 - Backend Implementation
- **Step**: Update backend legal document service to support terms of service content
- **Modifications**: 
  - Updated `backend/src/legal/application/GetLegalDocument.ts` to return actual terms content instead of throwing error
  - Added comprehensive Terms and Conditions content in Spanish
  - Updated `backend/src/legal/legalRouter.ts` to add `/terms-of-service.html` endpoint
- **Change Summary**: Extended existing legal document system to support Terms and Conditions
- **Reason**: Executing plan step 1
- **Blockers**: None
- **Status**: ✅ Success

## 2024-12-28 - Frontend Dependencies
- **Step**: Add flutter_html dependency to pubspec.yaml
- **Modifications**: Added `flutter_html: ^3.0.0-beta.2` to dependencies
- **Change Summary**: Added HTML rendering capability for Terms dialog
- **Reason**: Executing plan step 2
- **Blockers**: None
- **Status**: ✅ Success

## 2024-12-28 - Core Services Implementation
- **Step**: Create core services and providers
- **Modifications**:
  - Created `buscafarma/lib/core/services/legal_document_service.dart` for HTTP requests
  - Created `buscafarma/lib/core/services/terms_service.dart` following AnnouncementService pattern
  - Created `buscafarma/lib/core/providers/terms_provider.dart` for Riverpod integration
  - Updated `buscafarma/lib/core/utils/api_config.dart` to support legal document endpoints
  - Updated `buscafarma/lib/core/error/failures.dart` to add legal document error types
- **Change Summary**: Implemented core business logic for Terms and Conditions management
- **Reason**: Executing plan steps 3-8
- **Blockers**: None
- **Status**: ✅ Success

## 2024-12-28 - UI Implementation
- **Step**: Create Terms dialog and integrate with authentication flow
- **Modifications**:
  - Created `buscafarma/lib/core/widgets/terms_dialog.dart` with Material Design 3 styling
  - Updated `buscafarma/lib/features/auth/presentation/pages/login_page.dart` to check Terms before navigation
  - Updated `buscafarma/lib/features/auth/presentation/pages/profile_page.dart` to add Terms menu option
- **Change Summary**: Implemented UI components and integrated Terms checking into app flow
- **Reason**: Executing plan steps 9-10
- **Blockers**: None
- **Status**: ✅ Success

## 2024-12-28 - Backend Testing
- **Step**: Verify backend implementation
- **Modifications**: Ran integration tests and manual endpoint testing
- **Change Summary**: Confirmed backend endpoints are working correctly
- **Reason**: Validation of backend implementation
- **Blockers**: None
- **Status**: ✅ Success

# Final Review (Populated by REVIEW mode)
*To be completed after testing phase*

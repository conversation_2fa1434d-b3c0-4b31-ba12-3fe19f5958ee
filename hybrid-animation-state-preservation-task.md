# Context
Filename: hybrid-animation-state-preservation-task.md
Created On: 2024-12-19
Created By: AI Assistant
Associated Protocol: RIPER-5 + Multidimensional + Agent Protocol

# Task Description
El problema persiste: los favoritos siguen cargándose cada vez que se cambia entre las vistas del mapa y favoritos, en lugar de cargar solo una vez y mantener el estado. El AutomaticKeepAliveClientMixin no funciona con AnimatedSwitcher porque AnimatedSwitcher reemplaza completamente el widget hijo cuando cambia la key.

La solución requerida debe:
1. **Preservar Estado**: Mantener el estado de la página de favoritos (scroll, búsqueda, datos cargados) entre cambios de tab
2. **Mantener Animaciones**: Conservar las animaciones suaves de transición entre tabs
3. **Cargar Solo Una Vez**: Los favoritos deben cargarse solo una vez (primera visita) o cuando el usuario haga refresh explícito
4. **Evitar Recreación**: Evitar que se llame initState() cada vez que se cambia de tab

# Project Overview
BuscaFarma Flutter app con:
- AnimatedSwitcher implementado para transiciones suaves entre tabs
- AutomaticKeepAliveClientMixin implementado pero no funciona con AnimatedSwitcher
- Problema: AnimatedSwitcher crea nuevas instancias de widget en cada transición
- Necesidad: Solución híbrida que combine animaciones con preservación de estado

---
*The following sections are maintained by the AI during protocol execution*
---

# Analysis (Populated by RESEARCH mode)

## Root Cause Analysis

### Por qué AutomaticKeepAliveClientMixin No Funciona
- **AnimatedSwitcher Behavior**: Reemplaza completamente el widget hijo cuando cambia la key
- **Widget Recreation**: `_getCurrentPageWidget()` crea nuevas instancias en cada cambio de tab
- **Mixin Limitation**: AutomaticKeepAliveClientMixin funciona dentro de widgets que mantienen múltiples hijos (ListView, PageView, IndexedStack), no con AnimatedSwitcher

### Current Implementation Issues
1. **Complete Widget Replacement**: AnimatedSwitcher destruye y recrea widgets completamente
2. **initState() Called Repeatedly**: Cada cambio de tab llama initState() en FavoritesPage
3. **State Loss**: Scroll position, search text, y otros estados locales se pierden
4. **Unnecessary API Calls**: loadFavorites() se ejecuta en cada recreación

### Evidence from Code Analysis
```dart
// En MainNavigationPage._getCurrentPageWidget()
case 1:
  return const FavoritesPage(key: ValueKey('favorites_page')); // Nueva instancia cada vez
```

```dart
// En FavoritesPage.initState()
WidgetsBinding.instance.addPostFrameCallback((_) {
  final favoritesNotifier = ref.read(favoritesProvider.notifier);
  favoritesNotifier.clearError();
  favoritesNotifier.loadFavorites(refresh: true); // Se ejecuta cada vez
});
```

## Technical Constraints
- Debe mantener las animaciones suaves de AnimatedSwitcher
- Debe preservar todo el estado de FavoritesPage
- Debe evitar llamadas innecesarias a la API
- Debe mantener la integración con PopScope
- Debe seguir los patrones existentes de Riverpod state management
- No debe romper la funcionalidad existente

# Proposed Solution (Populated by INNOVATE mode)

## Solution Approaches Evaluated

### Approach 1: Hybrid IndexedStack + Animation Overlay (Recommended)
Mantener IndexedStack para preservación de estado pero agregar capa de animación encima que simule transiciones de AnimatedSwitcher.

**Pros:**
- Preserva completamente el estado como IndexedStack original
- Puede lograr animaciones similares a AnimatedSwitcher
- Mínimos cambios arquitecturales
- No rompe funcionalidad existente
- Mantiene integración con PopScope

**Cons:**
- Implementación más compleja de animaciones
- Requiere coordinación cuidadosa entre IndexedStack y animaciones

### Approach 2: PageView with Custom Controller
Reemplazar con PageView que naturalmente preserva estado y agregar animaciones personalizadas.

**Pros:**
- Preservación natural de estado
- Control total sobre animaciones
- Soporte nativo para gestos de swipe

**Cons:**
- Cambio arquitectural mayor
- Puede interferir con navegación existente
- Más complejo de implementar

### Approach 3: Custom Widget Manager
Crear widget personalizado que mantenga instancias en memoria y use AnimatedSwitcher solo para efectos visuales.

**Pros:**
- Control total sobre lifecycle de widgets
- Puede mantener animaciones exactas de AnimatedSwitcher
- Preservación completa de estado

**Cons:**
- Implementación muy compleja
- Potencial para memory leaks
- Reinventando patrones existentes de Flutter

## Recommended Solution: Hybrid IndexedStack + Animation Overlay

La solución híbrida combina lo mejor de ambos mundos: preservación de estado de IndexedStack con animaciones suaves.

**Implementation Strategy:**
1. Restaurar IndexedStack para preservación de estado
2. Agregar capa de animación que simule transiciones de AnimatedSwitcher
3. Crear custom animation controller para transiciones
4. Coordinar animaciones con cambios de índice
5. Mantener toda la funcionalidad de PopScope existente

**Benefits:**
- Preservación completa de estado de widgets
- Animaciones suaves mantenidas
- Mínimos cambios arquitecturales
- No rompe funcionalidad existente
- Solución robusta y mantenible

# Implementation Plan (Generated by PLAN mode)

## File Modifications Required

### File: `lib/features/navigation/presentation/pages/main_navigation_page.dart`
**Rationale:** AnimatedSwitcher está causando recreación de widgets y pérdida de estado. La solución híbrida usará IndexedStack para mantener widgets vivos en memoria mientras agrega capa de animación para transiciones suaves.

**Technical Details:**
- Convert MainNavigationPage from ConsumerWidget to ConsumerStatefulWidget
- Add AnimationController para manejar transiciones entre tabs
- Restaurar IndexedStack para preservación de estado
- Crear animation overlay que simule transiciones de AnimatedSwitcher
- Coordinar animaciones con cambios de índice
- Mantener toda la funcionalidad de PopScope existente

**Animation Logic:**
- Slide Transitions: Mantener mismas transiciones (left/right)
- Duration: AnimationConstants.stateTransitionDuration (300ms)
- Curve: AnimationConstants.emphasizedCurve
- Coordination: Animar opacity/transform mientras IndexedStack cambia índice

**State Preservation Benefits:**
- Complete Widget Lifecycle: Widgets se crean una vez y se mantienen vivos
- No initState() Repetition: initState() solo se llama una vez por widget
- Preserved Controllers: Scroll, search, y otros controllers mantienen estado
- No Unnecessary API Calls: loadFavorites() solo se ejecuta cuando es necesario

### File: `lib/features/favorites/presentation/pages/favorites_page.dart`
**Rationale:** Remover AutomaticKeepAliveClientMixin ya que no es necesario con IndexedStack.

Implementation Checklist:
1. ✅ Convert MainNavigationPage from ConsumerWidget to ConsumerStatefulWidget
2. ✅ Add AnimationController and required animation setup
3. ✅ Replace AnimatedSwitcher with IndexedStack
4. ✅ Create animation overlay widget for slide transitions
5. ✅ Implement animation coordination logic
6. ✅ Update tab switching logic to trigger animations
7. ✅ Ensure PopScope integration remains intact
8. ✅ Remove AutomaticKeepAliveClientMixin from FavoritesPage (no longer needed)
9. Test state preservation across tab switches
10. Verify animations work smoothly
11. Test PopScope back button functionality
12. Update task progress documentation

# Current Execution Step (Updated by EXECUTE mode when starting a step)
> Currently executing: "Hybrid IndexedStack + Animation Overlay implementation"

# Task Progress (Appended by EXECUTE mode after each step completion)
*   2024-12-19
    *   Step: Hybrid IndexedStack + Animation Overlay implementation (Steps 1-8)
    *   Modifications:
        - Modified `lib/features/navigation/presentation/pages/main_navigation_page.dart`
        - Converted MainNavigationPage from ConsumerWidget to ConsumerStatefulWidget
        - Added AnimationController with TickerProviderStateMixin for smooth transitions
        - Replaced AnimatedSwitcher with IndexedStack wrapped in SlideTransition
        - Created _buildAnimatedContent() method for animation overlay
        - Implemented _animateTabChange() method for coordinating transitions
        - Updated bottom navigation onTap to trigger animations before index change
        - Updated PopScope onPopInvokedWithResult to trigger animations for back button
        - Modified `lib/features/favorites/presentation/pages/favorites_page.dart`
        - Removed AutomaticKeepAliveClientMixin (no longer needed with IndexedStack)
        - Removed super.build() call (not required without mixin)
        - Maintained all existing functionality and state management
    *   Change Summary: Successfully implemented hybrid solution combining IndexedStack state preservation with smooth slide animations
    *   Reason: Executing plan steps 1-8 for hybrid animation and state preservation solution
    *   Blockers: None
    *   Status: Pending Confirmation

{"parameters": {"api_base_url_prod": {"defaultValue": {"value": "https://backend-production-ecfc.up.railway.app/api/v1"}, "description": "Production API base URL for BuscaFarma backend services"}, "api_base_url_dev": {"defaultValue": {"value": "http://********:3000/api/v1"}, "description": "Development API base URL for Android emulator testing"}, "api_timeout_seconds": {"defaultValue": {"value": "15"}, "description": "API connection timeout duration in seconds"}, "api_receive_timeout_seconds": {"defaultValue": {"value": "15"}, "description": "API receive timeout duration in seconds"}, "search_debounce_ms": {"defaultValue": {"value": "500"}, "conditionalValues": {"search_debounce_experiment": {"value": "300"}}, "description": "Search input debounce delay in milliseconds to optimize API calls"}, "search_min_length": {"defaultValue": {"value": "2"}, "description": "Minimum number of characters required before triggering search"}, "search_max_length": {"defaultValue": {"value": "100"}, "description": "Maximum number of characters allowed in search input"}, "ads_sdk_key": {"defaultValue": {"value": "CJ6BXTWo1kigmgL6nUTb0DZFg5n_UyJCvVOqLSiJfG6yGUVPa9eSwyn0aFyx_-izg2Q-SMEu4qo1K2xRyN0z33"}, "description": "AppLovin MAX SDK key for ad monetization"}, "ads_banner_unit_id": {"defaultValue": {"value": "96f9ae17420b2c57"}, "description": "AppLovin MAX banner ad unit ID"}, "ads_interstitial_unit_id": {"defaultValue": {"value": "563c58c849ccad6d"}, "description": "AppLovin MAX interstitial ad unit ID"}, "ads_app_open_unit_id": {"defaultValue": {"value": "67f966ae4c0fc6a7"}, "description": "AppLovin MAX app open ad unit ID"}, "ads_rewarded_unit_id": {"defaultValue": {"value": "MOCK_REWARDED_AD_UNIT_ID"}, "description": "AppLovin MAX rewarded ad unit ID"}, "ads_native_unit_id": {"defaultValue": {"value": "MOCK_NATIVE_AD_UNIT_ID"}, "description": "AppLovin MAX native ad unit ID"}, "ads_interstitial_cooldown_seconds": {"defaultValue": {"value": "60"}, "conditionalValues": {"ad_frequency_conservative": {"value": "90"}, "ad_frequency_aggressive": {"value": "42"}}, "description": "Minimum time between interstitial ads in seconds"}, "ads_max_interstitials_per_session": {"defaultValue": {"value": "5"}, "conditionalValues": {"ad_frequency_conservative": {"value": "3"}, "ad_frequency_aggressive": {"value": "7"}}, "description": "Maximum number of interstitial ads allowed per user session"}, "ads_app_open_cooldown_seconds": {"defaultValue": {"value": "240"}, "conditionalValues": {"ad_frequency_conservative": {"value": "312"}, "ad_frequency_aggressive": {"value": "192"}}, "description": "Minimum time between app open ads in seconds"}, "ads_show_in_debug": {"defaultValue": {"value": "true"}, "description": "Whether to display ads in debug/development mode"}, "ads_load_timeout_seconds": {"defaultValue": {"value": "10"}, "description": "Maximum time to wait for ad loading before timeout"}, "ads_enable_adaptive_banners": {"defaultValue": {"value": "true"}, "description": "Enable adaptive banner ads that adjust to screen size"}, "ads_enable_banner_auto_refresh": {"defaultValue": {"value": "true"}, "description": "Enable automatic refresh of banner ads"}, "ads_enable_banner_preloading": {"defaultValue": {"value": "true"}, "description": "Enable preloading of banner ads for better performance"}, "animation_splash_entrance_duration_ms": {"defaultValue": {"value": "1200"}, "conditionalValues": {"animation_speed_fast": {"value": "800"}, "animation_speed_slow": {"value": "1800"}}, "description": "Splash screen entrance animation duration in milliseconds"}, "animation_login_entrance_duration_ms": {"defaultValue": {"value": "1000"}, "conditionalValues": {"animation_speed_fast": {"value": "700"}, "animation_speed_slow": {"value": "1500"}}, "description": "Login screen entrance animation duration in milliseconds"}, "animation_state_transition_duration_ms": {"defaultValue": {"value": "300"}, "conditionalValues": {"animation_speed_fast": {"value": "200"}, "animation_speed_slow": {"value": "450"}}, "description": "State transition animation duration in milliseconds"}, "animation_micro_interaction_duration_ms": {"defaultValue": {"value": "150"}, "conditionalValues": {"animation_speed_fast": {"value": "100"}, "animation_speed_slow": {"value": "225"}}, "description": "Micro-interaction animation duration in milliseconds"}, "animation_hover_duration_ms": {"defaultValue": {"value": "200"}, "conditionalValues": {"animation_speed_fast": {"value": "140"}, "animation_speed_slow": {"value": "300"}}, "description": "Hover effect animation duration in milliseconds"}, "animation_error_duration_ms": {"defaultValue": {"value": "400"}, "conditionalValues": {"animation_speed_fast": {"value": "280"}, "animation_speed_slow": {"value": "600"}}, "description": "Error animation duration in milliseconds"}, "animation_input_focus_duration_ms": {"defaultValue": {"value": "150"}, "conditionalValues": {"animation_speed_fast": {"value": "100"}, "animation_speed_slow": {"value": "225"}}, "description": "Input field focus animation duration in milliseconds"}, "animation_input_error_duration_ms": {"defaultValue": {"value": "300"}, "conditionalValues": {"animation_speed_fast": {"value": "210"}, "animation_speed_slow": {"value": "450"}}, "description": "Input field error animation duration in milliseconds"}, "review_min_session_count": {"defaultValue": {"value": "3"}, "description": "Minimum number of app sessions before showing review prompt"}, "review_min_positive_interactions": {"defaultValue": {"value": "2"}, "description": "Minimum positive interactions before showing review prompt"}, "review_min_days_since_install": {"defaultValue": {"value": "2"}, "description": "Minimum days since app installation before showing review prompt"}, "review_days_between_prompts": {"defaultValue": {"value": "30"}, "description": "Days to wait between review prompts"}, "review_days_after_dismissal": {"defaultValue": {"value": "90"}, "description": "Days to wait after user dismisses review prompt"}, "review_days_after_remind_later": {"defaultValue": {"value": "7"}, "description": "Days to wait after user selects 'remind me later'"}, "update_priority_threshold": {"defaultValue": {"value": "4"}, "description": "Update priority threshold for triggering immediate updates"}, "update_staleness_threshold": {"defaultValue": {"value": "30"}, "description": "Days of staleness before triggering immediate update"}, "update_enable_auto_check": {"defaultValue": {"value": "true"}, "description": "Enable automatic checking for app updates"}, "pagination_scroll_threshold": {"defaultValue": {"value": "200"}, "description": "Scroll threshold in pixels for triggering pagination load"}, "pagination_default_page_size": {"defaultValue": {"value": "20"}, "description": "Default number of items per page for paginated content"}, "feature_favorites_enabled": {"defaultValue": {"value": "true"}, "description": "Enable medication favorites feature"}, "feature_in_app_review_enabled": {"defaultValue": {"value": "true"}, "description": "Enable in-app review prompts"}, "feature_in_app_update_enabled": {"defaultValue": {"value": "true"}, "description": "Enable in-app update functionality"}, "feature_ads_enabled": {"defaultValue": {"value": "true"}, "description": "Enable ads monetization system"}, "feature_analytics_enabled": {"defaultValue": {"value": "true"}, "description": "Enable analytics tracking"}, "experiment_search_debounce_group_a": {"defaultValue": {"value": "500"}, "description": "Search debounce value for A/B test group A"}, "experiment_search_debounce_group_b": {"defaultValue": {"value": "300"}, "description": "Search debounce value for A/B test group B"}, "experiment_animation_speed_variant": {"defaultValue": {"value": "standard"}, "description": "Animation speed variant for A/B testing (fast/standard/slow)"}, "experiment_ad_frequency_variant": {"defaultValue": {"value": "standard"}, "description": "Ad frequency variant for A/B testing (conservative/standard/aggressive)"}}, "conditions": [{"name": "search_debounce_experiment", "expression": "percent <= 50", "tagColor": "BLUE"}, {"name": "ad_frequency_conservative", "expression": "percent <= 25", "tagColor": "GREEN"}, {"name": "ad_frequency_aggressive", "expression": "percent > 75", "tagColor": "RED"}, {"name": "animation_speed_fast", "expression": "percent <= 33", "tagColor": "PURPLE"}, {"name": "animation_speed_slow", "expression": "percent > 66", "tagColor": "ORANGE"}]}